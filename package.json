{"name": "interchat", "private": true, "version": "5.0.0", "description": "A growing Discord bot which provides inter-server chat!", "main": "build/bot/src/index.js", "license": "AGPL-3.0-only", "scripts": {"start": "node build/bot/src/index.js", "start:prod": "pm2 start .ecosystem.config.js", "dev": "nodemon --exec \"bun run build && bun start\" --ext ts,js,json --ignore build/", "test": "vitest run --reporter=verbose", "build": "tsc --build", "locale-types": "bun scripts/genLocaleTypes.js", "sync:commands": "bun scripts/syncCommands.js", "sync:emojis": "bun scripts/syncEmojis.js", "release": "release-it", "lint": "eslint --cache --fix ./src", "type-check": "tsc --noEmit", "format": "prettier --write ./src", "prepare": "husky", "db:seed": "bun prisma/seed/seed.ts", "db:generate": "bun prisma generate"}, "sponsor": {"url": "https://ko-fi.com/interchat"}, "type": "module", "dependencies": {"@hono/node-server": "^1.15.0", "@hono/zod-validator": "^0.7.0", "@prisma/client": "^6.11.1", "@sentry/node": "^9.35.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "common-tags": "^1.8.2", "discord-hybrid-sharding": "^2.2.6", "discord.js": "^14.21.0", "dotenv": "^16.6.1", "hono": "^4.8.4", "husky": "^9.1.7", "inversify": "^7.5.4", "ioredis": "^5.6.1", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "lz-string": "^1.5.0", "ms": "^2.1.3", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "zod": "^3.25.75"}, "devDependencies": {"@stylistic/eslint-plugin": "^5.1.0", "@types/common-tags": "^1.8.4", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.20", "@types/ms": "^2.1.0", "@types/supertest": "^6.0.3", "@vitest/ui": "^3.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.30.1", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.6.2", "prisma": "^6.11.1", "release-it": "^19.0.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "typescript": "5.8.3", "typescript-eslint": "^8.35.1", "vitest": "^3.2.4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.ts": ["eslint --cache --fix"]}, "imports": {"#src/*.js": "./build/*.js", "#utils/*.js": "./build/utils/*.js"}}