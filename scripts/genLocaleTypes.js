/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

// @ts-check
import yaml from "js-yaml";
import prettier from "prettier";
import {
	NewLineKind,
	NodeFlags,
	SyntaxKind,
	createPrinter,
	factory,
} from "typescript";
import { writeFile, readFile } from "node:fs/promises";

// Read the YAML file
const file = await readFile("locales/en.yml", "utf8");

// Parse the YAML content
/** @type {{ [key: string]: string }} */
// @ts-expect-error
const data = yaml.load(file);

// Generate type definitions
const typeDefinitions = generateTypes(data);

// Create the TypeScript type alias
const typeAliasDeclaration = factory.createTypeAliasDeclaration(
	[factory.createModifier(SyntaxKind.ExportKeyword)],
	"TranslationKeys",
	undefined,
	factory.createTypeLiteralNode(typeDefinitions),
);

// Create a source file and add the type alias
const sourceFile = factory.createSourceFile(
	[typeAliasDeclaration],
	factory.createToken(SyntaxKind.EndOfFileToken),
	NodeFlags.None,
);

// Print the TypeScript code to a string
const printer = createPrinter({ newLine: NewLineKind.LineFeed });
const formattedTypes = await formatWithPrettier(printer.printFile(sourceFile));
// skipcq: JS-0038
const errorLocaleKeysExtra =
	"export type ErrorLocaleKeys = Extract<keyof TranslationKeys, `errors.${string}`>;";

const output = `/*
  THIS IS AN AUTOGENERATED FILE. DO NOT EDIT IT DIRECTLY.
  To regenerate this file, run 'npm run locale-types'.
*/

${formattedTypes}
${errorLocaleKeysExtra}
`;

// Write the .d.ts file
const outputFilePath = "src/types/TranslationKeys.d.ts";
await writeFile(outputFilePath, output);

console.log(`Type definitions for locales written to ${outputFilePath}`);

/** Recursively generate type definitions from the translation data
    @param { { [key: string]: string } } obj the object with all translation data
    @returns {import('typescript').TypeElement[]}
*/
function generateTypes(obj, path = "") {
	/** @type {import('typescript').TypeElement[]} */
	const typeDefs = [];
	const keys = Object.keys(obj);

	for (const key of keys) {
		const fullPath = path ? `${path}.${key}` : key;
		if (typeof obj[key] === "object" && obj[key] !== null) {
			typeDefs.push(...generateTypes(obj[key], fullPath));
			continue;
		}

		const regex = /{([^}]+)}/g;
		const variables = [...obj[key].matchAll(regex)].map(
			(match) => `'${match[1]}'`,
		);
		const variablesType =
			variables.length !== 0 ? variables.join(" | ") : "never";

		typeDefs.push(
			factory.createPropertySignature(
				undefined,
				factory.createStringLiteral(fullPath),
				undefined,
				factory.createTypeReferenceNode(variablesType),
			),
		);
	}

	return typeDefs;
}

/**
 *
 * @param {string} values
 */
async function formatWithPrettier(values) {
	const configFile = await prettier.resolveConfigFile();
	if (!configFile) return values;

	const config = await prettier.resolveConfig(configFile);
	const formatted = await prettier.format(values, {
		...config,
		parser: "typescript",
	});

	return formatted;
}
