#!/usr/bin/env node

/**
 * Hub Activity Level Update Script
 * Updates hub activity levels based on actual hub metrics
 */

import { PrismaClient } from '../dashboard/src/lib/generated/prisma/client/index.js';

const db = new PrismaClient();

async function main() {
  console.log(`[${new Date().toISOString()}] Starting hub activity level updates...`);

  try {
    // Get all hubs with their activity metrics
    const hubs = await db.hub.findMany({
      include: {
        _count: {
          select: {
            connections: { where: { connected: true } },
            messages: true,
            upvotes: true,
            reviews: true,
          },
        },
      },
    });

    console.log(`📊 Processing ${hubs.length} hubs...`);

    let updatedCount = 0;
    const activityChanges = {
      LOW: 0,
      MEDIUM: 0,
      HIGH: 0,
    };

    for (const hub of hubs) {
      const newActivityLevel = calculateHubActivityLevel(hub);

      if (newActivityLevel !== hub.activityLevel) {
        await db.hub.update({
          where: { id: hub.id },
          data: { activityLevel: newActivityLevel },
        });

        activityChanges[newActivityLevel]++;
        updatedCount++;

        console.log(`Updated ${hub.name}: ${hub.activityLevel} → ${newActivityLevel}`);
      }
    }

    console.log(`✅ Updated ${updatedCount} hubs' activity levels:`);
    console.log(`   LOW: ${activityChanges.LOW} hubs`);
    console.log(`   MEDIUM: ${activityChanges.MEDIUM} hubs`);
    console.log(`   HIGH: ${activityChanges.HIGH} hubs`);

    console.log('✅ Hub activity level updates completed successfully');
  } catch (error) {
    console.error('❌ Hub activity level update failed:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

function calculateHubActivityLevel(hub) {
  const connections = hub._count?.connections || 0;
  const messages = hub._count?.messages || 0;
  const upvotes = hub._count?.upvotes || 0;
  const reviews = hub._count?.reviews || 0;

  // Calculate recent activity (last 7 days)
  const now = new Date();
  const recentActivityWindow = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const isRecentlyActive = hub.lastActive && hub.lastActive > recentActivityWindow;

  // Enhanced activity score calculation
  const connectionScore = connections; // 1 point per connection
  const messageScore = Math.min(messages / 10, 50); // Max 50 points from messages
  const upvoteScore = upvotes * 2; // 2 points per upvote
  const reviewScore = reviews * 3; // 3 points per review
  const recentActivityBonus = isRecentlyActive ? 20 : 0; // 20 point bonus for recent activity

  const totalActivity = connectionScore + messageScore + upvoteScore + reviewScore + recentActivityBonus;

  // Determine activity level based on score
  if (totalActivity >= 100) {
    return 'HIGH';
  } else if (totalActivity >= 30) {
    return 'MEDIUM';
  } else {
    return 'LOW';
  }
}

main();
