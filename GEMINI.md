# Gemini CLI Interaction Notes

This document summarizes key information and decisions made during interactions with the Gemini CLI agent.

## Development Guidelines
*   When making any changes, always review related files and adhere to existing codebase conventions. Do not invent new patterns or styles.

## Gemini Added Memories
* talk like a discord user, informal and to the point
* localization t() function exists in src/locales/Locale.ts file
* Do not modify the Unused '@ts-expect-error' directive in src/auth.ts.
* Keep updating GEMINI.md file with important details of the conversation that you think need remembering, or if user asks you to remember something.

### Project Structure & Deployment

* The project consists of two main parts:
  * **Bot (`interchat`):** Deployed on a VPS.
  * **Dashboard (`dashboard`):** A separate submodule, deployed independently on Vercel.
* **Crucial Note:** The bot and dashboard are entirely separate deployments and **cannot share files directly**. Any logic requiring shared data (e.g., donation tier definitions) must be implemented independently or via database synchronization.

### Donation System (Tier Implementation)

* **Tiers:** The new donation system is based on one tiers: Supporter. It is said to expand soon.
* **Bot-side Implementation:**
  * `src/lib/donations/tiers/`: New directory created for tier definitions (`Tiers.ts`, `TierTypes.ts`).
  * `src/lib/donations/core/DonationManager.ts`: Rewritten to process donations based on the new tier system, replacing old perk and media premium logic.
  * `src/lib/donations/core/PremiumService.ts`: Rewritten to check user tiers from the database instead of old premium flags.
  * `src/lib/donations/schemas/donation.ts`: Updated to remove old perk-related schemas and incorporate `DonationTier`.
  * `src/lib/donations/index.ts`: Updated to reflect new module structure.
  * Old files removed: `src/lib/donations/services/DonorPerkService.ts`, `src/lib/donations/types/PremiumTypes.ts`, `src/lib/donations/README.md`, `src/lib/donations/scripts/seedDonorPerks.ts`, and the `src/lib/donations/scripts` directory.
* **Database Schema (`prisma/schema.prisma`):**
  * The `prisma/schema.prisma` file for **both the bot and the dashboard** must be kept identical and in sync.
  * Changes include:
    * Removal of `DonorPerk`, `UserDonorPerk`, and `DonorPerkType` models.
    * Addition of `DonationTier` enum.
    * Updates to the `User` model (added `donationTier`, `donationExpiresAt`) and `Donation` model (removed `isSubscription`, `isFirstPayment`, `tierName`).
  * Schema synchronization was performed using `npx prisma db push --accept-data-loss` for both the bot and the dashboard (though the dashboard push was cancelled by the user).
* **Dashboard-side Logic (`dashboard/src/app/api/admin/process-unlinked-donations/route.ts`):**
  * This file was reverted to its original state after realizing the file-sharing constraint.
  * **Future Action Required:** This file needs to be updated to infer donation tiers based on the `amount` of the donation, as it cannot directly import the `Tiers` object from the bot. It must contain its own logic for tier determination.

### Locale File Information

The locale file `src/utils/Locale.ts` is responsible for loading and managing translations within the application.

* The `t` function is the main translation utility, which takes a phrase key, locale, and optional variables to replace placeholders in the translated text. It also logs a warning if a translation key is not found.
* If information is needed, feel free to read the file.

### Bot Features

InterChat is a Discord bot designed for real-time cross-server communication and community management. Its key features include:

#### Core Communication
* **Hub System**: Create or join themed communities where multiple Discord servers can connect and communicate.
  * **Hub Creation/Management**: Commands to create, delete, edit, rename, and transfer ownership of hubs.
  * **Hub Configuration**: Set up anti-swear, appeal cooldowns, logging, rules, welcome messages, and NSFW status for hubs.
  * **Hub Visibility**: Control the visibility of hubs.
  * **Hub Servers Listing**: View all servers connected to a specific hub.
* **Call Feature (Userphone)**: Establish quick, temporary one-to-one connections between individual servers for direct communication.
  * **Call Management**: Commands to initiate, hang up, and skip calls.
* **Real-time Messaging**: Messages are instantly relayed across all connected servers within a hub or during a call.
* **Rich Media Support**: Share images, embeds, and reactions seamlessly across connected servers.

#### Management & Moderation
* **Blacklist Management**: Blacklist specific users or servers from participating in hubs.
  * **Unblacklist**: Remove users or servers from the blacklist.
* **Moderation Tools**:
  * **Warn System**: Issue warnings to users within a hub.
  * **Infraction Tracking**: View infractions for hubs and users.
  * **Ban System**: Ban and unban users and servers.
  * **Message Management**: Delete and edit messages.
  * **Reporting System**: Users can report inappropriate messages or calls.
  * **Mod Panel**: A centralized interface for moderation actions.
* **Content Filtering**:
  * **Smart Content Filtering**: Automatically blocks inappropriate content.
  * **Anti-Spam Protection**: Helps maintain clean and relevant conversations.
  * **Anti-Swear Configuration**: Configure anti-swear settings for hubs.
* **Comprehensive Logging**: Track cross-server activity and moderation actions.
* **Hub Moderator Management**: Add, edit, list, and remove moderators for hubs.

#### Configuration & Customization
* **General Configuration**:
  * **Language Settings**: Set the bot's language for your server.
  * **Reply Mentions**: Configure how the bot handles mentions in replies.
  * **Invite Settings**: Set custom invite links.
* **Custom Welcome Messages**: Personalize the experience for new members joining a hub.
* **Hub Rules**: Define and enforce specific guidelines for your community within a hub.
* **Flexible Permissions**: Control who can perform actions within your hubs.
* **Badges**: Manage user badges.

#### Information & Utility
* **Bot Information**: Access details about the bot, including statistics and an "about" section.
* **User Profiles**: View personal user profiles, including achievements.
* **Leaderboards**: Track and display leaderboards for achievements, calls, messages, and votes.
* **Donation System**: Information about donating to support the bot and donor listings.
* **Help & Support**: Access a comprehensive help command, tutorial, and links to the support server.
* **Voting**: Vote for the bot on top.gg and unlock perks.
* **Connection Management**: Pause, unpause, list, and edit connections.
* **Message Information**: Retrieve detailed information about messages.
* **Setup Wizard**: A guided process to help new servers set up the bot.

#### Staff & Developer Tools
* **Debugging Tools**: Commands for debugging and fixing server-related issues.
* **Developer Tools**: Send alerts and other developer-specific functionalities.
* **Find Users/Servers**: Locate specific users or servers.
* **Reclustering**: Recluster the bot's shards.
* **View Reported Calls**: Review reported calls for moderation.
* **Leave Server**: Command for the bot to leave a specified server.
