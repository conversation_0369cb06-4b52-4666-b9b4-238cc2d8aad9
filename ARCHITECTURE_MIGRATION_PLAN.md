# InterChat Architecture Migration Plan

## Executive Summary

We are transforming the InterChat Discord bot from a traditional monolithic structure to a **Clean Architecture** pattern with **Domain-Driven Design** principles. This migration will create a more maintainable, scalable, and robust codebase that can handle the complexities of a multi-sharded Discord bot with clustering.

**Key Migration Date**: Started July 7, 2025
**Estimated Completion**: 7 weeks (by August 25, 2025)
**Current Status**: ✅ Phase 1 Complete! Moving to Phase 2 - Core Domain Migration 🚀

---

## Critical Architecture Considerations

### 🔄 Sharding & Clustering Architecture

**IMPORTANT**: InterChat uses Discord's sharding system with clustering where **each cluster runs in a separate process**. This has significant implications for our new architecture:

#### Current Sharding Setup
- **Shards**: Discord connection instances that handle specific guilds
- **Clusters**: Groups of shards running in separate Node.js processes
- **Process Isolation**: Each cluster process is completely isolated
- **Inter-Process Communication**: Clusters communicate via IPC/Redis

#### Migration Implications
1. **Shared State Management**: Must use Redis/Database for cross-cluster state
2. **Event Broadcasting**: Events must be published across all cluster processes
3. **Dependency Injection**: Each cluster process needs its own DI container
4. **Configuration**: Each process loads its own configuration
5. **Database Connections**: Each cluster maintains its own connection pool
6. **Memory Isolation**: No shared in-memory state between clusters

#### Architectural Adaptations Required
```typescript
// Each cluster process will have:
ClusterProcess {
  ├── Own DI Container
  ├── Own Database Connection Pool
  ├── Own Event Bus (with Redis backend for cross-cluster events)
  ├── Own Configuration Instance
  ├── Shard-specific Command Handlers
  └── Inter-Cluster Communication Layer
}
```

---

## Why This Migration?

### Current Problems
- **Tight Coupling**: Commands, services, and data access are mixed
- **Testing Difficulty**: Hard to unit test individual components
- **Scalability Issues**: Adding features requires touching multiple files
- **Maintenance Burden**: Complex dependencies make debugging difficult
- **Cluster Complexity**: Current architecture doesn't account for multi-process nature

### Expected Benefits
- **Clean Separation**: Clear boundaries between business logic, data, and presentation
- **Testability**: Easy to mock dependencies and test in isolation
- **Scalability**: Simple to add new features following established patterns
- **Maintainability**: Clear structure makes onboarding and debugging easier
- **Cluster-Aware**: Architecture designed for multi-process distributed system

---

## Target Architecture Overview

### Clean Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │    Discord      │ │      REST       │ │   WebSocket     ││
│  │   Commands      │ │      API        │ │   Handlers      ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION LAYER                         │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Use Cases     │ │   Services      │ │   Middleware    ││
│  │  (Commands)     │ │  (Application)  │ │   (Validation)  ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     DOMAIN LAYER                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │    Entities     │ │     Services    │ │  Value Objects  ││
│  │  (Hub, User)    │ │   (Business)    │ │   (UserID)      ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 INFRASTRUCTURE LAYER                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │    Database     │ │      Redis      │ │   External      ││
│  │  (Repositories) │ │   (Caching)     │ │     APIs        ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### Multi-Cluster Aware Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        CLUSTER 1                            │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Clean Architecture Stack                   ││
│  │  [Presentation → Application → Domain → Infrastructure] ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                        ┌─────────────┐
                        │    REDIS    │ ← Cross-Cluster State
                        │  EVENT BUS  │ ← Event Broadcasting
                        │  DATABASE   │ ← Shared Persistence
                        └─────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        CLUSTER 2                            │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Clean Architecture Stack                   ││
│  │  [Presentation → Application → Domain → Infrastructure] ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

---

## Detailed Migration Phases

### Phase 1: Foundation Setup (Week 1) ⏳
**Goal**: Establish new architecture alongside existing code

#### 1.1 Directory Structure ✅
```
packages/
├── bot/                          # Main bot package
│   ├── src/
│   │   ├── application/          # Use cases, app services
│   │   │   ├── commands/         # Command handlers
│   │   │   ├── events/          # Event handlers
│   │   │   ├── services/        # Application services
│   │   │   └── middleware/      # Cross-cutting concerns
│   │   ├── domain/              # Business logic
│   │   │   ├── entities/        # Domain entities
│   │   │   ├── repositories/    # Repository interfaces
│   │   │   ├── services/        # Domain services
│   │   │   └── value-objects/   # Value objects
│   │   ├── infrastructure/      # External concerns
│   │   │   ├── database/        # Database implementations
│   │   │   ├── external/        # External API clients
│   │   │   ├── config/          # Configuration
│   │   │   └── logging/         # Logging setup
│   │   ├── presentation/        # Interface layer
│   │   │   ├── controllers/     # HTTP controllers
│   │   │   ├── dto/             # Data transfer objects
│   │   │   ├── validators/      # Input validation
│   │   │   └── formatters/      # Output formatting
│   │   └── shared/              # Shared utilities
│   │       ├── constants/       # Application constants
│   │       ├── types/           # Type definitions
│   │       ├── utils/           # Utility functions
│   │       └── errors/          # Error definitions
│   └── tests/                   # Test suites
├── shared/                      # Cross-package shared code
└── dashboard/                   # Web dashboard (existing)
```

#### 1.2 Core Dependencies ✅
- `inversify` - Dependency Injection for each cluster
- `reflect-metadata` - Required for DI decorators
- `zod` - Configuration and data validation
- `class-validator` - DTO validation
- `class-transformer` - Object transformation

#### 1.3 Infrastructure Setup 🔄
- [x] DI Container types
- [x] Base error classes
- [x] Configuration system
- [ ] Event Bus (Redis-backed for clusters)
- [ ] Database connection abstraction
- [ ] Logging infrastructure

### Phase 2: Core Domain Migration (Week 2)
**Goal**: Migrate core business logic to domain layer

#### 2.1 Donation System ✨
Your existing donation system is well-structured! We'll adapt it:

```typescript
// Current: src/lib/donations/
// Future: packages/bot/src/domain/donations/

Domain Layer:
├── entities/
│   ├── Donation.ts              # Donation aggregate
│   ├── DonationTier.ts          # Premium tiers
│   └── User.ts                  # User with premium status
├── services/
│   ├── DonationManager.ts       # Business logic
│   └── PremiumService.ts        # Premium verification
├── repositories/
│   └── IDonationRepository.ts   # Data access interface
└── value-objects/
    ├── Amount.ts                # Money value object
    └── TierLevel.ts             # Tier enumeration
```

#### 2.2 Hub System
```typescript
Domain Layer:
├── entities/
│   ├── Hub.ts                   # Hub aggregate root
│   ├── Server.ts                # Connected servers
│   └── Message.ts               # Cross-hub messages
├── services/
│   ├── HubService.ts            # Hub management
│   └── MessageRoutingService.ts # Message distribution
└── repositories/
    ├── IHubRepository.ts
    └── IMessageRepository.ts
```

#### 2.3 Cluster-Aware Adaptations
- **Event Publishing**: Use Redis pub/sub for cross-cluster events
- **State Synchronization**: Cache premium status in Redis
- **Process Isolation**: Each cluster has independent domain instances

### Phase 3: Command System Overhaul (Week 3)
**Goal**: Migrate Discord commands to clean architecture

#### 3.1 New Command Pattern
```typescript
// Before:
export class CreateHubCommand extends BaseCommand {
  // Tightly coupled to Discord.js
}

// After:
@injectable()
export class CreateHubUseCase {
  constructor(
    @inject(TYPES.HubService) private hubService: HubService,
    @inject(TYPES.EventBus) private eventBus: IEventBus
  ) {}

  async execute(command: CreateHubCommand): Promise<CreateHubResult> {
    // Pure business logic, testable
  }
}

export class CreateHubCommandHandler {
  constructor(
    @inject(TYPES.CreateHubUseCase) private useCase: CreateHubUseCase
  ) {}

  async handle(interaction: ChatInputCommandInteraction): Promise<void> {
    // Discord-specific presentation logic
  }
}
```

#### 3.2 Cross-Cluster Command Handling
- **Shard Awareness**: Commands route to appropriate shard's cluster
- **State Consistency**: Use distributed locks for cross-cluster operations
- **Response Coordination**: Ensure consistent responses across clusters

### Phase 4: Testing Infrastructure (Week 4)
**Goal**: Comprehensive test coverage

#### 4.1 Test Strategy
```typescript
├── tests/
│   ├── unit/                    # Isolated unit tests
│   │   ├── domain/              # Domain logic tests
│   │   ├── application/         # Use case tests
│   │   └── infrastructure/      # Infrastructure tests
│   ├── integration/             # Cross-layer tests
│   │   ├── commands/            # End-to-end command tests
│   │   ├── events/              # Event flow tests
│   │   └── clusters/            # Multi-cluster tests
│   └── fixtures/                # Test data and mocks
```

#### 4.2 Cluster Testing
- **Multi-Process Testing**: Test cluster communication
- **Redis Integration**: Test cross-cluster event broadcasting
- **Shard Simulation**: Mock different shard scenarios

### Phase 5: Advanced Features (Week 5)
**Goal**: Enhanced observability and performance

#### 5.1 Cluster-Aware Caching
```typescript
interface ICacheManager {
  // Local cache within cluster
  getLocal<T>(key: string): Promise<T | null>;
  setLocal<T>(key: string, value: T, ttl?: number): Promise<void>;

  // Distributed cache across clusters
  getDistributed<T>(key: string): Promise<T | null>;
  setDistributed<T>(key: string, value: T, ttl?: number): Promise<void>;

  // Cache invalidation across all clusters
  invalidateAcrossClusters(pattern: string): Promise<void>;
}
```

#### 5.2 Monitoring & Observability
- **Per-Cluster Metrics**: Monitor each cluster independently
- **Cross-Cluster Health**: Overall system health monitoring
- **Distributed Tracing**: Track requests across clusters

### Phase 6: Dashboard Integration (Week 6)
**Goal**: Integrate dashboard with new architecture

#### 6.1 API Gateway
- **Cluster Aggregation**: Aggregate data from all clusters
- **Load Balancing**: Distribute API requests across clusters
- **Real-time Updates**: WebSocket connections to all clusters

### Phase 7: Legacy Migration (Week 7)
**Goal**: Complete migration and cleanup

#### 7.1 Gradual Rollout
- **Feature Flags**: Toggle between old and new implementations
- **Cluster-by-Cluster**: Migrate one cluster at a time
- **Rollback Strategy**: Ability to revert if issues arise

---

## Cluster-Specific Implementation Details

### Event Bus Architecture
```typescript
// Each cluster process
class ClusterEventBus implements IEventBus {
  private localBus: InMemoryEventBus;      // For intra-cluster events
  private distributedBus: RedisEventBus;   // For cross-cluster events

  async publish<T extends DomainEvent>(event: T): Promise<void> {
    // Publish locally first
    await this.localBus.publish(event);

    // Then broadcast to other clusters if needed
    if (event.shouldBroadcast) {
      await this.distributedBus.publish(event);
    }
  }
}
```

### Database Per Cluster
```typescript
// Each cluster maintains its own connection pool
class ClusterDatabaseManager {
  private pool: DatabasePool;

  constructor(
    private config: Configuration,
    private clusterId: string
  ) {
    this.pool = createPool({
      ...config.get('database'),
      applicationName: `interchat-cluster-${clusterId}`,
      max: config.get('database').pool.max / totalClusters
    });
  }
}
```

### Configuration Per Cluster
```typescript
// Cluster-specific configuration
class ClusterConfiguration extends Configuration {
  constructor(private clusterId: string) {
    super();
  }

  getClusterSpecificConfig(): ClusterConfig {
    return {
      clusterId: this.clusterId,
      shardIds: this.getShardIdsForCluster(),
      totalShards: this.getTotalShards(),
      totalClusters: this.getTotalClusters(),
    };
  }
}
```

---

## Risk Mitigation for Clusters

### Data Consistency
- **Problem**: Multiple clusters modifying same data
- **Solution**: Distributed locks using Redis
- **Implementation**: Optimistic locking with version numbers

### Event Ordering
- **Problem**: Events processed out of order across clusters
- **Solution**: Event sequencing and deduplication
- **Implementation**: Event IDs and timestamp-based ordering

### Cluster Failures
- **Problem**: One cluster goes down
- **Solution**: Graceful degradation and failover
- **Implementation**: Health checks and automatic restarts

### Memory Leaks
- **Problem**: Long-running cluster processes accumulating memory
- **Solution**: Regular memory monitoring and restarts
- **Implementation**: Memory usage alerts and scheduled restarts

---

## Development Workflow

### Setting Up Local Development
1. **Single Cluster Mode**: For development, run single cluster
2. **Multi-Cluster Testing**: Use Docker Compose for testing
3. **Redis Local**: Local Redis instance for event bus testing
4. **Database Isolation**: Separate test databases per cluster

### Testing Clusters
```bash
# Start multiple cluster processes for testing
bun run start:cluster:0  # Cluster 0
bun run start:cluster:1  # Cluster 1
bun run test:clusters    # Test cross-cluster communication
```

### Monitoring Clusters
```bash
# Monitor all clusters
bun run monitor:all

# Monitor specific cluster
bun run monitor:cluster:0

# Check cluster health
bun run health:check
```

---

## Success Metrics

### Technical Metrics
- [ ] **Zero Downtime Migration**: No service interruption during migration
- [ ] **90%+ Test Coverage**: Comprehensive testing across all layers
- [ ] **<100ms Command Response**: Faster than current implementation
- [ ] **Memory Efficiency**: 20% reduction in memory usage per cluster
- [ ] **Error Rate**: <0.1% error rate across all clusters

### Architectural Metrics
- [ ] **Dependency Inversion**: All dependencies point inward
- [ ] **Testability**: All components mockable and testable
- [ ] **Separation of Concerns**: Clear layer boundaries
- [ ] **Cluster Independence**: Each cluster can operate independently

### Developer Experience
- [ ] **Onboarding Time**: New developers productive in <2 days
- [ ] **Feature Development**: 50% faster to add new features
- [ ] **Bug Resolution**: 60% faster to identify and fix issues
- [ ] **Code Review**: Clearer review process with established patterns

---

## Next Steps (Immediate Actions)

### Today (July 7, 2025)
1. ✅ Create directory structure
2. ✅ Install core dependencies
3. ✅ Set up DI types
4. ✅ Create error classes
5. ✅ Build configuration system
6. 🔄 **NEXT**: Create cluster-aware Event Bus
7. 🔄 **NEXT**: Set up database abstraction

### This Week
- Complete Phase 1 foundation
- Begin migrating donation system
- Set up basic testing infrastructure
- Document cluster communication patterns

### This Month
- Complete Phases 1-3
- Have working cluster-aware architecture
- Migrate core command system
- Establish testing patterns

---

*This document serves as the complete migration plan and should be updated as we progress. Each cluster process will implement this architecture independently while coordinating through shared infrastructure.*

**Last Updated**: July 7, 2025
**Migration Status**: Phase 1 - Foundation Setup (In Progress)
**Next Milestone**: Complete Event Bus and Database Layer
