{
  "compilerOptions": {
    "target": "esnext",
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "allowSyntheticDefaultImports": true,
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmitOnError": true,
    "declaration": true,
    "baseUrl": ".",
    "rootDir": "packages",
    "outDir": "build",
    "esModuleInterop": true,
    "removeComments": true,
    "strict": true,
    "inlineSourceMap": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": false,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "exactOptionalPropertyTypes": false,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "typeRoots": [
      "node_modules/@types",
      "../../node_modules/@types"
    ],
    "paths": {
      "#src/*": ["./src/*"],
      "#utils/*": ["./src/utils/*"],
      "#types/*.d.ts": ["src/types/*.d.ts"]
    }
  },
  "include": ["packages/src/**/*.json", "packages/**/src/**/*.ts"],
  "exclude": ["node_modules", "build", "**/*.test.ts", "**/tests/**/*"],
  "packageManager": "bun@1.2.1"
}
