{"title": "InterChat - Comprehensive Monitoring Dashboard", "id": null, "uid": "interchat-main", "tags": ["discord", "bot", "interchat", "monitoring"], "timezone": "browser", "schemaVersion": 36, "version": 2, "refresh": "30s", "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": [{"name": "cluster", "type": "query", "query": "label_values(interchat_guilds_total, cluster)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "hub", "type": "query", "query": "label_values(interchat_hub_activity, hub_name)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}]}, "panels": [{"id": 1, "type": "row", "title": "🎯 Executive Summary", "collapsed": false, "gridPos": {"x": 0, "y": 0, "w": 24, "h": 1}}, {"id": 2, "title": "Active Users (24h)", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 1, "w": 4, "h": 4}, "options": {"reduceOptions": {"values": false, "calcs": ["last"]}, "colorMode": "background", "graphMode": "area", "justifyMode": "center"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "red"}, {"value": 100, "color": "yellow"}, {"value": 500, "color": "green"}]}, "unit": "short"}}, "targets": [{"expr": "interchat_user_engagement{metric_type=\"active_users\", time_period=\"24h\"}", "legendFormat": "Active Users"}]}, {"id": 3, "title": "Messages/Hour", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 4, "y": 1, "w": 4, "h": 4}, "options": {"reduceOptions": {"values": false, "calcs": ["last"]}, "colorMode": "background", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "red"}, {"value": 50, "color": "yellow"}, {"value": 200, "color": "green"}]}, "unit": "short"}}, "targets": [{"expr": "sum(rate(interchat_messages_total[1h])) * 3600", "legendFormat": "Messages/Hour"}]}, {"id": 4, "title": "Total Servers", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 8, "y": 1, "w": 4, "h": 4}, "options": {"reduceOptions": {"values": false, "calcs": ["last"]}, "colorMode": "background", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "blue"}]}, "unit": "short"}}, "targets": [{"expr": "sum(interchat_guilds_total)", "legendFormat": "Servers"}]}, {"id": 5, "title": "Active Hubs", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 1, "w": 4, "h": 4}, "options": {"reduceOptions": {"values": false, "calcs": ["last"]}, "colorMode": "background", "graphMode": "area"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "purple"}]}, "unit": "short"}}, "targets": [{"expr": "interchat_hubs_total", "legendFormat": "<PERSON><PERSON>"}]}, {"id": 6, "title": "System Health", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 16, "y": 1, "w": 4, "h": 4}, "options": {"reduceOptions": {"values": false, "calcs": ["last"]}, "colorMode": "background", "textMode": "value_and_name"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "red"}, {"value": 0.8, "color": "yellow"}, {"value": 0.95, "color": "green"}]}, "unit": "percentunit", "max": 1, "min": 0}}, "targets": [{"expr": "avg(interchat_shards_status)", "legendFormat": "Uptime"}]}, {"id": 7, "title": "Error Rate", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 20, "y": 1, "w": 4, "h": 4}, "options": {"reduceOptions": {"values": false, "calcs": ["last"]}, "colorMode": "background"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "green"}, {"value": 0.01, "color": "yellow"}, {"value": 0.05, "color": "red"}]}, "unit": "percentunit"}}, "targets": [{"expr": "sum(rate(interchat_errors_total[5m])) / sum(rate(interchat_api_requests_total[5m]))", "legendFormat": "Error Rate"}]}, {"id": 8, "type": "row", "title": "📊 User Engagement & Growth", "collapsed": false, "gridPos": {"x": 0, "y": 5, "w": 24, "h": 1}}, {"id": 9, "title": "User Activity Trends", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 6, "w": 12, "h": 8}, "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "smooth", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "opacity", "showPoints": "never", "stacking": {"mode": "none"}}, "color": {"mode": "palette-classic"}, "unit": "short"}}, "targets": [{"expr": "interchat_user_engagement{metric_type=\"active_users\", time_period=\"1h\"}", "legendFormat": "Active Users (1h)"}, {"expr": "interchat_user_engagement{metric_type=\"active_users\", time_period=\"24h\"}", "legendFormat": "Active Users (24h)"}, {"expr": "interchat_user_engagement{metric_type=\"new_users\", time_period=\"24h\"}", "legendFormat": "New Users (24h)"}]}, {"id": 10, "title": "Command Usage Distribution", "type": "piechart", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 6, "w": 12, "h": 8}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"]}, "pieType": "donut", "tooltip": {"mode": "single"}, "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}, "displayLabels": ["name", "percent"]}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "unit": "short"}}, "targets": [{"expr": "topk(10, sum by (command_name) (increase(interchat_command_usage_total[24h])))", "legendFormat": "{{command_name}}"}]}, {"id": 11, "type": "row", "title": "🌐 Hub Ecosystem Health", "collapsed": false, "gridPos": {"x": 0, "y": 14, "w": 24, "h": 1}}, {"id": 12, "title": "Hub Activity Heatmap", "type": "heatmap", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 15, "w": 24, "h": 8}, "options": {"calculate": false, "cellGap": 2, "cellValues": {"unit": "short"}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "spectrum", "reverse": false, "scale": "exponential", "scheme": "Spectral", "steps": 64}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true}, "rowsFrame": {"layout": "auto"}, "tooltip": {"show": true, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "short"}}, "targets": [{"expr": "sum by (hub_name) (rate(interchat_messages_total[5m]))", "legendFormat": "{{hub_name}}"}]}, {"id": 13, "type": "row", "title": "⚡ Performance & System Health", "collapsed": false, "gridPos": {"x": 0, "y": 23, "w": 24, "h": 1}}, {"id": 14, "title": "Response Time Distribution", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 24, "w": 12, "h": 8}, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "smooth", "lineWidth": 1, "fillOpacity": 20, "gradientMode": "opacity", "showPoints": "never"}, "color": {"mode": "palette-classic"}, "unit": "s"}}, "targets": [{"expr": "histogram_quantile(0.50, sum(rate(interchat_response_time_seconds_bucket[5m])) by (le, operation_type))", "legendFormat": "{{operation_type}} (p50)"}, {"expr": "histogram_quantile(0.95, sum(rate(interchat_response_time_seconds_bucket[5m])) by (le, operation_type))", "legendFormat": "{{operation_type}} (p95)"}, {"expr": "histogram_quantile(0.99, sum(rate(interchat_response_time_seconds_bucket[5m])) by (le, operation_type))", "legendFormat": "{{operation_type}} (p99)"}]}, {"id": 15, "title": "System Resource Usage", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 24, "w": 12, "h": 8}, "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "desc"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "smooth", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "opacity"}, "color": {"mode": "palette-classic"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Usage"}, "properties": [{"id": "unit", "value": "percent"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Usage"}, "properties": [{"id": "unit", "value": "bytes"}]}]}, "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "CPU Usage"}, {"expr": "process_resident_memory_bytes", "legendFormat": "Memory Usage"}, {"expr": "sum(interchat_cluster_memory_mb) by (cluster)", "legendFormat": "Cluster {{cluster}} Memory"}]}, {"id": 16, "type": "row", "title": "🔧 Operational Insights", "collapsed": false, "gridPos": {"x": 0, "y": 32, "w": 24, "h": 1}}, {"id": 17, "title": "Top Active Hubs", "type": "table", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 33, "w": 12, "h": 8}, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Messages/Hour"}]}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}, "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "green"}, {"value": 100, "color": "yellow"}, {"value": 500, "color": "red"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Messages/Hour"}, "properties": [{"id": "unit", "value": "short"}, {"id": "custom.displayMode", "value": "gradient-gauge"}, {"id": "max", "value": 1000}]}]}, "targets": [{"expr": "topk(10, sum by (hub_name) (rate(interchat_messages_total[1h])) * 3600)", "legendFormat": "{{hub_name}}", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"Value": "Messages/Hour", "hub_name": "<PERSON>b Name"}}}]}, {"id": 18, "title": "Error Rate by Component", "type": "bargauge", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 33, "w": 12, "h": 8}, "options": {"orientation": "horizontal", "displayMode": "gradient", "showUnfilled": true}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "green"}, {"value": 0.01, "color": "yellow"}, {"value": 0.05, "color": "red"}]}, "unit": "percentunit", "max": 0.1}}, "targets": [{"expr": "sum by (component) (rate(interchat_errors_total[5m])) / sum by (component) (rate(interchat_api_requests_total[5m]))", "legendFormat": "{{component}}"}]}], "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "links": [], "liveNow": false, "style": "dark"}