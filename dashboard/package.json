{"name": "my-app", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "type-check": "tsc --noEmit", "lint": "next lint", "format": "prettier --write .", "postinstall": "fumadocs-mdx && prisma generate", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "analyze": "ANALYZE=true next build", "optimize-build": "node scripts/optimize-build.js", "deploy:optimized": "opennextjs-cloudflare build && node scripts/optimize-build.js && opennextjs-cloudflare deploy", "deploy:paid": "opennextjs-cloudflare build && node scripts/optimize-build.js && wrangler deploy .open-next/worker.js --name interchat"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@discordjs/rest": "^2.5.1", "@prisma/client": "^6.11.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-virtual": "^3.13.12", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@types/canvas-confetti": "^1.9.0", "@types/ioredis": "^5.0.0", "@uploadthing/react": "^7.3.2", "canvas-confetti": "^1.9.3", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "discord-api-types": "^0.38.15", "fumadocs-core": "^15.6.1", "fumadocs-mdx": "^11.6.10", "fumadocs-ui": "^15.6.1", "ioredis": "^5.6.1", "lodash": "^4.17.21", "lucide-react": "0.525.0", "motion": "^12.23.0", "next": "^15.3.5", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "superjson": "^2.2.2", "uploadthing": "^7.7.3", "zod": "^3.25.75", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@next/bundle-analyzer": "^15.3.5", "@tailwindcss/postcss": "^4.1.11", "@types/lodash": "^4.17.20", "@types/mdx": "^2.0.13", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-window": "^1.8.8", "esbuild": "^0.25.5", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "postcss": "^8.5.6", "prisma": "^6.11.1", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1"}}