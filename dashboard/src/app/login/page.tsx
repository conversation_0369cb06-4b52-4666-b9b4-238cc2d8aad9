import { But<PERSON> } from "@/components/ui/button";
import { signIn } from "@/auth";
import { DiscordIcon } from "@/components/DiscordIcon";

export default async function LoginPage(props: {
  searchParams: Promise<{ callbackUrl?: string }>;
}) {
  const { callbackUrl } = await props.searchParams;

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="mx-auto max-w-sm space-y-6 p-6">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Welcome to InterChat</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Sign in with Discord to continue
          </p>
        </div>
        <form
          action={async () => {
            "use server";
            await signIn("discord", { redirectTo: callbackUrl || "/" });
          }}
        >
          <Button
            className="w-full cursor-pointer bg-gradient-to-r from-primary to-primary-alt hover:opacity-90 text-white font-medium shadow-md shadow-primary/20 dark:shadow-md dark:shadow-primary/10"
            size="lg"
          >
            <DiscordIcon className="h-5 w-5 fill-white" />
            Sign in with Discord
          </Button>
        </form>
      </div>
    </div>
  );
}
