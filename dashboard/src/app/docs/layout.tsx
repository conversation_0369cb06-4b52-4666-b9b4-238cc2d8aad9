import { DocsLayout } from "fumadocs-ui/layouts/docs";
import type { ReactNode } from "react";
import { source } from "@/lib/source";
import { RootProvider } from "fumadocs-ui/provider";
import { BaseLayoutProps } from "fumadocs-ui/layouts/shared";
import Image from "next/image";

export const metadata = {
  title: "InterChat Docs",
  description: "InterChat documentation",
  openGraph: {
    title: "InterChat Docs",
    description: "InterChat documentation",
    type: "website",
    images: [
      {
        url: "/InterChatLogo.png",
        width: 300,
        height: 300,
        alt: "InterChat Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "InterChat Docs",
    description: "InterChat documentation",
    images: ["/InterChatLogo.png"],
  },
};

const baseOptions: BaseLayoutProps = {
  themeSwitch: {
    enabled: false,
  },
  nav: {
    title: (
      <>
        {<Image alt="InterChat Logo" src="/interchat.png" height={30} width={30} />}
        <span className="font-medium [.uwu_&]:hidden [header_&]:text-[15px]">
          InterChat
        </span>
      </>
    ),
  },
  githubUrl: "https://github.com/interchatapp/InterChat",
};

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <RootProvider>
      <div className="relative min-h-screen">
        {/* Content */}
        <div className="relative">
          <DocsLayout tree={source.pageTree} {...baseOptions}>
            {children}
          </DocsLayout>
        </div>
      </div>
    </RootProvider>
  );
}
