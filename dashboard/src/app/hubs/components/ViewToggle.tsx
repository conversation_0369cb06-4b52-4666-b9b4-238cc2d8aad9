"use client";

import { But<PERSON> } from "@/components/ui/button";
import { LayoutGrid, List } from "lucide-react";
import { useEffect, useState } from "react";

export type ViewMode = "grid" | "list";

interface ViewToggleProps {
  onChange: (mode: ViewMode) => void;
  initialMode?: ViewMode;
}

export function ViewToggle({ onChange, initialMode = "grid" }: ViewToggleProps) {
  const [viewMode, setViewMode] = useState<ViewMode>(initialMode);

  // Load saved preference from localStorage on mount
  useEffect(() => {
    const savedMode = localStorage.getItem("hubViewMode") as ViewMode | null;
    if (savedMode) {
      setViewMode(savedMode);
      onChange(savedMode);
    }
  }, [onChange]);

  const handleViewChange = (mode: ViewMode) => {
    setViewMode(mode);
    localStorage.setItem("hubViewMode", mode);
    onChange(mode);
  };

  return (
    <div className="flex items-center gap-1 bg-gray-800/50 rounded-lg p-0.5 border border-gray-700/50">
      <Button
        variant="ghost"
        size="sm"
        className={`px-2 py-1 h-8 ${
          viewMode === "grid"
            ? "bg-gray-700 text-white"
            : "text-gray-400 hover:text-white hover:bg-gray-700/50"
        }`}
        onClick={() => handleViewChange("grid")}
        aria-label="Grid view"
        aria-pressed={viewMode === "grid"}
      >
        <LayoutGrid className="h-4 w-4 mr-1" />
        <span className="text-xs">Grid</span>
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={`px-2 py-1 h-8 ${
          viewMode === "list"
            ? "bg-gray-700 text-white"
            : "text-gray-400 hover:text-white hover:bg-gray-700/50"
        }`}
        onClick={() => handleViewChange("list")}
        aria-label="List view"
        aria-pressed={viewMode === "list"}
      >
        <List className="h-4 w-4 mr-1" />
        <span className="text-xs">List</span>
      </Button>
    </div>
  );
}
