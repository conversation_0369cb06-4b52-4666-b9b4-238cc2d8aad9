"use client";

import { Card } from "@/components/ui/card";
import { ScrollText } from "lucide-react";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

export default function RulesSection({ hub }: { hub: SimplifiedHub }) {
  return (
    <Card className="border-fd-border bg-fd-card dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
      <div className="p-8">
        <div className="prose dark:prose-invert max-w-none mb-8">
          <h2 className="text-2xl font-semibold mb-4 border-b border-fd-border pb-3 flex items-center gap-2">
            <ScrollText className="h-6 w-6 text-primary" />
            Hub Rules
          </h2>

          {hub.rules && hub.rules.length > 0 ? (
            <div className="divide-y divide-fd-border">
              {hub.rules.map((rule, index) => (
                <div
                  key={index}
                  className="flex gap-4 py-3 hover:bg-primary/5 transition-colors"
                >
                  <span className="text-primary font-medium w-6 text-right flex-shrink-0">
                    {index + 1}.
                  </span>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        // Disable block-level elements
                        h1: "span",
                        h2: "span",
                        h3: "span",
                        h4: "span",
                        h5: "span",
                        h6: "span",
                        // Convert paragraphs to spans with line break preservation
                        p: ({ children }) => (
                          <span className="whitespace-pre-wrap">{children}</span>
                        ),
                        // Style inline elements
                        strong: ({ children }) => (
                          <strong className="font-bold">{children}</strong>
                        ),
                        em: ({ children }) => (
                          <em className="italic">{children}</em>
                        ),
                        u: ({ children }) => (
                          <u className="underline">{children}</u>
                        ),
                        code: ({ children }) => (
                          <code className="bg-primary/10 rounded px-1 py-0.5">
                            {children}
                          </code>
                        ),
                        // Disable block elements we don't want
                        pre: "span",
                        // Make links open in new tab
                        a: ({ children, href }) => (
                          <a
                            href={href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:text-primary/90 transition-colors"
                          >
                            {children}
                          </a>
                        ),
                      }}
                    >
                      {rule}
                    </ReactMarkdown>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
              <ScrollText className="h-4 w-4" />
              No specific rules have been set for this hub yet.
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
