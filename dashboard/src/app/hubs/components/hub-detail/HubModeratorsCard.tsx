import { Shield } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

interface HubModeratorsCardProps {
  moderators: Array<{
    id: string;
    role: string | null;
    user: { name: string | null; image: string | null; id: string } | null;
  }> | null;
}

const HubModeratorsCard: React.FC<HubModeratorsCardProps> = ({ moderators }) => {
  return (
    <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
      <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
        <Shield className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
        Moderators
      </h3>
      {moderators && moderators.length > 0 ? (
        <div className="space-y-3">
          {moderators.map((mod) => (
            <div
              key={mod.id}
              className="flex items-center gap-3 rounded-md p-2 transition-colors hover:bg-gray-800/50"
            >
              <div className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-800 border border-gray-700/50">
                <Image
                  src={mod.user?.image || '/default-avatar.png'}
                  alt={mod.user?.name || 'User Avatar'}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
              <div className="min-w-0 flex-1">
                <p className="truncate text-xs sm:text-sm font-medium text-gray-200">
                  {mod.user?.name}
                </p>
                <p className="truncate text-xs text-gray-400">
                  {mod.role || 'Moderator'}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="py-3 text-center text-sm text-gray-400">No moderators listed</p>
      )}
    </div>
  );
};

export default HubModeratorsCard;
