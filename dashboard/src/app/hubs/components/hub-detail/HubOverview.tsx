import React from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import remarkGfm from 'remark-gfm';

interface HubOverviewProps {
  description: string | null;
}

const HubOverview: React.FC<HubOverviewProps> = ({ description }) => {
  return (
    <div className="prose prose-invert max-w-none prose-p:text-gray-300 prose-headings:text-white prose-a:text-primary hover:prose-a:text-primary-alt prose-img:rounded-lg prose-img:shadow-lg">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw, rehypeSanitize]}
      >
        {description || 'No description provided.'}
      </ReactMarkdown>
    </div>
  );
};

export default HubOverview;
