"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Info, ScrollText } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import RulesSection from "./RulesSection";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export default function MainContent({ hub }: { hub: SimplifiedHub }) {
  const { description } = hub;

  return (
    <div className="lg:col-span-2 space-y-6">
      <Tabs defaultValue="about" className="w-full">
        <div className="relative overflow-x-auto no-scrollbar mb-4">
          <TabsList className="bg-gray-900/50 border border-gray-800 min-w-max w-full">
            <TabsTrigger
              value="about"
              className="data-[state=active]:bg-gray-800 px-3 sm:px-4 py-2 text-xs sm:text-sm flex-shrink-0"
            >
              <Info className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
              <span className="truncate">About</span>
            </TabsTrigger>
            <TabsTrigger
              value="rules"
              className="data-[state=active]:bg-gray-800 px-3 sm:px-4 py-2 text-xs sm:text-sm flex-shrink-0"
            >
              <ScrollText className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 flex-shrink-0" />
              <span className="truncate">Rules</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="about" className="mt-6">
          <Card className="bg-[#0f1117] border-gray-800">
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-4">About this hub</h3>
              <div className="prose prose-invert max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {description}
                </ReactMarkdown>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="rules" className="mt-6">
          <RulesSection hub={hub} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
