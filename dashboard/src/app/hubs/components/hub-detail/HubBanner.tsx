import Image from 'next/image';
import React from 'react';

interface HubBannerProps {
  bannerUrl: string | null;
  name: string;
}

const HubBanner: React.FC<HubBannerProps> = ({ bannerUrl, name }) => {
  const bannerContent = bannerUrl ? (
    <div className="relative h-full w-full">
      <Image
        src={bannerUrl}
        alt={`${name} banner`}
        fill
        className="object-cover"
        priority
      />
      {/* Enhanced gradient overlay with animated particles */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent" />
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.1]" />
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-gray-950 to-transparent" />

      {/* Optional visual elements - subtle animated orbs */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl opacity-30 animate-pulse" />
      <div
        className="absolute top-1/2 right-1/3 w-48 h-48 bg-purple-600/10 rounded-full blur-3xl opacity-20 animate-pulse"
        style={{ animationDelay: '1s' }}
      />
    </div>
  ) : (
    // Fallback banner with enhanced design
    <div className="relative h-full w-full bg-gradient-to-br from-gray-900 via-gray-950 to-black overflow-hidden">
      {/* Animated grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.15]" />

      {/* Enhanced glow effects */}
      <div className="absolute top-1/4 left-1/5 w-72 h-72 bg-primary/15 rounded-full blur-3xl opacity-50 animate-pulse" />
      <div
        className="absolute bottom-1/4 right-1/6 w-96 h-96 bg-purple-500/15 rounded-full blur-3xl opacity-40 animate-pulse"
        style={{ animationDelay: '2s' }}
      />
      <div
        className="absolute top-1/2 left-1/2 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl opacity-30 animate-pulse"
        style={{ animationDelay: '3s' }}
      />

      {/* Optional floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/30 rounded-full animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDuration: `${Math.random() * 10 + 10}s`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>
    </div>
  );

  return (
    <div className="relative h-[320px] md:h-[400px] w-full overflow-hidden">
      {bannerContent}
    </div>
  );
};

export default HubBanner;
