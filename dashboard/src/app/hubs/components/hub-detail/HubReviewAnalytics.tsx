import { cn } from '@/lib/utils';
import { Star } from 'lucide-react';
import React from 'react';

interface ReviewStats {
  total: number;
  average: number;
  distribution: Array<{ rating: number; count: number; percentage: number }>;
}

interface HubReviewAnalyticsProps {
  reviewStats: ReviewStats;
}

const HubReviewAnalytics: React.FC<HubReviewAnalyticsProps> = ({ reviewStats }) => {
  return (
    <div className="flex flex-col md:flex-row gap-6 mb-8">
      <div className="bg-gray-800/40 rounded-xl p-5 flex-1 border border-gray-700/40">
        <div className="flex items-end gap-2 mb-2">
          <span className="text-4xl font-bold text-white">
            {reviewStats.average.toFixed(1)}
          </span>
          <div className="flex items-center text-amber-400 mb-1">
            {Array.from({ length: 5 }).map((_, i) => (
              <Star
                key={i}
                className={cn(
                  'h-4 w-4',
                  i < Math.round(reviewStats.average)
                    ? 'fill-amber-400'
                    : 'fill-gray-700',
                )}
              />
            ))}
          </div>
        </div>
        <p className="text-sm text-gray-400">
          {reviewStats.total} review
          {reviewStats.total !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Rating distribution */}
      <div className="bg-gray-800/40 rounded-xl p-5 flex-1 border border-gray-700/40">
        <div className="space-y-2">
          {reviewStats.distribution.map((item) => (
            <div key={item.rating} className="flex items-center gap-3">
              <div className="flex items-center gap-1 w-12">
                <span className="text-sm font-medium text-gray-300">{item.rating}</span>
                <Star className="h-3 w-3 text-amber-400 fill-amber-400" />
              </div>
              <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-amber-500 to-amber-400"
                  style={{ width: `${item.percentage}%` }}
                />
              </div>
              <span className="text-xs font-medium text-gray-400 w-10 text-right">
                {item.percentage}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HubReviewAnalytics;
