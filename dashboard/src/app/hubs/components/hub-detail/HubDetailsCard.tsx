import { formatDistanceToNow } from 'date-fns';
import { Calendar, Heart, MessageSquare, Users } from 'lucide-react';
import React from 'react';

interface HubDetailsCardProps {
  formattedDate: string;
  hub: {
    lastActive: Date | null;
    upvotes: Array<{ id: string; userId: string; }>;
  };
  connections: Array<{ id: string; serverId: string; connected: boolean; compact: boolean; createdAt: Date; lastActive: Date; server: { id: string; name: string | null; }; }>;
}

const HubDetailsCard: React.FC<HubDetailsCardProps> = ({ formattedDate, hub, connections }) => {
  return (
    <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
      <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
        <Calendar className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
        Hub Details
      </h3>
      <dl className="space-y-2 sm:space-y-3">
        {/* Created Date */}
        <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
          <dt className="flex items-center text-sm text-gray-400">
            <Calendar className="h-4 w-4 mr-2 text-primary/80" />
            Created
          </dt>
          <dd className="text-sm font-medium text-gray-200">{formattedDate}</dd>
        </div>

        {/* Last Message */}
        <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
          <dt className="flex items-center text-sm text-gray-400">
            <MessageSquare className="h-4 w-4 mr-2 text-primary/80" />
            Last Message
          </dt>
          <dd className="text-sm font-medium text-gray-200">
            {hub.lastActive
              ? formatDistanceToNow(hub.lastActive, {
                  addSuffix: true,
                })
              : 'No activity yet'}
          </dd>
        </div>

        {/* Connected Servers */}
        <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
          <dt className="flex items-center text-sm text-gray-400">
            <Users className="h-4 w-4 mr-2 text-primary/80" />
            Connected Servers
          </dt>
          <dd className="text-sm font-medium text-gray-200">{connections.length}</dd>
        </div>

        {/* Upvotes */}
        <div className="flex items-center justify-between rounded-md bg-gray-800/40 p-3 border border-gray-700/30 hover:bg-gray-800/60 transition-colors">
          <dt className="flex items-center text-sm text-gray-400">
            <Heart className="h-4 w-4 mr-2 text-rose-500/80" />
            Upvotes
          </dt>
          <dd className="text-sm font-medium text-gray-200">{hub.upvotes.length}</dd>
        </div>
      </dl>
    </div>
  );
};

export default HubDetailsCard;
