"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { Heart, Share2 } from "lucide-react";
import Image from "next/image";
import { useHubUpvote } from "../../hooks/use-hub-upvote";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import type React from "react";

const JOIN_HUB_COMMAND_PREFIX = "/connect hub:";

export default function BannerSection({ hub }: { hub: SimplifiedHub }) {
  const { liked, handleUpvote } = useHubUpvote(hub.id, hub.upvotes);

  const joinHubCommand = `${JOIN_HUB_COMMAND_PREFIX}${hub.name}`;

  const handleCopyCommand = async () => {
    try {
      await navigator.clipboard.writeText(joinHubCommand);
      toast({
        title: "Command copied!",
        description: "Paste this command in your Discord server",
        className: "bg-green-100 border border-green-400 text-green-700",
      });
    } catch {
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  // Determine what to display in the banner area
  let bannerContent: React.ReactNode;

  if (hub.bannerUrl) {
    bannerContent = (
      <>
        <Image
          src={hub.bannerUrl}
          alt={`${hub.name} banner`}
          fill
          className="object-cover"
          priority
          unoptimized
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      </>
    );
  } else {
    bannerContent = (
      <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-primary/20 via-primary/10 to-transparent">
        <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-15" />
        <div className="absolute top-1/4 left-1/5 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/6 w-96 h-96 bg-primary-alt/20 rounded-full blur-3xl" />
        {hub.iconUrl ? (
          <div className="relative w-32 h-32 rounded-full border-4 border-background shadow-lg z-10">
            <Image
              src={hub.iconUrl}
              alt={hub.name}
              fill
              className="object-cover rounded-full"
              unoptimized
            />
          </div>
        ) : (
          <div className="flex items-center justify-center w-32 h-32 text-6xl font-bold rounded-full bg-primary/20 text-primary z-10">
            {hub.name.charAt(0).toUpperCase()}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="mb-6 w-full">
      <div className="overflow-hidden rounded-xl border border-border bg-gray-950 shadow-lg">
        <div className="relative h-64 w-full md:h-80">
          {bannerContent}

          <div className="absolute bottom-0 left-0 right-0 flex flex-col items-start justify-between gap-4 p-4 sm:flex-row sm:items-end sm:gap-6 sm:p-6 z-10">
            <div className="flex flex-1 items-start gap-4 sm:items-center">
              {hub.iconUrl && hub.bannerUrl && (
                <div className="hidden sm:block">
                  <div className="relative h-20 w-20 overflow-hidden rounded-full border-4 border-background shadow-lg">
                    <Image
                      src={hub.iconUrl}
                      alt={hub.name}
                      fill
                      className="object-cover rounded-full"
                      unoptimized
                    />
                  </div>
                </div>
              )}
              <div className="min-w-0 flex-1">
                <h1 className="mb-2 flex items-center gap-2 text-2xl font-bold text-white md:text-3xl">
                  {hub.name}
                </h1>
                <p className="max-w-2xl line-clamp-2 text-sm text-white/80">
                  {hub.description}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/10 hover:bg-white/20 text-white cursor-pointer"
                onClick={handleUpvote}
              >
                <Heart
                  className={cn(
                    "h-4 w-4 mr-2",
                    liked ? "fill-red-500 text-red-500" : "text-white",
                  )}
                />
                {liked ? "Upvoted" : "Upvote"}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/10 hover:bg-white/20 text-white"
                onClick={handleCopyCommand}
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
