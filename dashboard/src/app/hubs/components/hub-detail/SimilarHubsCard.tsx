import { Button } from '@/components/ui/button';
import { Tag } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

const SimilarHubsCard = () => {
  return (
    <div className="rounded-xl border border-gray-800/70 bg-gray-900/60 p-4 sm:p-6 shadow-lg backdrop-blur-md">
      <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-5 flex items-center">
        <Tag className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
        Similar Hubs
      </h3>

      <div className="space-y-3">
        {/* Placeholder for similar hubs - would be dynamically populated */}
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-800/40 transition-colors"
          >
            <div className="h-10 w-10 rounded-lg overflow-hidden border border-gray-700/50">
              <Image
                src={`https://api.dicebear.com/7.x/shapes/svg?seed=hub${i}`}
                alt={`Similar Hub ${i}`}
                width={40}
                height={40}
                className="object-cover"
                unoptimized
              />
            </div>
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm font-medium text-gray-200">
                {['Gaming Central', 'Anime Hangout', 'Tech Community'][i - 1]}
              </p>
              <p className="truncate text-xs text-gray-400">
                {[24, 18, 32][i - 1]} servers
              </p>
            </div>
          </div>
        ))}

        <Button className="w-full mt-2 py-2 px-4 bg-gray-800/60 hover:bg-gray-700/60 text-white text-sm font-medium rounded-lg border border-gray-700/30 transition-colors">
          Explore More Hubs
        </Button>
      </div>
    </div>
  );
};

export default SimilarHubsCard;
