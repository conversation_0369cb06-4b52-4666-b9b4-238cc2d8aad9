import { formatDistanceToNow } from 'date-fns';
import { Clock, Users } from 'lucide-react';
import React from 'react';

interface HubConnectedServersProps {
  connections: { id: string; lastActive: Date; server: { name: string | null } }[]; // Replace with actual type if available
}

const HubConnectedServers: React.FC<HubConnectedServersProps> = ({ connections }) => {
  return (
    <>
      {connections.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {connections.slice(0, 10).map((conn) => (
            <div
              key={conn.id}
              className="flex items-center gap-4 rounded-lg border border-gray-800/60 bg-gray-800/30 p-4 hover:bg-gray-800/50 transition-colors duration-200"
            >
              <div className="h-12 w-12 rounded-lg overflow-hidden border border-gray-700/50">
                <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                  <Users className="h-6 w-6 text-gray-300" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-white truncate">
                  {conn.server?.name || 'Discord Server'}
                </h4>
                <p className="text-xs text-gray-400 flex items-center mt-1">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDistanceToNow(new Date(conn.lastActive), {
                    addSuffix: true,
                  })}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-10 text-gray-400">
          <Users className="h-12 w-12 text-gray-500 mb-3 opacity-50" />
          <p className="text-center text-gray-400">No servers connected yet</p>
          <p className="text-center text-gray-500 text-sm mt-1">
            Be the first to connect your Discord server!
          </p>
        </div>
      )}
    </>
  );
};

export default HubConnectedServers;
