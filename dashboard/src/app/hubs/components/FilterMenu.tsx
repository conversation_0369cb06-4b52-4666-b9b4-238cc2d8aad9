"use client";

import { useState } from "react";
import { Filter, X, Globe, Shield, Languages } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { ContentFilter, VerificationStatus, LANGUAGES, REGIONS } from "@/app/hubs/constants";

interface EnhancedFilterMenuProps {
  contentFilter: ContentFilter;
  verificationStatus: VerificationStatus;
  language?: string;
  region?: string;
  minServers?: number;
  maxServers?: number;
  onFilterChange: (filterType: string, value: ContentFilter | VerificationStatus | string | number | undefined) => void;
  onClearFilters: () => void;
}

export function FilterMenu({
  contentFilter,
  verificationStatus,
  language,
  region,
  minServers,
  maxServers,
  onFilterChange,
  onClearFilters,
}: EnhancedFilterMenuProps) {
  const [serverRange, setServerRange] = useState<[number, number]>([
    minServers || 0,
    maxServers || 300,
  ]);

  // Count active filters
  const activeFilterCount = [
    contentFilter !== ContentFilter.All ? 1 : 0,
    verificationStatus !== VerificationStatus.All ? 1 : 0,
    language && language !== "all" ? 1 : 0,
    region && region !== "all" ? 1 : 0,
    minServers !== undefined || maxServers !== undefined ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  // Handle server range change
  const handleServerRangeChange = (values: number[]) => {
    setServerRange([values[0], values[1]]);
  };

  // Apply server range filter
  const applyServerRange = () => {
    onFilterChange("minServers", serverRange[0]);
    onFilterChange("maxServers", serverRange[1]);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="h-14 border border-gray-700/50 bg-gray-800/50 text-white hover:bg-gray-700/50 hover:border-gray-600 shadow-lg shadow-primary/5 transition-all duration-200 rounded-xl"
        >
          <div className="relative mr-2">
            <Filter className="w-5 h-5 relative z-10" />
            {activeFilterCount > 0 && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-pulse" />
            )}
          </div>
          <span className="hidden sm:inline">Filters</span>
          {activeFilterCount > 0 && (
            <Badge className="ml-1 bg-primary text-white">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 bg-gray-900 border-gray-700 shadow-xl shadow-black/20 z-[9999]" style={{ zIndex: 9999 }} sideOffset={5}>
        <DropdownMenuLabel className="text-sm font-medium">Filter Options</DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Content Filter */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-xs text-gray-400 font-normal">
            Content Type
          </DropdownMenuLabel>
          <DropdownMenuRadioGroup
            value={contentFilter}
            onValueChange={(value) =>
              onFilterChange("contentFilter", value as ContentFilter)
            }
          >
            <DropdownMenuRadioItem value={ContentFilter.All} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              All Content
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem value={ContentFilter.SFW} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              SFW Only
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem value={ContentFilter.NSFW} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              NSFW Allowed
            </DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-700 my-2" />

        {/* Verification Status */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-xs text-gray-400 font-normal">
            Verification
          </DropdownMenuLabel>
          <DropdownMenuRadioGroup
            value={verificationStatus}
            onValueChange={(value) =>
              onFilterChange("verificationStatus", value as VerificationStatus)
            }
          >
            <DropdownMenuRadioItem value={VerificationStatus.All} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              All Hubs
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem value={VerificationStatus.Verified} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              <Shield className="w-4 h-4 mr-2 text-blue-400" />
              Verified Only
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem value={VerificationStatus.Partnered} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              <Shield className="w-4 h-4 mr-2 text-purple-400" />
              Partnered Only
            </DropdownMenuRadioItem>
            <DropdownMenuRadioItem value={VerificationStatus.VerifiedOrPartnered} className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
              <Shield className="w-4 h-4 mr-2 text-green-400" />
              Verified or Partnered
            </DropdownMenuRadioItem>
          </DropdownMenuRadioGroup>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-700 my-2" />

        {/* Language Submenu */}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
            <Languages className="w-4 h-4 mr-2 text-gray-400" />
            <span>Language</span>
            {language && language !== "all" && (
              <Badge className="ml-auto bg-primary/20 text-primary text-xs">
                {LANGUAGES.find(lang => lang.code === language)?.name || language}
              </Badge>
            )}
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="bg-gray-900 border-gray-700 max-h-[300px] overflow-y-auto z-[9999]" style={{ zIndex: 9999 }} sideOffset={8}>
              <DropdownMenuRadioGroup
                value={language || "all"}
                onValueChange={(value) => onFilterChange("language", value === "all" ? undefined : value)}
              >
                <DropdownMenuRadioItem value="all" className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
                  All Languages
                </DropdownMenuRadioItem>
                {LANGUAGES.map((lang) => (
                  <DropdownMenuRadioItem
                    key={lang.code}
                    value={lang.code}
                    className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
                  >
                    {lang.name}
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>

        {/* Region Submenu */}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
            <Globe className="w-4 h-4 mr-2 text-gray-400" />
            <span>Region</span>
            {region && region !== "all" && (
              <Badge className="ml-auto bg-primary/20 text-primary text-xs">
                {REGIONS.find(reg => reg.code === region)?.name || region}
              </Badge>
            )}
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="bg-gray-900 border-gray-700 z-[9999]" style={{ zIndex: 9999 }} sideOffset={8}>
              <DropdownMenuRadioGroup
                value={region || "all"}
                onValueChange={(value) => onFilterChange("region", value === "all" ? undefined : value)}
              >
                <DropdownMenuRadioItem value="all" className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800">
                  All Regions
                </DropdownMenuRadioItem>
                {REGIONS.map((reg) => (
                  <DropdownMenuRadioItem
                    key={reg.code}
                    value={reg.code}
                    className="cursor-pointer text-sm hover:bg-gray-800 focus:bg-gray-800"
                  >
                    {reg.name}
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>

        <DropdownMenuSeparator className="bg-gray-700 my-2" />

        {/* Server Range Slider */}
        <div className="px-2 py-3">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-gray-400">Server Count</span>
            <div className="text-xs text-gray-300">
              {serverRange[0]} - {serverRange[1] === 300 ? "∞" : serverRange[1]}
            </div>
          </div>
          <Slider
            defaultValue={[serverRange[0], serverRange[1]]}
            max={300}
            step={5}
            value={[serverRange[0], serverRange[1]]}
            onValueChange={handleServerRangeChange}
            onValueCommit={applyServerRange}
            className="my-4"
          />
        </div>

        <DropdownMenuSeparator className="bg-gray-700" />

        {/* Clear Filters Button */}
        <div className="p-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="w-full text-gray-400 hover:text-white cursor-pointer flex items-center justify-center"
          >
            <X className="w-4 h-4 mr-2" />
            Clear all filters
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
