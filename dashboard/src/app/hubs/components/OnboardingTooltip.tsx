"use client";

import { NSFWAgeVerificationModal } from "@/components/hubs/NSFWAgeVerificationModal";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useNSFWPreference } from "@/hooks/useNSFWPreference";
import { <PERSON>ertTriangle, ArrowRight, HelpCircle, Shield, X } from "lucide-react";
import { useEffect, useState } from "react";

interface OnboardingStep {
  title: string;
  description: string;
  position: "top" | "bottom" | "left" | "right";
  element?: string;
  isNsfwStep?: boolean;
}

const onboardingSteps: OnboardingStep[] = [
  {
    title: "Welcome to InterChat Hubs!",
    description: "Discover and join active Discord chatrooms that connect multiple servers together.",
    position: "bottom",
  },
  {
    title: "Content Preferences",
    description: "Set your content preferences to customize your hub discovery experience.",
    position: "bottom",
    isNsfwStep: true,
  },
  {
    title: "Search for Hubs",
    description: "Use the search bar to find hubs by name, description, or browse by popular tags.",
    position: "bottom",
    element: "search-form",
  },
  {
    title: "Explore Hub Cards",
    description: "Each card shows a hub's details, member count, and upvotes. Click to view more or join.",
    position: "right",
    element: "hub-results",
  },
  {
    title: "Join a Hub",
    description: "Connect your Discord server to a hub to chat with other communities.",
    position: "bottom",
  },
];

export function OnboardingTooltip() {
  const [showTooltip, setShowTooltip] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [showAgeVerification, setShowAgeVerification] = useState(false);
  const [tempNsfwPreference, setTempNsfwPreference] = useState(false);

  // Session and NSFW preference hooks
  const {
    showNsfwHubs,
    isLoading: nsfwLoading,
    isUpdating: nsfwUpdating,
    isAuthenticated,
    updateNSFWPreference
  } = useNSFWPreference();

  useEffect(() => {
    // Check if this is the user's first visit
    const hasSeenOnboarding = localStorage.getItem("hasSeenHubsOnboarding");
    if (!hasSeenOnboarding) {
      // Show tooltip after a short delay
      const timer = setTimeout(() => setShowTooltip(true), 1500);
      return () => clearTimeout(timer);
    }
  }, []);

  // Initialize temp NSFW preference with current user preference
  useEffect(() => {
    if (!nsfwLoading) {
      setTempNsfwPreference(showNsfwHubs);
    }
  }, [showNsfwHubs, nsfwLoading]);

  // Handle NSFW preference toggle
  const handleNsfwToggle = async (enabled: boolean) => {
    setTempNsfwPreference(enabled);

    if (!isAuthenticated) {
      // For non-authenticated users, just update the temp state
      // They'll see a message about signing in to save permanently
      return;
    }

    if (enabled && !showNsfwHubs) {
      // Show age verification modal when enabling NSFW
      setShowAgeVerification(true);
    } else {
      // Directly update preference when disabling or already enabled
      await updateNSFWPreference(enabled);
    }
  };

  // Handle age verification confirmation
  const handleAgeVerificationConfirm = async () => {
    const success = await updateNSFWPreference(true);
    if (success) {
      setTempNsfwPreference(true);
    }
    setShowAgeVerification(false);
  };

  // Handle age verification cancel
  const handleAgeVerificationCancel = () => {
    setTempNsfwPreference(false);
    setShowAgeVerification(false);
  };

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleClose();
    }
  };

  const handleClose = () => {
    setShowTooltip(false);
    localStorage.setItem("hasSeenHubsOnboarding", "true");
  };

  if (!showTooltip) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="fixed bottom-4 right-4 z-50 bg-primary/10 hover:bg-primary/20 text-primary rounded-full h-10 w-10"
        onClick={() => setShowTooltip(true)}
        aria-label="Show help"
      >
        <HelpCircle className="h-5 w-5" />
      </Button>
    );
  }

  const step = onboardingSteps[currentStep];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <Card className={`w-full mx-4 border-primary/20 bg-gray-900 text-white shadow-xl ${
        step.isNsfwStep ? 'max-w-lg' : 'max-w-md'
      }`}>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl text-primary">{step.title}</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 rounded-full text-gray-400 hover:text-white"
              aria-label="Close onboarding"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription className="text-gray-400">
            Step {currentStep + 1} of {onboardingSteps.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-200 mb-4">{step.description}</p>

          {/* NSFW Preference Step Content */}
          {step.isNsfwStep && (
            <div className="space-y-4">
              {/* NSFW Content Toggle */}
              <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center">
                      {tempNsfwPreference ? (
                        <AlertTriangle className="w-4 h-4 text-white" />
                      ) : (
                        <Shield className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div>
                      <label className="text-sm font-medium text-white cursor-pointer">
                        Show NSFW Content
                      </label>
                      <p className="text-xs text-gray-400">
                        NSFW hubs may contain mature themes and explicit content
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={tempNsfwPreference}
                    onCheckedChange={handleNsfwToggle}
                    disabled={nsfwUpdating}
                    className="data-[state=checked]:bg-orange-600"
                  />
                </div>

                {/* Age requirement notice */}
                <div className="bg-blue-500/10 rounded-lg p-3 border border-blue-500/20">
                  <div className="flex items-start gap-2">
                    <Shield className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-blue-200">
                      <strong>Age Requirement:</strong> You must be 18+ to enable NSFW content
                    </p>
                  </div>
                </div>

                {/* Authentication notice for non-logged-in users */}
                {!isAuthenticated && (
                  <div className="bg-yellow-500/10 rounded-lg p-3 border border-yellow-500/20 mt-3">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-yellow-200">
                        <strong>Sign in required:</strong> To save this preference permanently, please sign in to your account
                      </p>
                    </div>
                  </div>
                )}

                {/* Current preference confirmation for authenticated users */}
                {isAuthenticated && tempNsfwPreference && (
                  <div className="bg-orange-500/10 rounded-lg p-3 border border-orange-500/20 mt-3">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-orange-200">
                        NSFW content will be shown in your hub discovery results
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t border-gray-800 pt-4">
          <Button
            variant="ghost"
            onClick={handleClose}
            className="text-gray-400 hover:text-white"
          >
            Skip
          </Button>
          <Button onClick={handleNext} className="bg-primary hover:bg-primary/90">
            {currentStep < onboardingSteps.length - 1 ? (
              <>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              "Get Started"
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* NSFW Age Verification Modal */}
      <NSFWAgeVerificationModal
        isOpen={showAgeVerification}
        onClose={handleAgeVerificationCancel}
        onConfirm={handleAgeVerificationConfirm}
        isLoading={nsfwUpdating}
      />
    </div>
  );
}
