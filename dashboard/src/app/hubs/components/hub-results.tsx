"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, RefreshCw } from "lucide-react";
import { HubCard } from "@/components/hubs/HubCard";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";
import { useEffect, useState } from "react";
import { ViewToggle, type ViewMode } from "./ViewToggle";

interface HubResultsProps {
  hubs: SimplifiedHub[];
  searchTerm: string;
  isLoading: boolean;
  clearFilters: () => void;
  hasSearched: boolean;
}

export function HubResults({
  hubs,
  searchTerm,
  isLoading,
  clearFilters,
  hasSearched,
}: HubResultsProps) {
  const [announcement, setAnnouncement] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("grid");

  // Announce changes to screen readers
  useEffect(() => {
    if (isLoading) {
      setAnnouncement("Loading hubs...");
    } else if (hubs.length === 0 && hasSearched) {
      setAnnouncement(`No hubs found for ${searchTerm || "your search"}. Try adjusting your filters.`);
    } else if (hubs.length > 0) {
      setAnnouncement(`Found ${hubs.length} hubs${searchTerm ? ` for "${searchTerm}"` : ""}.`);
    }
  }, [hubs.length, isLoading, hasSearched, searchTerm]);

  // Handle view mode change
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
  };

  // Get the appropriate CSS class for the current view mode
  const getViewModeClass = () => {
    return viewMode === "grid"
      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 auto-rows-fr"
      : "flex flex-col gap-4 sm:gap-5";
  };

  return (
    <div className="relative">
      {/* Accessibility announcement for screen readers */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {announcement}
      </div>

      {/* View toggle header */}
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm text-gray-300">
          {hubs.length > 0 && (
            <span>
              Showing <span className="font-medium text-white">{hubs.length}</span> hubs
              {searchTerm ? <span> for &quot;<span className="text-primary">{searchTerm}</span>&quot;</span> : ""}
            </span>
          )}
        </div>
        <ViewToggle onChange={handleViewModeChange} initialMode={viewMode} />
      </div>

      <section
        aria-label={`Hub results${searchTerm ? ` for "${searchTerm}"` : ""}`}
        className={`${getViewModeClass()} mt-4 p-3 sm:p-4 rounded-xl bg-gray-900 border border-gray-800/50`}
      >
        {hubs.map((hub, index) => (
          <div
            key={hub.id}
            className="animate-fade-in"
            style={{
              animationDelay: `${index * 0.05}s`,
              opacity: 0 // Start with opacity 0, animation will bring it to 1
            }}
          >
            {viewMode === "grid"
              ? <HubCard hub={hub} variant="grid" />
              : <HubCard hub={hub} variant="list" />
            }
          </div>
        ))}
      </section>

      {/* Empty State */}
      {hubs.length === 0 && !isLoading && (
        <div
          className="flex flex-col items-center justify-center py-16 text-center p-8 rounded-xl bg-gray-900 border border-gray-800/50 mt-4 animate-fade-in"
        >
          <div className="mb-6 rounded-full bg-gradient-to-r from-primary/20 to-primary-alt/20 p-6 relative">
            <div className="absolute inset-0 rounded-full bg-primary/10 blur-xl animate-pulse"
                 style={{ animationDuration: "3s" }} />
            <Search className="h-10 w-10 text-primary relative" aria-hidden="true" />
          </div>
          <h3 className="mb-3 text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white via-purple-300 to-primary">
            No hubs found
          </h3>
          <p className="mb-8 max-w-md text-gray-300 text-lg">
            {searchTerm ?
              <>No results found for <span className="text-primary font-medium">&quot;{searchTerm}&quot;</span>. </> :
              ""}
            Try adjusting your search or filter criteria to find what
            you&apos;re looking for.
          </p>
          {hasSearched && (
            <div className="flex gap-4">
              <Button
                variant="outline"
                onClick={clearFilters}
                className="border-primary/20 text-primary hover:bg-primary/10 cursor-pointer shadow-lg shadow-primary/5 focus:ring-2 focus:ring-primary/50 h-12 px-6 rounded-xl"
                aria-label="Clear all search filters"
              >
                <RefreshCw className="h-5 w-5 mr-2" aria-hidden="true" />
                Clear all filters
              </Button>
              <Button
                variant="default"
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="bg-gradient-to-r from-primary to-primary-alt hover:from-primary/90 hover:to-primary-alt/90 text-white cursor-pointer shadow-lg shadow-primary/10 h-12 px-6 rounded-xl"
              >
                Try a new search
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Loading State */}
      {isLoading && hubs.length === 0 && (
        <div
          className={`${getViewModeClass()} p-3 sm:p-4 rounded-xl bg-gray-900 border border-gray-800/50 mt-4`}
          aria-label="Loading hubs"
          aria-busy="true"
        >
          {Array(6)
            .fill(0)
            .map((_, i) => (
              <div
                key={`skeleton-${i}`}
                className={`animate-pulse-slow ${viewMode === "grid"
                  ? "rounded-2xl border border-gray-700/50 bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 overflow-hidden h-[420px] flex flex-col"
                  : "rounded-xl border border-gray-700/50 bg-gradient-to-r from-gray-900 via-gray-900 to-gray-800 overflow-hidden h-[140px] flex"}`}
                aria-hidden="true"
                style={{
                  animationDelay: `${i * 0.15}s`,
                  opacity: 0.7
                }}
              >
                {viewMode === "grid" ? (
                  <>
                    <div className="h-32 w-full bg-gradient-to-br from-gray-800/70 to-gray-900/70 relative overflow-hidden">
                      <div className="absolute inset-0 bg-primary/5 animate-pulse"
                           style={{ animationDuration: `${3 + i * 0.5}s` }} />
                    </div>
                    <div className="p-5 flex-grow flex flex-col">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="w-14 h-14 bg-gray-700/50 rounded-xl" />
                        <div className="flex-1">
                          <div className="h-6 w-2/3 mb-2 bg-gray-700/50 rounded-md" />
                          <div className="h-4 w-1/2 bg-gray-700/30 rounded-md" />
                        </div>
                      </div>
                      <div className="h-4 w-full mb-2 bg-gray-700/30 rounded-md" />
                      <div className="h-4 w-3/4 mb-4 bg-gray-700/30 rounded-md" />
                      <div className="flex gap-2 mb-4">
                        <div className="h-6 w-16 bg-gray-700/40 rounded-full" />
                        <div className="h-6 w-16 bg-gray-700/40 rounded-full" />
                        <div className="h-6 w-16 bg-gray-700/40 rounded-full" />
                      </div>
                      <div className="grid grid-cols-3 gap-3 mb-4">
                        <div className="text-center">
                          <div className="h-4 w-8 mx-auto mb-1 bg-gray-700/40 rounded-md" />
                          <div className="h-3 w-12 mx-auto bg-gray-700/30 rounded-md" />
                        </div>
                        <div className="text-center">
                          <div className="h-4 w-8 mx-auto mb-1 bg-gray-700/40 rounded-md" />
                          <div className="h-3 w-12 mx-auto bg-gray-700/30 rounded-md" />
                        </div>
                        <div className="text-center">
                          <div className="h-4 w-8 mx-auto mb-1 bg-gray-700/40 rounded-md" />
                          <div className="h-3 w-12 mx-auto bg-gray-700/30 rounded-md" />
                        </div>
                      </div>
                      <div className="flex gap-2 mt-auto">
                        <div className="h-10 flex-1 bg-gray-700/40 rounded-md" />
                        <div className="h-10 flex-1 bg-gray-700/40 rounded-md" />
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="w-48 flex-shrink-0 bg-gradient-to-br from-gray-800/70 to-gray-900/70 relative overflow-hidden">
                      <div className="absolute inset-0 bg-primary/5 animate-pulse"
                           style={{ animationDuration: `${3 + i * 0.5}s` }} />
                    </div>
                    <div className="p-4 flex-grow flex flex-col">
                      <div className="flex justify-between mb-2">
                        <div className="flex-1">
                          <div className="h-6 w-32 mb-2 bg-gray-700/50 rounded-md" />
                          <div className="h-4 w-full bg-gray-700/30 rounded-md" />
                        </div>
                        <div className="h-6 w-20 bg-gray-700/40 rounded-full ml-3" />
                      </div>
                      <div className="flex justify-between items-center mt-auto">
                        <div className="flex gap-2">
                          <div className="h-5 w-12 bg-gray-700/40 rounded-full" />
                          <div className="h-5 w-12 bg-gray-700/40 rounded-full" />
                        </div>
                        <div className="flex gap-2">
                          <div className="h-4 w-8 bg-gray-700/30 rounded-md" />
                          <div className="h-4 w-8 bg-gray-700/30 rounded-md" />
                          <div className="h-4 w-8 bg-gray-700/30 rounded-md" />
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 p-4 justify-center w-32">
                      <div className="h-8 w-full bg-gray-700/40 rounded-md" />
                      <div className="h-8 w-full bg-gray-700/40 rounded-md" />
                    </div>
                  </>
                )}
              </div>
            ))}
        </div>
      )}
    </div>
  );
}
