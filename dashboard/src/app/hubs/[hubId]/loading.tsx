"use client";

import { Skeleton } from "@/components/ui/skeleton";
import {
  Calendar,
  Heart,
  Info,
  Search,
  Shield,
  Star,
  Users,
} from "lucide-react";

export default function HubDetailLoading() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-950 text-gray-200">
      {/* Header with Search Bar */}
      <header className="sticky top-0 z-40 w-full border-b border-gray-800 bg-gray-950/80 backdrop-blur-lg">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            {/* Breadcrumb */}
            <div className="flex items-center">
              <Skeleton className="h-5 w-32" />
            </div>

            {/* Search Bar */}
            <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                  <Search
                    className="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </div>
                <Skeleton className="h-10 w-full rounded-lg" />
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-grow">
        {/* Banner Section */}
        <div className="relative h-[280px] md:h-[350px] w-full overflow-hidden">
          <Skeleton className="absolute inset-0" />
        </div>

        {/* Page Content */}
        <div className="container mx-auto px-4 max-w-7xl pb-16">
          {/* Header Card (Overlaps Banner) */}
          <div className="relative -mt-24 md:-mt-32 mb-12">
            <div className="rounded-xl border border-gray-800 bg-gray-900/80 p-6 md:p-8 shadow-xl backdrop-blur-lg">
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                {/* Hub Icon */}
                <Skeleton className="h-28 w-28 md:h-36 md:w-36 flex-shrink-0 rounded-2xl" />

                {/* Hub Info */}
                <div className="flex-1 min-w-0">
                  <Skeleton className="h-10 w-3/4 mb-4" />
                  <div className="mt-2 flex flex-wrap items-center gap-x-4 gap-y-2">
                    {/* Server Count */}
                    <Skeleton className="h-8 w-32 rounded-full" />
                    {/* Creation Date */}
                    <Skeleton className="h-8 w-40 rounded-full" />
                    {/* Tags */}
                    <Skeleton className="h-8 w-36 rounded-full" />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-shrink-0 gap-3 mt-4 md:mt-0 self-start md:self-center">
                  <Skeleton className="h-10 w-32 rounded-full" />
                  <Skeleton className="h-10 w-28 rounded-full" />
                </div>
              </div>
            </div>
          </div>

          {/* Main Grid (Tabs/Reviews + Sidebar) */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column (Tabs and Reviews) */}
            <div className="lg:col-span-2 space-y-8">
              {/* Tabs Section */}
              <div className="rounded-xl border border-gray-800 bg-gray-900/70 p-6 md:p-8 shadow-lg backdrop-blur-md">
                {/* Tab List */}
                <div className="mb-6">
                  <Skeleton className="h-10 w-64 rounded-lg" />
                </div>

                {/* Tab Content */}
                <div className="space-y-4">
                  <Skeleton className="h-6 w-48" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                  <Skeleton className="h-4 w-full" />
                </div>
              </div>

              {/* Review Section */}
              <div className="rounded-xl border border-gray-800 bg-gray-900/70 p-6 md:p-8 shadow-lg backdrop-blur-md">
                <div className="flex items-center mb-6">
                  <div className="bg-primary/10 p-2 rounded-lg mr-3 border border-primary/30">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <Skeleton className="h-8 w-48" />
                </div>

                {/* Review Form */}
                <div className="mb-8 p-4 border border-gray-800 rounded-lg bg-gray-800/30">
                  <Skeleton className="h-6 w-40 mb-4" />
                  <Skeleton className="h-24 w-full mb-4 rounded-md" />
                  <div className="flex justify-end">
                    <Skeleton className="h-10 w-24 rounded-md" />
                  </div>
                </div>

                {/* Reviews List */}
                <div className="space-y-4">
                  {Array(2)
                    .fill(0)
                    .map((_, i) => (
                      <div
                        key={i}
                        className="p-4 border border-gray-800 rounded-lg bg-gray-800/30"
                      >
                        <div className="flex items-center gap-3 mb-3">
                          <Skeleton className="h-10 w-10 rounded-full" />
                          <div>
                            <Skeleton className="h-5 w-32 mb-1" />
                            <div className="flex">
                              {Array(5)
                                .fill(0)
                                .map((_, j) => (
                                  <Star
                                    key={j}
                                    className="h-4 w-4 text-yellow-500"
                                  />
                                ))}
                            </div>
                          </div>
                        </div>
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-4 w-5/6" />
                      </div>
                    ))}
                </div>
              </div>
            </div>

            {/* Right Column (Sidebar) */}
            <div className="lg:col-span-1 space-y-6">
              {/* Hub Details Card */}
              <div className="rounded-xl border border-gray-800 bg-gray-900/70 p-6 shadow-lg backdrop-blur-md">
                <div className="flex items-center mb-5">
                  <Info className="h-5 w-5 mr-2 text-primary" />
                  <Skeleton className="h-6 w-32" />
                </div>
                <div className="space-y-3">
                  {/* Created Date */}
                  <div className="flex items-center justify-between rounded-md bg-black/20 p-3 border border-gray-700/50">
                    <div className="flex items-center text-sm text-gray-400">
                      <Calendar className="h-4 w-4 mr-2 text-primary/80" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                    <Skeleton className="h-4 w-24" />
                  </div>
                  {/* Connected Servers */}
                  <div className="flex items-center justify-between rounded-md bg-black/20 p-3 border border-gray-700/50">
                    <div className="flex items-center text-sm text-gray-400">
                      <Users className="h-4 w-4 mr-2 text-primary/80" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <Skeleton className="h-4 w-8" />
                  </div>
                  {/* Upvotes */}
                  <div className="flex items-center justify-between rounded-md bg-black/20 p-3 border border-gray-700/50">
                    <div className="flex items-center text-sm text-gray-400">
                      <Heart className="h-4 w-4 mr-2 text-rose-500/80" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                    <Skeleton className="h-4 w-8" />
                  </div>
                </div>
              </div>

              {/* Moderators Card */}
              <div className="rounded-xl border border-gray-800 bg-gray-900/70 p-6 shadow-lg backdrop-blur-md">
                <div className="flex items-center mb-5">
                  <Shield className="h-5 w-5 mr-2 text-primary" />
                  <Skeleton className="h-6 w-24" />
                </div>
                <div className="space-y-3">
                  {Array(3)
                    .fill(0)
                    .map((_, i) => (
                      <div
                        key={i}
                        className="flex items-center gap-3 rounded-md p-2 transition-colors hover:bg-gray-800/50"
                      >
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-5 w-32 mb-1" />
                          <Skeleton className="h-4 w-20" />
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
