import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Home, Search } from "lucide-react";

export default function HubNotFound() {
  return (
    <div className="container max-w-6xl mx-auto py-16 px-4 text-center">
      <h1 className="text-4xl font-bold mb-4">Hub Not Found</h1>
      <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-lg mx-auto">
        Sorry, the hub you&apos;re looking for doesn&apos;t exist or may have
        been removed.
      </p>
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
        <Button asChild>
          <Link href="/hubs">
            <Search className="mr-2 h-4 w-4" />
            Browse Hubs
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/">
            <Home className="mr-2 h-4 w-4" />
            Go Home
          </Link>
        </Button>
      </div>
    </div>
  );
}
