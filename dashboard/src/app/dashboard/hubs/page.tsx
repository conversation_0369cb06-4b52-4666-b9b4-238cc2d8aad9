import { auth } from "@/auth";
import { getUserHubs } from "@/lib/permissions";
import { But<PERSON> } from "@/components/ui/button";
import { TabsContent } from "@/components/ui/tabs";
import { UnderlinedTabs } from "@/components/dashboard/underlined-tabs";
import { PlusCircle } from "lucide-react";
import type { Metadata } from "next";
import Link from "next/link";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { AnimatedHubsHero } from "@/components/dashboard/hubs/animated-hubs-hero";
import { AnimatedHubCard } from "@/components/dashboard/hubs/animated-hub-card";
import { AnimatedEmptyState } from "@/components/dashboard/hubs/animated-empty-state";
import { AnimatedHubsSkeleton } from "@/components/dashboard/hubs/animated-hubs-skeleton";
import { db } from "@/lib/prisma";
import { PermissionLevel } from "@/lib/constants";

export const metadata: Metadata = {
  title: "My Hubs | InterChat Dashboard",
  description: "Manage your InterChat hubs",
};

export default function HubsPage() {
  return (
    <Suspense fallback={<AnimatedHubsSkeleton />}>
      <HubsContent />
    </Suspense>
  );
}

async function HubsContent() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/hubs");
  }

  // Get user's hubs
  const userHubs = await getUserHubs(session.user.id);

  // Filter hubs by permission level
  const ownedHubs = userHubs.filter(
    (hub) => hub.permissionLevel === PermissionLevel.OWNER,
  );
  const managedHubs = userHubs.filter(
    (hub) => hub.permissionLevel === PermissionLevel.MANAGER,
  );
  const moderatedHubs = userHubs.filter(
    (hub) => hub.permissionLevel === PermissionLevel.MODERATOR,
  );

  // Get total connections and servers
  const userHubIds = userHubs.map((hub) => hub.id);

  // Count connections
  const totalConnections = await db.connection.count({
    where: {
      hubId: {
        in: userHubIds,
      },
    },
  });

  // Get connected servers
  const connectedServers = await db.serverData.findMany({
    where: {
      connections: {
        some: {
          hubId: {
            in: userHubIds,
          },
        },
      },
    },
    distinct: ["id"],

  });

  return (
    <div className="space-y-6">
      {/* Animated Hero Section */}
      <AnimatedHubsHero
        totalHubs={userHubs.length}
        totalConnections={totalConnections}
        totalServers={connectedServers.length}
      />
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">My Hubs</h1>
        <Button
          asChild
          className="bg-gradient-to-r from-primary-alt to-primary hover:from-primary-alt/60 hover:to-primary/60 border-none w-full sm:w-auto"
          data-tour="create-hub"
        >
          <Link href="/dashboard/hubs/create">
            <PlusCircle className="h-4 w-4 mr-2" />
            Create Hub
          </Link>
        </Button>
      </div>
      <UnderlinedTabs
        defaultValue="owned"
        className="space-y-6"
        data-tour="hub-list"
        tabs={[
          {
            value: "owned",
            label: `Owned (${ownedHubs.length})`,
            color: "indigo",
          },
          {
            value: "managed",
            label: `Managed (${managedHubs.length})`,
            color: "blue",
          },
          {
            value: "moderated",
            label: `Moderated (${moderatedHubs.length})`,
            color: "purple",
          },
        ]}
      >
        <TabsContent value="owned" className="space-y-6">
          {ownedHubs.length === 0 ? (
            <AnimatedEmptyState type="owned" />
          ) : (
            <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {ownedHubs.map((hub, index) => (
                <AnimatedHubCard key={hub.id} hub={hub} index={index} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="managed" className="space-y-6">
          {managedHubs.length === 0 ? (
            <AnimatedEmptyState type="managed" />
          ) : (
            <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {managedHubs.map((hub, index) => (
                <AnimatedHubCard key={hub.id} hub={hub} index={index} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="moderated" className="space-y-6">
          {moderatedHubs.length === 0 ? (
            <AnimatedEmptyState type="moderated" />
          ) : (
            <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {moderatedHubs.map((hub, index) => (
                <AnimatedHubCard key={hub.id} hub={hub} index={index} />
              ))}
            </div>
          )}
        </TabsContent>
      </UnderlinedTabs>
    </div>
  );
}
