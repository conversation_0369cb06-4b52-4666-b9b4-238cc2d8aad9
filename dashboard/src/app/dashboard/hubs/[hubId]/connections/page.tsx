import { auth } from "@/auth";
import { ClientConnectionsList } from "./client";
import { Button } from "@/components/ui/button";
import { HubLayout } from "@/components/dashboard/hubs/hub-layout";
import { HydrationBoundaryProvider } from "@/components/providers/hydration-boundary";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { Plus } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { createDehydratedState } from "@/lib/create-dehydrated-state";

interface HubConnectionsPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export default async function HubConnectionsPage({
  params,
}: HubConnectionsPageProps) {
  const hubId  = (await params).hubId;
  const session = await auth();

  if (!session?.user) {
    return notFound();
  }

  // Create dehydrated state for React Query
  const dehydratedState = await createDehydratedState(async (queryClient) => {
    // Prefetch connections for this hub
    await queryClient.prefetchQuery({
      queryKey: ["connections", hubId],
      queryFn: async () => {
        const connections = await db.connection.findMany({
          where: { hubId },
          select: {
            id: true,
            serverId: true,
            channelId: true,
            connected: true,
            compact: true,
            createdAt: true,
            lastActive: true,
            invite: true,
            hubId: true,
            server: {
              select: {
                id: true,
                name: true,
                iconUrl: true,
              },
            },
          },
          orderBy: { lastActive: "desc" },
        });
        return connections;
      },
    });
  });

  // Get user's permission level for this hub
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;

  // Get hub details
  const hub = await db.hub.findUnique({
    where: { id: hubId },
    select: {
      id: true,
      name: true,
      description: true,
      private: true,
      ownerId: true,
      nsfw: true,
      iconUrl: true,
      bannerUrl: true,
    },
  });

  if (!hub) {
    return notFound();
  }

  // Only allow users with at least moderator permissions
  if (permissionLevel < PermissionLevel.MODERATOR) {
    return notFound();
  }

  // Get connections for this hub
  const connections = await db.connection.findMany({
    where: { hubId },
    select: {
      id: true,
      serverId: true,
      channelId: true,
      connected: true,
      compact: true,
      createdAt: true,
      lastActive: true,
      invite: true,
      hubId: true,
      server: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      },
    },
    orderBy: { lastActive: "desc" },
  });

  // Prepare hub data for the layout
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    iconUrl: hub.iconUrl,
    bannerUrl: hub.bannerUrl,
    private: hub.private,
    nsfw: hub.nsfw,
    connectionCount: connections.filter(c => c.connected).length,
  };

  // Header actions - Mobile optimized
  const headerActions = canEdit ? (
    <Button
      asChild
      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none h-10 sm:h-9 px-3 sm:px-4 text-sm touch-manipulation"
    >
      <Link href={`/dashboard/servers?hubId=${hubId}`}>
        <Plus className="h-4 w-4 mr-2" />
        <span className="hidden xs:inline">Connect Server</span>
        <span className="xs:hidden">Connect</span>
      </Link>
    </Button>
  ) : null;

  return (
    <HubLayout
      hub={hubData}
      currentTab="connections"
      canModerate={canModerate}
      canEdit={canEdit}
      headerActions={headerActions}
    >
      {/* Use the ClientConnectionsList component with the fetched data */}
      <HydrationBoundaryProvider state={dehydratedState}>
        <ClientConnectionsList
          initialConnections={connections}
          hubId={hubId}
          canManage={canEdit}
        />
      </HydrationBoundaryProvider>
    </HubLayout>
  );
}
