"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import { UnderlinedTabs } from "@/components/dashboard/underlined-tabs";
import { formatDistanceToNow } from "date-fns";
import { Clock, Home, PlusCircle, Shield, Ban, AlertTriangle, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";

interface Infraction {
  id: string;
  type: string;
  status: string;
  reason: string;
  createdAt: string;
  expiresAt: string | null;
  userId: string | null;
  serverId: string | null;
  serverName: string | null;
  hub: {
    id: string;
    name: string;
    iconUrl: string;
  };
  moderator: {
    id: string;
    name: string;
    image: string | null;
  } | null;
  user: {
    id: string;
    name: string;
    image: string | null;
  } | null;
}

interface BlacklistsClientProps {
  hubId: string;
}

// Helper function to calculate duration
function getDuration(createdAt: Date, expiresAt: Date | null): string {
  if (!expiresAt) return "Permanent";

  const durationMs = expiresAt.getTime() - createdAt.getTime();
  const durationDays = Math.round(durationMs / (1000 * 60 * 60 * 24));

  if (durationDays <= 1) return "1 day";
  if (durationDays <= 7) return `${durationDays} days`;
  if (durationDays <= 30) return `${Math.ceil(durationDays / 7)} weeks`;
  if (durationDays <= 365) return `${Math.ceil(durationDays / 30)} months`;
  return `${Math.ceil(durationDays / 365)} years`;
}

export function BlacklistsClient({ hubId }: BlacklistsClientProps) {
  const [blacklists, setBlacklists] = useState<Infraction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const { toast } = useToast();

  const limit = 20; // Show 20 items per page
  const totalPages = Math.ceil(totalCount / limit);

  // Fetch blacklists for this specific hub
  const fetchBlacklists = useCallback(async (page: number = 1) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/infractions?hubId=${hubId}&type=BLACKLIST&status=ACTIVE&page=${page}&limit=${limit}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch blacklists");
      }

      const data = await response.json();
      setBlacklists(data.infractions || []);
      setTotalCount(data.total || 0);
    } catch (error) {
      console.error("Error fetching blacklists:", error);
      toast({
        title: "Error",
        description: "Failed to load blacklists. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [hubId, limit, toast]);

  useEffect(() => {
    fetchBlacklists(currentPage);
  }, [fetchBlacklists, currentPage]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Separate users and servers
  const blacklistedUsers = blacklists.filter((item) => item.userId);
  const blacklistedServers = blacklists.filter((item) => item.serverId);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Hub Blacklists</h1>
            <p className="text-gray-400">
              Manage blacklisted users and servers for this hub
            </p>
          </div>
        </div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-700 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Hub Blacklists</h1>
          <p className="text-gray-400">
            Manage blacklisted users and servers for this hub
          </p>
        </div>
        <Button
          asChild
          className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none"
        >
          <Link href={`/dashboard/moderation/blacklist/add?hubId=${hubId}`}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add to Blacklist
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-500/20 rounded-lg">
                <Ban className="h-4 w-4 text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Total Blacklisted</p>
                <p className="text-xl font-bold text-white">{blacklists.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/20 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-orange-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Blacklisted Users</p>
                <p className="text-xl font-bold text-white">{blacklistedUsers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Home className="h-4 w-4 text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Blacklisted Servers</p>
                <p className="text-xl font-bold text-white">{blacklistedServers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Users and Servers */}
      <UnderlinedTabs
        defaultValue="users"
        className="w-full space-y-6"
        tabs={[
          {
            value: "users",
            label: `Users (${blacklistedUsers.length})`,
            color: "red",
          },
          {
            value: "servers",
            label: `Servers (${blacklistedServers.length})`,
            color: "purple",
          },
        ]}
      >
        <TabsContent value="users" className="space-y-4">
          {blacklistedUsers.length > 0 ? (
            <div className="space-y-4">
              {blacklistedUsers.map((infraction) => (
                <BlacklistedUserCard
                  key={infraction.id}
                  infraction={infraction}
                />
              ))}
            </div>
          ) : (
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>No Blacklisted Users</CardTitle>
                <CardDescription>
                  There are no users on the blacklist for this hub.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-400 mb-4">
                  When you blacklist users, they will appear here.
                </p>
                <Button
                  asChild
                  className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none"
                >
                  <Link href={`/dashboard/moderation/blacklist/add?hubId=${hubId}&type=user`}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add User to Blacklist
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="servers" className="space-y-4">
          {blacklistedServers.length > 0 ? (
            <div className="space-y-4">
              {blacklistedServers.map((infraction) => (
                <BlacklistedServerCard
                  key={infraction.id}
                  infraction={infraction}
                />
              ))}
            </div>
          ) : (
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>No Blacklisted Servers</CardTitle>
                <CardDescription>
                  There are no servers on the blacklist for this hub.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-400 mb-4">
                  When you blacklist servers, they will appear here.
                </p>
                <Button
                  asChild
                  className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-600/80 hover:to-purple-600/80 border-none"
                >
                  <Link href={`/dashboard/moderation/blacklist/add?hubId=${hubId}&type=server`}>
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Server to Blacklist
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </UnderlinedTabs>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="flex items-center justify-between p-4">
            <div className="text-sm text-gray-400">
              Page {currentPage} of {totalPages} ({totalCount} total blacklists)
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function BlacklistedUserCard({ infraction }: { infraction: Infraction }) {
  const blacklistedOn = formatDistanceToNow(new Date(infraction.createdAt), {
    addSuffix: true,
  });

  const expiresIn = infraction.expiresAt
    ? formatDistanceToNow(new Date(infraction.expiresAt), {
        addSuffix: true,
      })
    : "Never";

  const duration = getDuration(
    new Date(infraction.createdAt),
    infraction.expiresAt ? new Date(infraction.expiresAt) : null,
  );

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full border-2 border-gray-700/50 overflow-hidden">
              <Image
                src={infraction.hub.iconUrl}
                alt={infraction.hub.name}
                width={32}
                height={32}
                className="object-cover"
                style={{ width: "100%", height: "100%" }}
                unoptimized
              />
            </div>
            <CardTitle className="text-lg">{infraction.hub.name}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-400 flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              Blacklisted {blacklistedOn}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-red-500/20 overflow-hidden">
            <Image
              src={
                infraction.user?.image ||
                "https://api.dicebear.com/7.x/shapes/svg?seed=user"
              }
              alt={infraction.user?.name || "Unknown User"}
              width={40}
              height={40}
              className="object-cover"
              style={{ width: "100%", height: "100%" }}
              unoptimized
            />
          </div>
          <div>
            <div className="font-medium">
              <Link
                href={`https://discordlookup.com/user/${infraction.userId}`}
                target="_blank"
                className="hover:text-red-400 transition-colors"
              >
                {infraction.user?.name || "Unknown User"}
              </Link>
            </div>
            <div className="text-xs text-gray-400">
              User ID: {infraction.userId}
            </div>
          </div>
        </div>

        <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
          <div className="text-sm mb-2 text-gray-400">Reason:</div>
          <div className="text-sm">{infraction.reason}</div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Duration:</span> {duration}
          </div>
          <div>
            <span className="text-gray-400">Expires:</span> {expiresIn}
          </div>
          <div>
            <span className="text-gray-400">Added by:</span>{" "}
            {infraction.moderator?.name || "Unknown"}
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <Button
            variant="outline"
            className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-blue-400 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/extend/${infraction.id}`}
            >
              <Clock className="h-4 w-4 mr-2" />
              Extend
            </Link>
          </Button>
          <Button
            variant="outline"
            className="border-red-700/30 bg-red-950/20 text-red-400 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/remove/${infraction.id}`}
            >
              <Shield className="h-4 w-4 mr-2" />
              Remove
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function BlacklistedServerCard({ infraction }: { infraction: Infraction }) {
  const blacklistedOn = formatDistanceToNow(new Date(infraction.createdAt), {
    addSuffix: true,
  });

  const expiresIn = infraction.expiresAt
    ? formatDistanceToNow(new Date(infraction.expiresAt), {
        addSuffix: true,
      })
    : "Never";

  const duration = getDuration(
    new Date(infraction.createdAt),
    infraction.expiresAt ? new Date(infraction.expiresAt) : null,
  );

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full border-2 border-gray-700/50 overflow-hidden">
              <Image
                src={infraction.hub.iconUrl}
                alt={infraction.hub.name}
                width={32}
                height={32}
                className="object-cover"
                style={{ width: "100%", height: "100%" }}
                unoptimized
              />
            </div>
            <CardTitle className="text-lg">{infraction.hub.name}</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-400 flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              Blacklisted {blacklistedOn}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-purple-500/20 overflow-hidden bg-gray-800 flex items-center justify-center">
            <Home className="h-5 w-5 text-purple-400" />
          </div>
          <div>
            <div className="font-medium">
              <Link
                href={`https://discordlookup.com/server/${infraction.serverId}`}
                target="_blank"
                className="hover:text-purple-400 transition-colors"
              >
                {infraction.serverName || "Unknown Server"}
              </Link>
            </div>
            <div className="text-xs text-gray-400">
              Server ID: {infraction.serverId}
            </div>
          </div>
        </div>

        <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
          <div className="text-sm mb-2 text-gray-400">Reason:</div>
          <div className="text-sm">{infraction.reason}</div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Duration:</span> {duration}
          </div>
          <div>
            <span className="text-gray-400">Expires:</span> {expiresIn}
          </div>
          <div>
            <span className="text-gray-400">Added by:</span>{" "}
            {infraction.moderator?.name || "Unknown"}
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <Button
            variant="outline"
            className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-purple-400 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/extend/${infraction.id}`}
            >
              <Clock className="h-4 w-4 mr-2" />
              Extend
            </Link>
          </Button>
          <Button
            variant="outline"
            className="border-purple-700/30 bg-purple-950/20 text-purple-400 hover:bg-purple-900/30 hover:text-purple-300 hover:border-purple-700/50 transition-all"
            asChild
          >
            <Link
              href={`/dashboard/moderation/blacklist/remove/${infraction.id}`}
            >
              <Shield className="h-4 w-4 mr-2" />
              Remove
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
