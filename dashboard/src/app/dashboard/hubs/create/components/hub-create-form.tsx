"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  ArrowLeft,
  ArrowRight,
  Check,
  Loader2,
  Plus,
  X,
  Sparkles,
  Users,
  MessageSquare,
  Shield
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

export function HubCreateForm() {
  const [step, setStep] = useState(1);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(true);
  const [welcomeMessage, setWelcomeMessage] = useState("");
  const [rules, setRules] = useState<string[]>([""]);
  const [nameError, setNameError] = useState("");
  const [isValidatingName, setIsValidatingName] = useState(false);
  const [isNameValid, setIsNameValid] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const nameInputRef = useRef<HTMLInputElement>(null);

  // Focus on name input when component mounts
  useEffect(() => {
    if (nameInputRef.current) {
      nameInputRef.current.focus();
    }
  }, []);

  // Check if hub name is unique
  const checkHubName = async (name: string) => {
    if (name.length < 3) {
      setIsNameValid(false);
      return;
    }

    setIsValidatingName(true);
    setNameError("");

    try {
      const response = await fetch(
        `/api/hubs/validate-name?name=${encodeURIComponent(name)}`
      );

      if (!response.ok) {
        setIsValidatingName(false);
        return;
      }

      const data = await response.json();

      if (!data.available) {
        setNameError("This hub name is already taken");
        setIsNameValid(false);
      } else {
        setNameError("");
        setIsNameValid(true);
      }
    } catch (error) {
      console.error("Error checking hub name:", error);
      setNameError("Failed to check hub name availability");
      setIsNameValid(false);
    } finally {
      setIsValidatingName(false);
    }
  };

  // Debounce name checking
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (name.length >= 3) {
        checkHubName(name);
      } else {
        setIsNameValid(false);
        setNameError("");
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [name]);

  const addRule = () => {
    if (rules.length < 10) {
      setRules([...rules, ""]);
    }
  };

  const updateRule = (index: number, value: string) => {
    const updatedRules = [...rules];
    updatedRules[index] = value;
    setRules(updatedRules);
  };

  const removeRule = (index: number) => {
    if (rules.length > 1) {
      const updatedRules = rules.filter((_, i) => i !== index);
      setRules(updatedRules);
    } else {
      // If it's the last rule, just clear it
      updateRule(0, "");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    if (name.length < 3) {
      toast({
        title: "Error",
        description: "Hub name must be at least 3 characters",
        variant: "destructive",
      });
      return;
    }

    if (description.length < 10) {
      toast({
        title: "Error",
        description: "Description must be at least 10 characters",
        variant: "destructive",
      });
      return;
    }

    if (nameError || !isNameValid || isValidatingName) {
      toast({
        title: "Error",
        description:
          nameError || "Please ensure the hub name is valid and unique",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Filter out empty rules
      const filteredRules = rules.filter((rule) => rule.trim() !== "");

      const response = await fetch("/api/hubs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
          private: isPrivate,
          welcomeMessage: welcomeMessage || null,
          rules: filteredRules,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create hub");
      }

      const data = await response.json();

      toast({
        title: "🎉 Hub Created Successfully!",
        description: `${name} is ready to connect communities worldwide.`,
      });

      // Redirect to the hub management page
      router.push(`/dashboard/hubs/${data.hub.id}`);
    } catch (error) {
      console.error("Error creating hub:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to create hub",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (
      step === 1 &&
      name.length >= 3 &&
      !nameError &&
      isNameValid &&
      !isValidatingName
    ) {
      setStep(2);
    } else if (step === 2 && description.length >= 10) {
      setStep(3);
    }
  };

  const prevStep = () => {
    if (step === 2) {
      setStep(1);
    } else if (step === 3) {
      setStep(2);
    }
  };

  const canProceed = () => {
    if (step === 1) {
      return name.length >= 3 && !nameError && isNameValid && !isValidatingName;
    } else if (step === 2) {
      return description.length >= 10;
    }
    return true;
  };

  const stepIcons = [
    { icon: Sparkles, label: "Name", color: "text-purple-400" },
    { icon: MessageSquare, label: "Details", color: "text-blue-400" },
    { icon: Shield, label: "Rules", color: "text-green-400" },
  ];

  return (
    <div className="max-w-2xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {stepIcons.map((stepIcon, index) => {
            const StepIcon = stepIcon.icon;
            const isActive = step === index + 1;
            const isCompleted = step > index + 1;

            return (
              <div key={index} className="flex items-center">
                <div
                  className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    isCompleted
                      ? "bg-green-500 border-green-500 text-white"
                      : isActive
                      ? `border-indigo-500 bg-indigo-500/10 ${stepIcon.color}`
                      : "border-gray-600 bg-gray-800/50 text-gray-400"
                  }`}
                >
                  {isCompleted ? (
                    <Check className="h-6 w-6" />
                  ) : (
                    <StepIcon className="h-6 w-6" />
                  )}
                </div>
                {index < stepIcons.length - 1 && (
                  <div
                    className={`w-16 h-0.5 mx-4 transition-all duration-300 ${
                      step > index + 1 ? "bg-green-500" : "bg-gray-600"
                    }`}
                  />
                )}
              </div>
            );
          })}
        </div>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white mb-1">
            Step {step} of 3: {stepIcons[step - 1].label}
          </h3>
          <p className="text-gray-400 text-sm">
            {step === 1 && "Choose a unique name that represents your community"}
            {step === 2 && "Describe your hub and configure basic settings"}
            {step === 3 && "Set up welcome message and community rules"}
          </p>
        </div>
      </div>

      {/* Form Card */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-2xl">
        <form onSubmit={handleSubmit}>
          {/* Step 1: Hub Name */}
          {step === 1 && (
            <>
              <CardHeader className="text-center pb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mb-4">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl">What&apos;s your hub called?</CardTitle>
                <CardDescription className="text-base">
                  Choose a unique name that represents your community
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="name" className="text-base font-medium">
                    Hub Name
                  </Label>
                  <Input
                    ref={nameInputRef}
                    id="name"
                    placeholder="e.g., Gaming Central, Art Community, Tech Hub"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className={`bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 text-lg py-6 ${
                      nameError ? "border-red-500/50" : ""
                    } ${isNameValid && name.length >= 3 ? "border-green-500/50" : ""}`}
                    maxLength={32}
                  />
                  <div className="flex items-center justify-between">
                    <div className="text-sm">
                      {isValidatingName && (
                        <span className="text-indigo-400 flex items-center">
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Checking availability...
                        </span>
                      )}
                      {nameError && (
                        <span className="text-red-400">{nameError}</span>
                      )}
                      {isNameValid && name.length >= 3 && !isValidatingName && (
                        <span className="text-green-400 flex items-center">
                          <Check className="h-3 w-3 mr-1" />
                          Name is available
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-400">{name.length}/32</span>
                  </div>
                </div>

                <div className="flex justify-end pt-4">
                  <Button
                    type="button"
                    onClick={nextStep}
                    disabled={!canProceed()}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none px-8 py-3"
                  >
                    Continue
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </>
          )}

          {/* Step 2: Description & Settings */}
          {step === 2 && (
            <>
              <CardHeader className="text-center pb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center mb-4">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Describe your hub</CardTitle>
                <CardDescription className="text-base">
                  Help people understand what your community is about
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="description" className="text-base font-medium">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="Describe what your hub is about, its purpose, and what kind of community you want to build..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[120px] resize-none"
                    maxLength={500}
                  />
                  <div className="flex items-center justify-between">
                    <div className="text-sm">
                      {description.length < 10 && description.length > 0 && (
                        <span className="text-yellow-400">
                          Description must be at least 10 characters
                        </span>
                      )}
                      {description.length >= 10 && (
                        <span className="text-green-400 flex items-center">
                          <Check className="h-3 w-3 mr-1" />
                          Good description
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-400">
                      {description.length}/500
                    </span>
                  </div>
                </div>

                <div className="bg-gray-800/30 p-4 rounded-lg border border-gray-800/50">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="private" className="text-base font-medium">
                        Private Hub
                      </Label>
                      <p className="text-sm text-gray-400">
                        Private hubs require an invitation to join and won&apos;t appear in public listings
                      </p>
                    </div>
                    <Switch
                      id="private"
                      checked={isPrivate}
                      onCheckedChange={setIsPrivate}
                      className="data-[state=checked]:bg-indigo-600"
                    />
                  </div>
                </div>

                <div className="flex justify-between pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white px-6"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                  <Button
                    type="button"
                    onClick={nextStep}
                    disabled={!canProceed()}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none px-8"
                  >
                    Continue
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </>
          )}

          {/* Step 3: Rules & Welcome Message */}
          {step === 3 && (
            <>
              <CardHeader className="text-center pb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Welcome & Rules</CardTitle>
                <CardDescription className="text-base">
                  Set up a welcome message and community guidelines (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="welcomeMessage" className="text-base font-medium">
                    Welcome Message (Optional)
                  </Label>
                  <Textarea
                    id="welcomeMessage"
                    placeholder="Welcome to our hub! This message will be shown to new members when they join..."
                    value={welcomeMessage}
                    onChange={(e) => setWelcomeMessage(e.target.value)}
                    className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[100px] resize-none"
                    maxLength={1000}
                  />
                  <div className="flex justify-between">
                    <p className="text-xs text-gray-400">
                      A warm welcome message helps new members feel at home
                    </p>
                    <span className="text-xs text-gray-400">
                      {welcomeMessage.length}/1000
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">
                      Community Rules (Optional)
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addRule}
                      disabled={rules.length >= 10}
                      className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Rule
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {rules.map((rule, index) => (
                      <div key={index} className="flex gap-3">
                        <div className="flex-1">
                          <Input
                            placeholder={`Rule ${index + 1}: e.g., Be respectful to all members`}
                            value={rule}
                            onChange={(e) => updateRule(index, e.target.value)}
                            className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50"
                            maxLength={200}
                          />
                        </div>
                        {rules.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() => removeRule(index)}
                            className="border-red-500/30 text-red-400 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>

                  {rules.length >= 10 && (
                    <p className="text-sm text-yellow-400">
                      Maximum of 10 rules allowed
                    </p>
                  )}

                  <p className="text-xs text-gray-400">
                    Clear rules help maintain a positive community environment
                  </p>
                </div>

                <div className="flex justify-between pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                    className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white px-6"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-600/80 hover:to-emerald-600/80 border-none px-8 py-3"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating Hub...
                      </>
                    ) : (
                      <>
                        <Users className="h-4 w-4 mr-2" />
                        Create Hub
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </>
          )}
        </form>
      </Card>
    </div>
  );
}
