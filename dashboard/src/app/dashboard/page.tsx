import { auth } from "@/auth";
import { AnimatedActivityCard } from "@/components/dashboard/animated-activity-card";
import { AnimatedDashboardSkeleton } from "@/components/dashboard/animated-dashboard-skeleton";
import { AnimatedStatCard } from "@/components/dashboard/animated-stat-card";
import { AnimatedWelcome } from "@/components/dashboard/animated-welcome";
import { OnboardingQuickActions } from "@/components/dashboard/onboarding/onboarding-quick-actions";
import { RecentNotifications } from "@/components/dashboard/recent-notifications";
import { SystemStatus } from "@/components/dashboard/system-status";
import { getUserHubs } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Dashboard | InterChat",
  description: "InterChat Dashboard - Manage your hubs, servers, and users",
};

export default function DashboardPage() {
  return (
    <Suspense fallback={<AnimatedDashboardSkeleton />}>
      <DashboardContent />
    </Suspense>
  );
}

async function DashboardContent() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard");
  }

  // Get user's hubs
  const userHubs = await getUserHubs(session.user.id);

  // Get date for filtering recent activity
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  // Get user's hub IDs
  const userHubIds = userHubs.map((hub) => hub.id);

  // Get connections for user's hubs
  const connections = await db.connection.count({
    where: {
      hubId: { in: userHubIds },
      connected: true,
    },
  });

  // Get servers connected to user's hubs (unique count)
  const connectedServers = await db.connection.groupBy({
    by: ["serverId"],
    where: {
      hubId: { in: userHubIds },
      connected: true,
    },
  });

  // Get active hubs count (user's hubs with activity in the last 7 days)
  const activeHubs = userHubs.filter(
    (hub) => new Date(hub.lastActive) >= oneWeekAgo,
  ).length;

  // Get total message count across user's hubs
  const totalMessages = userHubs.reduce((sum, hub) => {
    // We'll use connections as a proxy for message activity
    return sum + (hub.connections?.length || 0);
  }, 0);

  // Calculate average messages per day (rough estimate)
  const avgMessagesPerDay = Math.round(totalMessages / 7); // Assuming weekly activity

  const stats = {
    totalHubs: userHubs.length,
    totalConnections: connections,
    totalServers: connectedServers.length,
    activeHubs,
    messagesPerDay: avgMessagesPerDay,
  };

  return (
    <div className="space-y-8">
      {/* Animated Welcome Hero */}
      <AnimatedWelcome user={session.user} />

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <AnimatedStatCard
          title="Your Hubs"
          value={stats.totalHubs.toLocaleString()}
          description="Hubs you own or moderate"
          iconName="MessageSquare"
          index={0}
          color="purple"
        />
        <AnimatedStatCard
          title="Connections"
          value={stats.totalConnections.toLocaleString()}
          description="Active channel connections"
          iconName="BarChart3"
          index={1}
          color="blue"
        />
        <AnimatedStatCard
          title="Connected Servers"
          value={stats.totalServers.toLocaleString()}
          description="Discord servers in your hubs"
          iconName="Server"
          index={2}
          color="indigo"
        />
        <AnimatedStatCard
          title="Active Hubs"
          value={stats.activeHubs.toLocaleString()}
          description="Hubs with recent activity"
          iconName="Activity"
          index={3}
          color="pink"
        />
      </div>

      {/* Main Dashboard Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Recent Activity and Quick Actions */}
        <div className="lg:col-span-2 space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <AnimatedActivityCard hubs={userHubs} />
            <OnboardingQuickActions />
          </div>

          {/* Recent Notifications */}
          <RecentNotifications />
        </div>

        {/* Right Column - System Status */}
        <div className="lg:col-span-1">
          <SystemStatus />
        </div>
      </div>
    </div>
  );
}
