import { auth } from "@/auth";
import { redirect } from "next/navigation";
import type { ReactNode } from "react";

export default async function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/admin");
  }

  // Check if the user has the specific admin ID
  const ADMIN_USER_ID = "701727675311587358";

  if (session.user.id !== ADMIN_USER_ID) {
    // Redirect non-admin users to the dashboard
    redirect("/dashboard");
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
          Admin Dashboard
        </h1>
      </div>
      {children}
    </div>
  );
}
