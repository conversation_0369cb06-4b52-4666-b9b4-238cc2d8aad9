import { auth } from "@/auth";
import { getUserHubs } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { redirect } from "next/navigation";
import { ReactNode } from "react";

export default async function AppealsLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/appeals`);
  }

  // Check if user has moderator permissions on any hub
  const userHubs = await getUserHubs(session.user.id);
  const hasModeratorAccess = userHubs.some(
    (hub) => hub.permissionLevel >= PermissionLevel.MODERATOR
  );

  if (!hasModeratorAccess) {
    // User doesn't have moderator permissions on any hub
    redirect("/dashboard");
  }

  return <>{children}</>;
}
