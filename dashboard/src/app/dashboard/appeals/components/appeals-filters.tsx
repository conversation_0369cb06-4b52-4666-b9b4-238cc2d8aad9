"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, RotateCcw, Search } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";

interface Hub {
  id: string;
  name: string;
  iconUrl: string | null;
}

interface AppealsFiltersCardProps {
  searchParams: {
    status?: string;
    userId?: string;
    infractionId?: string;
    hubId?: string;
    page?: string;
  };
  hubs: Hub[];
}

export function AppealsFiltersCard({ searchParams, hubs }: AppealsFiltersCardProps) {
  const router = useRouter();
  const currentSearchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState<string>("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Update URL with search parameters
    const params = new URLSearchParams(currentSearchParams.toString());

    // Preserve hubId if it exists
    if (!searchParams.hubId) {
      params.delete("hubId");
    }

    // Clear previous search parameters
    params.delete("userId");
    params.delete("infractionId");

    if (searchQuery.startsWith("user:")) {
      const userId = searchQuery.substring(5).trim();
      params.set("userId", userId);
    } else if (searchQuery.startsWith("infraction:")) {
      const infractionId = searchQuery.substring(11).trim();
      params.set("infractionId", infractionId);
    } else {
      // If no prefix, assume it's an infraction ID
      if (searchQuery) {
        params.set("infractionId", searchQuery);
      }
    }

    // Reset page to 1 when searching
    params.set("page", "1");

    // Navigate to the new URL
    router.push(`/dashboard/appeals?${params.toString()}`);
  };

  const resetFilters = () => {
    router.push("/dashboard/appeals");
  };

  const handleStatusChange = (value: string) => {
    const params = new URLSearchParams(currentSearchParams.toString());
    if (value === "all") {
      params.delete("status");
    } else {
      params.set("status", value);
    }
    params.set("page", "1");
    router.push(`/dashboard/appeals?${params.toString()}`);
  };

  const handleHubChange = (value: string) => {
    const params = new URLSearchParams(currentSearchParams.toString());
    if (value === "all") {
      params.delete("hubId");
    } else {
      params.set("hubId", value);
    }
    params.set("page", "1");
    router.push(`/dashboard/appeals?${params.toString()}`);
  };

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm text-gray-400 mb-1 block">Status</label>
            <Select
              value={searchParams.status || "all"}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger className="bg-[#0a0a0c] border-gray-800">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent className="bg-[#0f1117] border-gray-800">
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="ACCEPTED">Accepted</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm text-gray-400 mb-1 block">Hub</label>
            <Select
              value={searchParams.hubId || "all"}
              onValueChange={handleHubChange}
            >
              <SelectTrigger className="bg-[#0a0a0c] border-gray-800">
                <SelectValue placeholder="All Hubs" />
              </SelectTrigger>
              <SelectContent className="bg-[#0f1117] border-gray-800 max-h-[200px]">
                <SelectItem value="all">All Hubs</SelectItem>
                {hubs.map((hub) => (
                  <SelectItem key={hub.id} value={hub.id}>
                    {hub.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="md:col-span-2">
            <label className="text-sm text-gray-400 mb-1 block">
              Filter by Infraction or ID
            </label>
            <form onSubmit={handleSearch} className="flex flex-wrap gap-2">
              <div className="flex-1 min-w-[200px]">
                <Input
                  placeholder="user:123456789 or infraction:abc123"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 w-full"
                />
              </div>
              <div className="flex gap-2">
                <Button type="submit" variant="secondary" size="icon">
                  <Search className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={resetFilters}
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
