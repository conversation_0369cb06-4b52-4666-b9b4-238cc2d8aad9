"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useUpdateAppealStatus } from "@/hooks/use-appeals";
import { Check, Loader2, X } from "lucide-react";

interface AppealActionButtonsProps {
  appealId: string;
}

export function AppealActionButtons({ appealId }: AppealActionButtonsProps) {
  const { mutate, isPending } = useUpdateAppealStatus();

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="border-red-500/30 text-red-500 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 flex-1 sm:flex-initial"
        onClick={() => mutate({ appealId, status: "REJECTED" })}
        disabled={isPending}
      >
        {isPending ? (
          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
        ) : (
          <X className="h-4 w-4 mr-1" />
        )}
        Reject
      </Button>
      <Button
        variant="default"
        size="sm"
        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-600/80 hover:to-green-700/80 border-none flex-1 sm:flex-initial"
        onClick={() => mutate({ appealId, status: "ACCEPTED" })}
        disabled={isPending}
      >
        {isPending ? (
          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
        ) : (
          <Check className="h-4 w-4 mr-1" />
        )}
        Accept
      </Button>
    </>
  );
}
