"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { CardFooter } from "@/components/ui/card";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

interface AppealsPaginationCardProps {
  currentPage: number;
  totalPages: number;
}

export function AppealsPaginationCard({
  currentPage,
  totalPages
}: AppealsPaginationCardProps) {
  const router = useRouter();
  const currentSearchParams = useSearchParams();

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(currentSearchParams.toString());
    params.set("page", newPage.toString());
    router.push(`/dashboard/appeals?${params.toString()}`);
  };

  return (
    <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t border-gray-800/50 pt-4">
      <div className="text-sm text-gray-400 order-2 sm:order-1">
        Page {currentPage} of {totalPages}
      </div>
      <div className="flex gap-2 w-full sm:w-auto order-1 sm:order-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white flex-1 sm:flex-initial"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white flex-1 sm:flex-initial"
        >
          Next
          <ArrowRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </CardFooter>
  );
}
