import { auth } from '@/auth';
import { UserSettingsForm } from '@/components/dashboard/settings/user-settings-form';
import { UnderlinedTabs } from '@/components/dashboard/underlined-tabs';
import { KofiSection } from '@/components/KofiSection';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
import { db } from '@/lib/prisma';
import { Metadata } from 'next';
import Image from 'next/image';
import { redirect } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Settings | InterChat Dashboard',
  description: 'Manage your InterChat settings and preferences',
};

export default async function SettingsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect('/login?callbackUrl=/dashboard/settings');
  }

  // Fetch user data from database to get current settings
  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      mentionOnReply: true,
      locale: true,
      showNsfwHubs: true,
      email: true,
      donationEmail: true,
    },
  });

  // Fetch Ko-fi donations and pending claims server-side
  const recentDonations = await db.donation.findMany({
    where: {
      discordUserId: session.user.id,
      processed: true,
    },
    orderBy: { kofiTimestamp: 'desc' },
    take: 10,
    select: {
      id: true,
      amount: true,
      currency: true,
      fromName: true,
      kofiTimestamp: true,
      processed: true,
    },
  });

  if (!user?.email && !user?.donationEmail) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
        <p className="text-gray-400">
          You need to link your email to claim Ko-fi donations. Please{' '}
          <a href="/dashboard/settings/donation-email" className="text-blue-500 hover:underline">
            set your donation email
          </a>
          .
        </p>
      </div>
    );
  }

  const pendingClaims = await db.pendingClaim.findMany({
    where: {
      OR: [{ email: user?.email ?? '' }, { email: (user.donationEmail || user?.email)! }],
      claimed: false,
      expiresAt: { gt: new Date() },
    },
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      tierName: true,
      amount: true,
      currency: true,
      fromName: true,
      expiresAt: true,
      createdAt: true,
    },
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
      </div>
      <p className="text-gray-400 -mt-4">Manage your account settings and preferences</p>

      <UnderlinedTabs
        defaultValue="account"
        className="space-y-6"
        tabs={[
          {
            value: 'account',
            label: 'Account',
            color: 'indigo',
          },
        ]}
      >
        <TabsContent value="account" className="space-y-6">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">Profile Information</CardTitle>
              <CardDescription className="text-gray-400">
                Your account information from Discord
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Image
                    src={session.user.image || 'https://api.dicebear.com/7.x/shapes/svg?seed=user'}
                    alt={session.user.name || 'User'}
                    width={80}
                    height={80}
                    className="rounded-full border-2 border-indigo-500/30"
                  />
                  <div className="absolute bottom-0 right-0 h-4 w-4 rounded-full bg-green-500 border-2 border-gray-900"></div>
                </div>
                <div>
                  <h3 className="text-lg font-medium">{session.user.name}</h3>
                  <p className="text-xs text-gray-500 mt-1">User ID: {session.user.id}</p>
                </div>
              </div>
              <div className="pt-4">
                <p className="text-sm text-gray-400">
                  Your profile information is managed through Discord. To update your profile
                  picture or username, please do so through Discord.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl font-semibold">Account Preferences</CardTitle>
              <CardDescription className="text-gray-400">
                Manage your account settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserSettingsForm
                userId={session.user.id}
                initialMentionOnReply={user?.mentionOnReply ?? true}
                initialLocale={user?.locale ?? null}
                initialShowNsfwHubs={user?.showNsfwHubs ?? false}
              />
            </CardContent>
          </Card>

          <KofiSection pendingClaims={pendingClaims} recentDonations={recentDonations} />
        </TabsContent>
      </UnderlinedTabs>
    </div>
  );
}
