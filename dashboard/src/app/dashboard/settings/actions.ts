'use server';

import { auth } from '@/auth';
import { db } from '@/lib/prisma';
import { revalidatePath } from 'next/cache';

export async function claimPendingSubscription(claimId: string) {
  const session = await auth();
  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  try {
    // Find the pending claim
    const pendingClaim = await db.pendingClaim.findFirst({
      where: {
        id: claimId,
        claimed: false,
        expiresAt: { gt: new Date() },
      },
    });

    if (!pendingClaim) {
      throw new Error('Pending claim not found or expired');
    }

    // Determine tier
    let donationTier: 'SUPPORTER' | null = null;
    if (pendingClaim.tierName) {
      switch (pendingClaim.tierName.toLowerCase()) {
        case 'supporter':
          donationTier = 'SUPPORTER';
          break;
      }

      if (!donationTier) {
        throw new Error('Unable to determine tier from pending claim');
      }

      // Process the claim in a transaction
      await db.$transaction(async (tx) => {
        // Mark claim as processed
        await tx.pendingClaim.update({
          where: { id: pendingClaim.id },
          data: {
            claimed: true,
            claimedBy: session.user.id,
            claimedAt: new Date(),
          },
        });

        // Find and link the existing donation record
        const existingDonation = await tx.donation.findFirst({
          where: {
            kofiTransactionId: pendingClaim.kofiTransactionId,
          },
        });

        if (existingDonation) {
          // Link the donation to the user and mark as processed
          await tx.donation.update({
            where: { id: existingDonation.id },
            data: {
              discordUserId: session.user.id,
              processed: true,
            },
          });
        }

        // Grant premium tier (30 days)
        const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

        // Find the SUPPORTER tier
        const supporterTier = await tx.donationTierDefinition.findUnique({
          where: { name: 'SUPPORTER' },
        });

        if (!supporterTier) {
          throw new Error('SUPPORTER tier not found');
        }

        await tx.user.upsert({
          where: { id: session.user.id },
          create: {
            id: session.user.id,
            donationTierId: supporterTier.id,
            donationExpiresAt: expiresAt,
          },
          update: {
            donationTierId: supporterTier.id,
            donationExpiresAt: expiresAt,
          },
        });
      });

      // Revalidate the settings page to show updated data
      revalidatePath('/dashboard/settings');

      return {
        success: true,
        message: `Successfully claimed ${pendingClaim.tierName} tier subscription!`,
      };
    }
  } catch (error) {
    console.error('Error processing pending claim:', error);
    throw new Error('Failed to process claim');
  }
}

export async function claimManualSubscription(email: string, transactionId: string) {
  const session = await auth();
  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  // Input validation
  if (!email || !transactionId) {
    throw new Error('Email and transaction ID are required');
  }

  if (typeof email !== 'string' || typeof transactionId !== 'string') {
    throw new Error('Invalid input types');
  }

  const sanitizedEmail = email.toLowerCase().trim();
  const sanitizedTransactionId = transactionId.trim();

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(sanitizedEmail)) {
    throw new Error('Invalid email format');
  }

  // Validate transaction ID format (UUID)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(sanitizedTransactionId)) {
    throw new Error('Invalid transaction ID format');
  }

  try {
    // First check if there's a pending claim with this email and transaction ID
    const pendingClaim = await db.pendingClaim.findFirst({
      where: {
        email: sanitizedEmail,
        kofiTransactionId: sanitizedTransactionId,
        claimed: false,
        expiresAt: { gt: new Date() },
      },
    });

    if (pendingClaim) {
      // Use the pending claim logic
      return await claimPendingSubscription(pendingClaim.id);
    }

    // If no pending claim found, check if there's an unprocessed donation record
    const donation = await db.donation.findFirst({
      where: {
        email: sanitizedEmail,
        kofiTransactionId: sanitizedTransactionId,
        discordUserId: null, // Not yet claimed
        processed: false,
      },
    });

    if (!donation) {
      throw new Error(
        'No matching unclaimed Ko-fi subscription found with the provided email and transaction ID. Please verify your information.',
      );
    }

    // Determine tier from donation amount
    let donationTier: 'SUPPORTER' | null = null;

    // Only amount-based determination now (no tierName field)
    if (donation.amount >= 1.99) {
      donationTier = 'SUPPORTER'; // SUPPORTER is the only tier now
    }

    if (!donationTier) {
      throw new Error('This donation does not qualify for premium tier benefits');
    }

    // Grant premium and link donation to user
    await db.$transaction(async (tx) => {
      // Update donation to link to user and mark as processed
      await tx.donation.update({
        where: { id: donation.id },
        data: {
          discordUserId: session.user.id,
          processed: true,
        },
      });

      // Grant premium tier (30 days)
      const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      // Find the SUPPORTER tier
      const supporterTier = await tx.donationTierDefinition.findUnique({
        where: { name: 'SUPPORTER' },
      });

      if (!supporterTier) {
        throw new Error('SUPPORTER tier not found');
      }

      await tx.user.upsert({
        where: { id: session.user.id },
        create: {
          id: session.user.id,
          donationTierId: supporterTier.id,
          donationExpiresAt: expiresAt,
        },
        update: {
          donationTierId: supporterTier.id,
          donationExpiresAt: expiresAt,
        },
      });
    });

    // Revalidate the settings page to show updated data
    revalidatePath('/dashboard/settings');

    return {
      success: true,
      message: `Successfully claimed ${donationTier.toLowerCase()} tier subscription!`,
    };
  } catch (error) {
    console.error('Error processing manual claim:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to process manual claim');
  }
}
