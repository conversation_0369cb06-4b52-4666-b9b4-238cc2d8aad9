import { getServers } from '@/actions/server-actions';
import { auth } from '@/auth';
import { ConnectionEditFormClient } from '@/components/dashboard/connections/connection-edit-form-client';
import { ConnectionNavigationTabs } from '@/components/dashboard/connections/connection-navigation-tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { db } from '@/lib/prisma';
import { ArrowLeft, Trash } from 'lucide-react';
import type { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';

interface ConnectionEditPageProps {
  params: Promise<{
    connectionId: string;
  }>;
}

export async function generateMetadata(props: ConnectionEditPageProps): Promise<Metadata> {
  const { connectionId } = await props.params;
  const connection = await getConnectionForEdit(connectionId);

  if (!connection) {
    return {
      title: 'Connection Not Found - InterChat',
    };
  }

  return {
    title: `Edit ${connection.server?.name || 'Connection'} - InterChat`,
    description: `Edit connection settings for ${connection.server?.name || 'this server'} in ${connection.hub.name}`,
  };
}

async function getConnectionForEdit(connectionId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect('/login');
  }

  // Get connection with related data
  const connection = await db.connection.findUnique({
    where: { id: connectionId },
    include: {
      hub: true,
      server: true,
    },
  });

  if (!connection) {
    return null;
  }

  // Check if user has permission to edit this connection
  // User needs Discord server "Manage Channels" permission ONLY
  // Hub moderators/owners no longer have access to edit individual connections
  try {
    const serversResult = await getServers();

    if ('data' in serversResult) {
      const userServers = serversResult.data;
      // getServers() already filters for servers where user has Manage Channels permission (0x10)
      const hasServerAccess = userServers.some((server) =>
        server.connections.some((conn) => conn.id === connectionId),
      );

      if (hasServerAccess) {
        return connection;
      }
    }
  } catch (error) {
    console.error('Error checking server access:', error);
    // If we can't check server permissions due to rate limiting, deny access for security
  }

  // User doesn't have permission to edit this connection
  // Only Discord server moderators with "Manage Channels" permission can access
  return null;
}

export default async function ConnectionEditPage(props: ConnectionEditPageProps) {
  const { connectionId } = await props.params;
  const connection = await getConnectionForEdit(connectionId);

  if (!connection) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href={`/dashboard/connections/${connectionId}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Connection
            </Link>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Edit Connection</h1>
            <p className="text-sm text-gray-400 mt-1">Configure connection settings</p>
          </div>
        </div>
      </div>

      <ConnectionNavigationTabs connectionId={connectionId} currentTab="edit" />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
        <div className="md:col-span-2">
          <ConnectionEditFormClient connection={connection} />
        </div>

        <div className="space-y-6">
          {/* Hub Information Card */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-6 py-4">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-purple-500/10 border border-purple-500/20">
                  <Image
                    src={connection.hub.iconUrl || '/default-hub-icon.png'}
                    alt={connection.hub.name}
                    width={20}
                    height={20}
                    className="rounded"
                  />
                </div>
                <CardTitle className="text-lg">Connected Hub</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-white">{connection.hub.name}</h4>
                    <p className="text-sm text-gray-400 mt-1">{connection.hub.description}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Server Information Card */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-6 py-4">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                  {connection.server?.iconUrl ? (
                    <Image
                      src={connection.server.iconUrl}
                      alt={connection.server.name || 'Server'}
                      width={20}
                      height={20}
                      className="rounded"
                    />
                  ) : (
                    <div className="w-5 h-5 bg-gray-600 rounded" />
                  )}
                </div>
                <CardTitle className="text-lg">Discord Server</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-white">
                      {connection.server?.name || 'Unknown Server'}
                    </h4>
                    <p className="text-sm text-gray-400 mt-1">Channel: #{connection.channelId}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border border-red-800/50 bg-gradient-to-b from-red-900/20 to-red-950/20 backdrop-blur-sm">
            <CardHeader className="px-6 py-4">
              <CardTitle className="text-lg text-red-400">Danger Zone</CardTitle>
              <CardDescription className="text-red-300/70">
                Irreversible and destructive actions
              </CardDescription>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <div className="p-4 rounded-lg border border-red-800/50 bg-red-900/10">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-white mb-1">Delete Connection</h4>
                    <p className="text-sm text-gray-400">
                      Permanently remove this connection. This action cannot be undone.
                    </p>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-white"
                    asChild
                  >
                    <Link href={`/dashboard/connections/${connectionId}/delete`}>
                      <Trash className="h-4 w-4 mr-2" />
                      Delete
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
