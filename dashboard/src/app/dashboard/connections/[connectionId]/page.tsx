import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON>Content,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, Edit, Globe, Server, ExternalLink } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { ConnectionNavigationTabs } from "@/components/dashboard/connections/connection-navigation-tabs";
import { ConnectionOverview } from "@/components/dashboard/connections/connection-overview";
import { auth } from "@/auth";
import { redirect, notFound } from "next/navigation";
import { db } from "@/lib/prisma";
import type { Metadata } from "next";


export async function generateMetadata(props: {
  params: Promise<{ connectionId: string }>;
}): Promise<Metadata> {
  const params = await props.params;
  return {
    title: `Connection ${params.connectionId} | InterChat Dashboard`,
    description: "View and manage this connection",
  };
}

// Server-side function to fetch connection data
async function getConnectionData(connectionId: string) {
  const session = await auth();
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/connections/" + connectionId);
  }

  const connection = await db.connection.findUnique({
    where: { id: connectionId },
    select: {
      id: true,
      channelId: true,
      parentId: true,
      serverId: true,
      hubId: true,
      connected: true,
      compact: true,
      invite: true,
      createdAt: true,
      embedColor: true,
      lastActive: true,
      webhookURL: true,
      hub: {
        select: {
          id: true,
          name: true,
          description: true,
          ownerId: true,
          iconUrl: true,
          bannerUrl: true,
          welcomeMessage: true,
          private: true,
          locked: true,
          appealCooldownHours: true,
          lastActive: true,
          settings: true,
          createdAt: true,
          updatedAt: true,
          nsfw: true,
          verified: true,
          partnered: true,
          language: true,
          region: true,
          rules: true,
        },
      },
      server: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
          createdAt: true,
          premiumStatus: true,
          updatedAt: true,
          inviteCode: true,
          messageCount: true,
          lastMessageAt: true,
        },
      },
    },
  });

  if (!connection) {
    notFound();
  }

  // Check if user has permission to view this connection
  // User needs Discord server "Manage Channels" permission ONLY
  // Hub moderators/owners no longer have access to individual connection edit pages
  try {
    const { getServers } = await import("@/actions/server-actions");
    const serversResult = await getServers();

    if ("data" in serversResult) {
      const userServers = serversResult.data;
      // getServers() already filters for servers where user has Manage Channels permission (0x10)
      // So if the server is in the list, the user has the required permissions
      const hasServerAccess = userServers.some(server =>
        server.connections.some(conn => conn.id === connectionId)
      );

      if (hasServerAccess) {
        return connection;
      }
    }
  } catch (error) {
    console.error("Error checking server access:", error);
    // If we can't check server permissions due to rate limiting, deny access for security
  }

  // User doesn't have permission to view this connection
  // Only Discord server moderators with "Manage Channels" permission can access
  notFound();
}

export default async function ConnectionPage(props: {
  params: Promise<{ connectionId: string }>;
}) {
  const params = await props.params;
  const connection = await getConnectionData(params.connectionId);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/servers?tab=connections">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Connections
            </Link>
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Connection Details</h1>
            <p className="text-sm text-gray-400 mt-1">View and manage this connection</p>
          </div>
        </div>
        <Button
          asChild
          size="sm"
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none text-white font-medium px-6"
        >
          <Link href={`/dashboard/connections/${params.connectionId}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Connection
          </Link>
        </Button>
      </div>
      <ConnectionNavigationTabs connectionId={params.connectionId} currentTab="overview" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
        <div className="md:col-span-2">
          <ConnectionOverview connection={connection} />
        </div>

        <div className="space-y-6">
          {/* Hub Information Card */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-6 py-4">
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-purple-400" />
                <CardTitle className="text-lg">Connected Hub</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors">
                <div className="flex items-center gap-3">
                  <Image
                    src={connection.hub.iconUrl || "/pfp1.png"}
                    alt={connection.hub.name || "Hub"}
                    width={48}
                    height={48}
                    className="rounded-full border border-gray-700"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-white">{connection.hub.name}</div>
                    <div className="text-sm text-gray-400 line-clamp-2">{connection.hub.description}</div>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-800">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full border-gray-700 bg-gray-800/50 hover:bg-gray-700/50"
                    asChild
                  >
                    <Link href={`/hubs/${connection.hub.id}`} target="_blank">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Hub Details
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Server Information Card */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-6 py-4">
              <div className="flex items-center gap-2">
                <Server className="h-5 w-5 text-indigo-400" />
                <CardTitle className="text-lg">Discord Server</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors">
                <div className="flex items-center gap-3">
                  <Image
                    src={`https://api.dicebear.com/7.x/identicon/svg?seed=${connection.server.id}`}
                    alt={connection.server.name || "Server"}
                    width={48}
                    height={48}
                    className="rounded-full border border-gray-700"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-white">{connection.server.name}</div>
                    <div className="text-sm text-gray-400">Discord Server</div>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-800">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full border-gray-700 bg-gray-800/50 hover:bg-gray-700/50"
                    asChild
                  >
                    <Link href={`/dashboard/servers/${connection.server.id}`}>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Manage Server
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader className="px-6 py-4">
              <div className="flex items-center gap-2">
                <Edit className="h-5 w-5 text-blue-400" />
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <Button
                asChild
                className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none text-white font-medium"
              >
                <Link href={`/dashboard/connections/${params.connectionId}/edit`}>
                  <Edit className="h-5 w-5 mr-2" />
                  Edit Connection
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
