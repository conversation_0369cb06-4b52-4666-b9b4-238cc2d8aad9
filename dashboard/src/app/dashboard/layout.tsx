import { auth } from "@/auth";
import { DashboardLayoutContent } from "@/components/dashboard/layout-content";
import { DashboardLayoutProvider } from "@/components/dashboard/layout-provider";
import { EnhancedOnboardingModal } from "@/components/dashboard/onboarding/enhanced-onboarding-modal";
import { GuidedTourProvider } from "@/components/dashboard/onboarding/guided-tour-provider";
import { OnboardingProvider } from "@/components/dashboard/onboarding/onboarding-provider";
import { DashboardSidebar } from "@/components/dashboard/sidebar";
import { getUserHubs } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { redirect } from "next/navigation";
import type { ReactNode } from "react";
import "./dashboard.css";

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard");
  }

  // Get user's hubs and connections for onboarding state
  const userHubs = await getUserHubs(session.user.id);
  const connections = await db.connection.count({
    where: {
      hubId: { in: userHubs.map((hub) => hub.id) },
      connected: true,
    },
  });

  return (
    <DashboardLayoutProvider>
      <OnboardingProvider
        userHubCount={userHubs.length}
        userConnectionCount={connections}
      >
        <GuidedTourProvider>
          <div className="min-h-screen bg-[#0a0a0c]">
            {/* Dashboard layout with sidebar and main content */}
            <div className="flex h-screen overflow-hidden">
              {/* Sidebar */}
              <DashboardSidebar />

              {/* Main content area with dynamic layout */}
              <DashboardLayoutContent user={session.user}>
                {children}
              </DashboardLayoutContent>
            </div>

            {/* Enhanced Onboarding Modal */}
            <EnhancedOnboardingModal />
          </div>
        </GuidedTourProvider>
      </OnboardingProvider>
    </DashboardLayoutProvider>
  );
}
