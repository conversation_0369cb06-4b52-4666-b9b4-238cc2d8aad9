import { NextResponse } from "next/server";
import { checkRedisHealth, getRedisStatus } from "@/lib/redis-config";

/**
 * Redis health check endpoint
 * GET /api/health/redis
 */
async function handleGET() {
  try {
    const health = await checkRedisHealth();
    const status = getRedisStatus();
    
    const response = {
      redis: {
        ...health,
        status,
      },
      rateLimiting: {
        backend: health.connected ? 'redis' : 'memory',
        fallbackAvailable: true,
      },
      timestamp: new Date().toISOString(),
    };
    
    // Return 200 if Redis is connected, 503 if using fallback
    const httpStatus = health.connected ? 200 : 503;
    
    return NextResponse.json(response, { status: httpStatus });
    
  } catch (error) {
    console.error('Redis health check failed:', error);
    
    return NextResponse.json(
      {
        redis: {
          connected: false,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        rateLimiting: {
          backend: 'memory',
          fallbackAvailable: true,
        },
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}

export const GET = handleGET;
