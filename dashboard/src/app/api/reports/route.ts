import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { Prisma, ReportStatus } from "@/lib/generated/prisma/client";
import { getUserHubPermission } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const hubId = searchParams.get("hubId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    if (!hubId) {
      return NextResponse.json({ error: "Hub ID is required" }, { status: 400 });
    }

    // Check if user has moderator permissions for this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);

    if (permissionLevel < PermissionLevel.MODERATOR) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    // Build where clause
    const where = {
      hubId,
    } as Prisma.ReportWhereInput;

    if (status && status !== "all") {
      where.status = status.toUpperCase() as ReportStatus;
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Fetch reports with all necessary relations
    const [reports, total] = await Promise.all([
      db.report.findMany({
        where,
        include: {
          hub: {
            select: {
              id: true,
              name: true,
              iconUrl: true,
            },
          },
          reporter: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          reportedUser: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          handler: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: offset,
        take: limit,
      }),
      db.report.count({ where }),
    ]);

    // Enhance reports with message and server data
    const reportsWithEnhancedData = await Promise.all(
      reports.map(async (report) => {
        let messageData = null;
        let serverData = null;

        // Fetch message data if messageId exists (with content sanitization)
        if (report.messageId) {
          try {
            const message = await db.message.findUnique({
              where: { id: report.messageId },
              select: {
                id: true,
                content: true,
                imageUrl: true,
                channelId: true,
                guildId: true,
                authorId: true,
                createdAt: true,
                reactions: true,
                referredMessageId: true,
              },
            });

            // Include full message content for proper report review
            if (message) {
              messageData = {
                ...message,
                // Keep full content for thorough report review
                // Content is already sanitized by Prisma and database constraints
              };
            }
          } catch (error) {
            console.error(`Failed to fetch message ${report.messageId}:`, error);
          }
        }

        // Fetch server data from ServerData model
        if (report.reportedServerId) {
          try {
            serverData = await db.serverData.findUnique({
              where: { id: report.reportedServerId },
              select: {
                id: true,
                name: true,
                iconUrl: true,
                // Don't include invite code for security
              },
            });
          } catch (error) {
            console.error(`Failed to fetch server ${report.reportedServerId}:`, error);
          }
        }

        return {
          ...report,
          messageData,
          serverData,
        };
      })
    );

    return NextResponse.json({
      reports: reportsWithEnhancedData,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error("Error fetching reports:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { reportId, status, resolution } = body;

    if (!reportId || !status) {
      return NextResponse.json(
        { error: "Report ID and status are required" },
        { status: 400 }
      );
    }

    // Get the report to check permissions
    const report = await db.report.findUnique({
      where: { id: reportId },
      select: { hubId: true },
    });

    if (!report) {
      return NextResponse.json({ error: "Report not found" }, { status: 404 });
    }

    // Check if user has moderator permissions for this hub
    const permissionLevel = await getUserHubPermission(session.user.id, report.hubId);

    if (permissionLevel < PermissionLevel.MODERATOR) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    // Update the report
    const updatedReport = await db.report.update({
      where: { id: reportId },
      data: {
        status: status.toUpperCase(),
        handledBy: session.user.id,
        handledAt: new Date(),
        ...(resolution && { resolution }),
      },
      include: {
        hub: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
        reporter: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        reportedUser: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        handler: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({ report: updatedReport });
  } catch (error) {
    console.error("Error updating report:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
