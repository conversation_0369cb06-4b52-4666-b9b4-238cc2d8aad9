import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";
import { withCriticalRateLimit, withPublicRateLimit } from "@/lib/rate-limit-middleware";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Schema for creating an announcement
const createAnnouncementSchema = z.object({
  title: z.string().min(1).max(100),
  content: z.string().min(1).max(1000),
  thumbnailUrl: z.string().url().optional(),
  imageUrl: z.string().url().optional(),
});

// GET all announcements
async function handleGET(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _request: NextRequest
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all announcements ordered by creation date (newest first)
    const announcements = await db.announcement.findMany({
      select: {
        id: true,
        title: true,
        content: true,
        thumbnailUrl: true,
        imageUrl: true,
        createdAt: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(announcements);
  } catch (error) {
    console.error("Error fetching announcements:", error);
    return NextResponse.json(
      { error: "Failed to fetch announcements" },
      { status: 500 }
    );
  }
}

// POST (create) a new announcement
async function handlePOST(
  request: NextRequest
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has the specific admin ID
    const ADMIN_USER_ID = "701727675311587358";

    if (session.user.id !== ADMIN_USER_ID) {
      return NextResponse.json(
        { error: "Only authorized administrators can create announcements" },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate the request body
    const validationResult = createAnnouncementSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid announcement data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Create the announcement
    const announcement = await db.announcement.create({
      data: validationResult.data,
    });

    return NextResponse.json(announcement, { status: 201 });
  } catch (error) {
    console.error("Error creating announcement:", error);
    return NextResponse.json(
      { error: "Failed to create announcement" },
      { status: 500 }
    );
  }
}

// DELETE an announcement
async function handleDELETE(
  request: NextRequest
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the user has the specific admin ID
    const ADMIN_USER_ID = "701727675311587358";

    if (session.user.id !== ADMIN_USER_ID) {
      return NextResponse.json(
        { error: "Only authorized administrators can delete announcements" },
        { status: 403 }
      );
    }

    const searchParams = new URL(request.url).searchParams;
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Announcement ID is required" },
        { status: 400 }
      );
    }

    // Delete the announcement
    await db.announcement.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting announcement:", error);
    return NextResponse.json(
      { error: "Failed to delete announcement" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withPublicRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.ANNOUNCEMENTS.LIST,
});

export const POST = withCriticalRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.ANNOUNCEMENTS.CREATE,
  customMessage: "Announcement creation is heavily rate limited. Please wait before creating another announcement.",
});

export const DELETE = withCriticalRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.ANNOUNCEMENTS.DELETE,
  customMessage: "Announcement deletion is heavily rate limited. Please wait before deleting another announcement.",
});
