import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

async function handlePOST(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the hub exists
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      select: { id: true },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    const { rating, text } = await request.json();

    // Validate inputs
    if (!rating || !text) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Create review
    const review = await db.hubReview.create({
      data: {
        rating,
        text,
        userId: session.user.id,
        hubId,
      },
    });

    return NextResponse.json({ review }, { status: 201 });
  } catch (error) {
    console.error("Error creating review:", error);
    return NextResponse.json(
      { error: "Failed to create review" },
      { status: 500 },
    );
  }
}

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const { hubId } = await props.params;

    // Check if the hub exists
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      select: { id: true },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    const reviews = await db.hubReview.findMany({
      where: { hubId },
      select: {
        id: true,
        rating: true,
        text: true,
        createdAt: true,
        updatedAt: true,

        user: {
          select: {
            id: true,
            name: true,
            image: true,

          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(reviews);
  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 },
    );
  }
}

async function handleDELETE(request: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const searchParams = new URL(request.url).searchParams;
  const reviewId = searchParams.get("reviewId");

  if (!reviewId) {
    return NextResponse.json(
      { error: "Review ID is required" },
      { status: 400 },
    );
  }

  try {
    // First check if the review belongs to the user
    const review = await db.hubReview.findUnique({
      where: { id: reviewId },

    });

    if (!review) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    if (review.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Delete the review
    await db.hubReview.delete({
      where: { id: reviewId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting review:", error);
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handlers
export const POST = withStrictRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.REVIEW,
  customMessage: "Review creation rate limit exceeded. Please wait before submitting another review.",
});

export const DELETE = withStrictRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.REVIEW,
  customMessage: "Review deletion rate limit exceeded. Please wait before deleting another review.",
});
