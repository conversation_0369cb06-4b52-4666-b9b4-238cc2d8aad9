import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { withAuthRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

type Props = {
  params: Promise<{ hubId: string }>;
};

async function handlePOST(request: NextRequest, { params }: Props) {
  const session = await auth();

  // The ID is stored in token.sub for JWT strategy
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { hubId } = await params;

  try {
    // Check if the hub exists
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      select: { id: true },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }
    const existingUpvote = await db.hubUpvote.findUnique({
      where: {
        hubId_userId: {
          hubId,
          userId, // Using the extracted userId
        },
      },

    });

    if (existingUpvote) {
      // Remove upvote
      await db.hubUpvote.delete({
        where: {
          hubId_userId: {
            hubId,
            userId, // Using the extracted userId
          },
        },
      });
      return NextResponse.json({ upvoted: false });
    } else {
      // Add upvote
      await db.hubUpvote.create({
        data: {
          hubId,
          userId, // Using the extracted userId
        },
      });
      return NextResponse.json({ upvoted: true });
    }
  } catch (error) {
    console.error("Error handling hub upvote:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handler
export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.UPVOTE,
  customMessage: "Upvote rate limit exceeded. Please wait before voting again.",
});
