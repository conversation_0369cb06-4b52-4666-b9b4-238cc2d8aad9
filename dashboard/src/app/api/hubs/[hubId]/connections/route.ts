import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await params;

    // Check if user has permission to view this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);

    // Get hub details to check if it's private
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      select: { private: true },
    });

    // If hub is private and user doesn't have permission, return 404
    if (hub?.private && permissionLevel === PermissionLevel.NONE) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Get connections for this hub
    const connections = await db.connection.findMany({
      where: { hubId },
      select: {
        id: true,
        serverId: true,
        channelId: true,
        connected: true,
        compact: true,
        createdAt: true,
        lastActive: true,
        invite: true,
        hubId: true,
        embedColor: true,
        server: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
      },
      orderBy: { lastActive: "desc" },
    });

    return NextResponse.json({ connections });
  } catch (error) {
    console.error("Error fetching hub connections:", error);
    return NextResponse.json(
      { error: "Failed to fetch connections" },
      { status: 500 },
    );
  }
}
