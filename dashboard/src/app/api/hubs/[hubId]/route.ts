import { auth } from '@/auth';
import { PermissionLevel } from '@/lib/constants';
import { getUserHubPermission } from '@/lib/permissions';
import { db } from '@/lib/prisma';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';
import {
    withAuthRateLimit,
    withCriticalRateLimit,
    withStrictRateLimit,
} from '@/lib/rate-limit-middleware';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Schema for updating a hub
const updateHubSchema = z.object({
  name: z.string().min(3).max(32).optional(),
  description: z.string().min(10).max(500).optional(),
  private: z.boolean().optional(),
  nsfw: z.boolean().optional(),
  welcomeMessage: z.string().max(1000).nullable().optional(),
  rules: z.array(z.string()).optional(),
  language: z.string().min(2).max(10).optional(),
});

async function handleGET(request: NextRequest, props: { params: Promise<{ hubId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Get the hub first to check if it's public
    const hubBasic = await db.hub.findUnique({
      where: { id: hubId },
      select: {
        private: true,
      },
    });

    if (!hubBasic) {
      return NextResponse.json({ error: 'Hub not found' }, { status: 404 });
    }

    // Only check permissions for private hubs
    if (hubBasic.private) {
      const permissionLevel = await getUserHubPermission(session.user.id, hubId);
      if (permissionLevel === PermissionLevel.NONE) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
    }

    // Get the hub
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        moderators: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!hub) {
      return NextResponse.json({ error: 'Hub not found' }, { status: 404 });
    }

    // Add isOwner flag and premium status to the response
    const isOwner = hub.owner?.id === session.user.id;
    const userDonation = await db.user.findUnique({
      where: { id: session.user.id },
      select: { donationTier: true },
    });

    const hasPremium = userDonation?.donationTier?.name === 'SUPPORTER';

    const hubWithOwnerFlag = {
      ...hub,
      isOwner: !!isOwner,
      hasPremium,
    };

    return NextResponse.json({ hub: hubWithOwnerFlag });
  } catch (error) {
    console.error('Error fetching hub:', error);
    return NextResponse.json({ error: 'Failed to fetch hub' }, { status: 500 });
  }
}

async function handlePATCH(request: NextRequest, props: { params: Promise<{ hubId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to edit this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the hub
    const hub = await db.hub.findUnique({
      where: { id: hubId },
    });

    if (!hub) {
      return NextResponse.json({ error: 'Hub not found' }, { status: 404 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateHubSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 },
      );
    }

    const {
      name,
      description,
      private: isPrivate,
      nsfw,
      welcomeMessage,
      rules,
      language,
    } = validation.data;

    // Check premium status if name is being changed
    if (name && name.toLowerCase() !== hub.name.toLowerCase()) {
      // Only hub owners can change names, and they need premium
      if (hub.ownerId !== session.user.id) {
        return NextResponse.json(
          { error: 'Only hub owners can change the hub name' },
          { status: 403 },
        );
      }

      const userDonation = await db.user.findUnique({
      where: { id: session.user.id },
      select: { donationTier: true },
    });

    const hasPremium = userDonation?.donationTier?.name === 'SUPPORTER';
      if (!hasPremium) {
        return NextResponse.json(
          {
            error: 'Premium feature required',
            message: 'Hub name customization is available to Supporter tier and above ($1.99/month)',
            upgradeUrl: 'https://ko-fi.com/interchat',
          },
          { status: 402 }, // Payment Required
        );
      }
    }

    // Check if the name is already taken (if it's being changed)
    if (name && name.toLowerCase() !== hub.name.toLowerCase()) {
      const existingHub = await db.hub.findFirst({
        where: {
          name: { equals: name, mode: 'insensitive' },
          id: { not: hubId }, // Exclude current hub
        },
      });

      if (existingHub) {
        return NextResponse.json({ error: 'Hub name already taken' }, { status: 400 });
      }
    }

    // Update the hub
    const updatedHub = await db.hub.update({
      where: { id: hubId },
      data: {
        ...(name && { name }),
        ...(description && { description }),
        ...(isPrivate !== undefined && { private: isPrivate }),
        ...(nsfw !== undefined && { nsfw }),
        ...(welcomeMessage !== undefined && { welcomeMessage }),
        ...(rules && { rules }),
        ...(language && { language }),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({ hub: updatedHub });
  } catch (error) {
    console.error('Error updating hub:', error);
    return NextResponse.json({ error: 'Failed to update hub' }, { status: 500 });
  }
}

// Schema for deleting a hub
const deleteHubSchema = z.object({
  confirmName: z.string(),
});

async function handleDELETE(request: NextRequest, props: { params: Promise<{ hubId: string }> }) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to delete this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.OWNER) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the hub
    const hub = await db.hub.findUnique({
      where: { id: hubId },
    });

    if (!hub) {
      return NextResponse.json({ error: 'Hub not found' }, { status: 404 });
    }
    if (hub.ownerId !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate the request body
    let confirmName = '';
    try {
      const body = await request.json();
      const validation = deleteHubSchema.safeParse(body);
      if (validation.success) {
        confirmName = validation.data.confirmName;
      }
    } catch {
      // If no body is provided, continue without confirmation
      console.warn('No confirmation provided for hub deletion');
    }

    // Verify the confirmation name matches the hub name
    if (confirmName !== hub.name) {
      return NextResponse.json(
        { error: 'Confirmation name does not match hub name' },
        { status: 400 },
      );
    }

    // hell
    await db.hubModerator.deleteMany({ where: { hubId } });
    await db.connection.deleteMany({ where: { hubId } });
    await db.hubReview.deleteMany({ where: { hubId } });
    await db.hubUpvote.deleteMany({ where: { hubId } });
    await db.hubRulesAcceptance.deleteMany({ where: { hubId } });
    await db.hubInvite.deleteMany({ where: { hubId } });
    await db.hubLogConfig.deleteMany({ where: { hubId } });
    await db.blockWord.deleteMany({ where: { hubId } });
    await db.appeal.deleteMany({ where: { infraction: { hubId } } });
    await db.infraction.deleteMany({ where: { hubId } });
    await db.hub.delete({ where: { id: hubId } });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting hub:', error);
    return NextResponse.json({ error: 'Failed to delete hub' }, { status: 500 });
  }
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.GET_DETAILS,
});

export const PATCH = withStrictRateLimit(handlePATCH, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.UPDATE,
  customMessage: 'Hub update rate limit exceeded. Please wait before updating the hub again.',
});

export const DELETE = withCriticalRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.DELETE,
  customMessage:
    'Hub deletion is heavily rate limited. Please wait before attempting to delete another hub.',
});
