import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get hubs where user is owner
    const ownedHubs = await db.hub.findMany({
      where: { ownerId: session.user.id },
      select: {
        id: true,
        name: true,
        iconUrl: true,
      },

    });

    // Get hubs where user is a moderator or manager
    const moderatedHubs = await db.hubModerator.findMany({
      where: { userId: session.user.id },
      select: {
        hub: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
      },

    });

    // Combine the hubs and remove duplicates
    const allHubs = [...ownedHubs, ...moderatedHubs.map((mod) => mod.hub)];

    // Remove duplicates by ID
    const uniqueHubs = Array.from(
      new Map(allHubs.map((hub) => [hub.id, hub])).values(),
    );

    // Sort by name
    uniqueHubs.sort((a, b) => a.name.localeCompare(b.name));

    return NextResponse.json({ hubs: uniqueHubs });
  } catch (error) {
    console.error("Error fetching accessible hubs:", error);
    return NextResponse.json(
      { error: "Failed to fetch accessible hubs" },
      { status: 500 },
    );
  }
}
