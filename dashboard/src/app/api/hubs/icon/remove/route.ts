import { auth } from "@/auth";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { db } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await request.json();

    if (!hubId) {
      return NextResponse.json(
        { error: "Missing hub ID" },
        { status: 400 }
      );
    }

    // Check permissions
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get current hub
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      select: { id: true, name: true },
    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Generate a new default icon URL using the hub name
    const defaultIconUrl = `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(hub.name)}`;

    // Update hub to use default icon
    await db.hub.update({
      where: { id: hubId },
      data: { iconUrl: defaultIconUrl },
    });

    return NextResponse.json({
      success: true,
      iconUrl: defaultIconUrl,
      message: "Icon reset to default successfully",
    });
  } catch (error) {
    console.error("Error removing icon:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
