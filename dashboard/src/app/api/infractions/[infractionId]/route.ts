import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { z } from "zod";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { withAuthRateLimit, withStrictRateLimit, withCriticalRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

// Schema for updating an infraction
const updateInfractionSchema = z.object({
  status: z.enum(["ACTIVE", "REVOKED", "APPEALED"]).optional(),
  reason: z.string().min(3).max(500).optional(),
  expiresAt: z.string().nullable().optional(),
});

// GET a specific infraction
async function handleGET(
  request: NextRequest,
  props: { params: Promise<{ infractionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { infractionId } = await props.params;

    // Get the infraction with appeals count
    const infraction = await db.infraction.findUnique({
      where: { id: infractionId },
      include: {
        hub: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
            ownerId: true,
            moderators: {
              where: { userId: session.user.id },
              select: { userId: true },
            },
          },
        },
        moderator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        appeals: {
          select: {
            id: true,
          },
        },
      },

    });

    if (!infraction) {
      return NextResponse.json(
        { error: "Infraction not found" },
        { status: 404 },
      );
    }

    // Check if the user has permission to view this infraction
    const hasPermission =
      infraction.hub.ownerId === session.user.id ||
      infraction.hub.moderators.length > 0;

    if (!hasPermission) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    return NextResponse.json({ infraction });
  } catch (error) {
    console.error("Error fetching infraction:", error);
    return NextResponse.json(
      { error: "Failed to fetch infraction" },
      { status: 500 },
    );
  }
}

// PATCH (update) an infraction
async function handlePATCH(
  request: NextRequest,
  props: { params: Promise<{ infractionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { infractionId } = await props.params;

    // Get the infraction
    const infraction = await db.infraction.findUnique({
      where: { id: infractionId },
      include: {
        hub: true,
      },

    });

    if (!infraction) {
      return NextResponse.json(
        { error: "Infraction not found" },
        { status: 404 },
      );
    }

    // Check if the user has permission to update this infraction
    const permissionLevel = await getUserHubPermission(
      session.user.id,
      infraction.hubId,
    );
    if (permissionLevel < PermissionLevel.MODERATOR) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateInfractionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { status, reason, expiresAt } = validation.data;

    // Update the infraction
    const updatedInfraction = await db.infraction.update({
      where: { id: infractionId },
      data: {
        ...(status && { status }),
        ...(reason && { reason }),
        ...(expiresAt !== undefined && {
          expiresAt: expiresAt ? new Date(expiresAt) : null,
        }),
        ...(status === "APPEALED" && {
          appealedAt: new Date(),
          appealedBy: session.user.id,
        }),
      },
      include: {
        hub: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
          },
        },
        moderator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({ infraction: updatedInfraction });
  } catch (error) {
    console.error("Error updating infraction:", error);
    return NextResponse.json(
      { error: "Failed to update infraction" },
      { status: 500 },
    );
  }
}

// DELETE an infraction
async function handleDELETE(
  request: NextRequest,
  props: { params: Promise<{ infractionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { infractionId } = await props.params;

    // Get the infraction
    const infraction = await db.infraction.findUnique({
      where: { id: infractionId },
      include: {
        hub: true,
      },

    });

    if (!infraction) {
      return NextResponse.json(
        { error: "Infraction not found" },
        { status: 404 },
      );
    }

    // Check if the user has permission to delete this infraction
    const permissionLevel = await getUserHubPermission(
      session.user.id,
      infraction.hubId,
    );
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Delete the infraction
    await db.infraction.delete({
      where: { id: infractionId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting infraction:", error);
    return NextResponse.json(
      { error: "Failed to delete infraction" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.LIST_INFRACTIONS,
});

export const PATCH = withStrictRateLimit(handlePATCH, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.UPDATE_INFRACTION,
  customMessage: "Infraction update rate limit exceeded. Please wait before updating another infraction.",
});

export const DELETE = withCriticalRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.MODERATION.DELETE_INFRACTION,
  customMessage: "Infraction deletion is heavily rate limited. Please wait before deleting another infraction.",
});
