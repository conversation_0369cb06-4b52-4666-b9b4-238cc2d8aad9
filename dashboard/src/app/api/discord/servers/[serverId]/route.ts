import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { REST } from "@discordjs/rest";
import { APIGuild, Routes } from "discord-api-types/v10";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ serverId: string }> },
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = await props.params;

    // Validate that the serverId is a valid Discord ID format (numeric)
    if (!/^\d+$/.test(serverId)) {
      return NextResponse.json(
        { error: "Invalid Discord server ID format" },
        { status: 400 },
      );
    }

    // Get bot token from environment variable
    const botToken = process.env.DISCORD_BOT_TOKEN;
    if (!botToken) {
      console.error("Discord bot token not configured");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 500 },
      );
    }

    // Create REST instance
    const rest = new REST({ version: "10" }).setToken(botToken);

    try {
      // Fetch guild from Discord API
      const guild = (await rest.get(Routes.guild(serverId))) as APIGuild;

      return NextResponse.json({
        guild: {
          id: guild.id,
          name: guild.name,
          icon: guild.icon
            ? `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png`
            : null,
          features: guild.features,
          memberCount: guild.approximate_member_count,
        },
      });
    } catch (discordError) {
      // If Discord returns 404, the server doesn't exist
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((discordError as any).status === 404) {
        return NextResponse.json(
          { error: "Server not found on Discord" },
          { status: 404 },
        );
      }

      // For other errors
      console.error("Discord API error:", discordError);
      return NextResponse.json(
        { error: "Failed to fetch server from Discord" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error fetching Discord server:", error);
    return NextResponse.json(
      { error: "Failed to fetch Discord server" },
      { status: 500 },
    );
  }
}
