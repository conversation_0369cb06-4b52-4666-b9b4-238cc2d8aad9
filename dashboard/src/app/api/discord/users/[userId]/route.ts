import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { withAuthRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

async function handleGET(
  request: NextRequest,
  props: { params: Promise<{ userId: string }> },
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId } = await props.params;

    // Validate that the userId is a valid Discord ID format (numeric)
    if (!/^\d+$/.test(userId)) {
      return NextResponse.json(
        { error: "Invalid Discord user ID format" },
        { status: 400 },
      );
    }

    // Fetch user from Discord API
    // Note: This requires a bot token with proper permissions
    const discordResponse = await fetch(
      `https://discord.com/api/v10/users/${userId}`,
      {
        headers: {
          Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`,
        },
      },
    );

    if (!discordResponse.ok) {
      // If Discord returns 404, the user doesn't exist
      if (discordResponse.status === 404) {
        return NextResponse.json(
          { error: "User not found on Discord" },
          { status: 404 },
        );
      }

      // For other errors
      return NextResponse.json(
        { error: "Failed to fetch user from Discord" },
        { status: discordResponse.status },
      );
    }

    const discordUser = await discordResponse.json();

    return NextResponse.json({
      user: {
        id: discordUser.id,
        username: discordUser.username,
        discriminator: discordUser.discriminator,
        avatar: discordUser.avatar
          ? `https://cdn.discordapp.com/avatars/${discordUser.id}/${discordUser.avatar}.png`
          : null,
      },
    });
  } catch (error) {
    console.error("Error fetching Discord user:", error);
    return NextResponse.json(
      { error: "Failed to fetch Discord user" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handler
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.DISCORD.GET_USER,
});
