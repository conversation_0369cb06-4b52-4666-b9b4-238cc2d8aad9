import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Discord API URL
const DISCORD_API = "https://discord.com/api/v10";

// Discord guild interface
interface DiscordGuild {
  id: string;
  name: string;
  icon: string | null;
  owner: boolean;
  permissions: string;
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user's Discord account to get the access token
    const account = await db.account.findFirst({
      where: {
        userId: session.user.id,
        provider: "discord",
      },
      select: {
        access_token: true,
        // Never expose other sensitive fields like refresh_token, id_token, etc.
      },
    });

    if (!account || !account.access_token) {
      return NextResponse.json(
        { error: "Discord account not connected" },
        { status: 400 }
      );
    }

    const accessToken = account.access_token;

    // Check if we should only return servers where the user has manage permissions
    const manageable = request.nextUrl.searchParams.get("manageable") === "true";

    // Fetch the user's guilds from Discord
    const userGuildsResponse = await fetch(`${DISCORD_API}/users/@me/guilds`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!userGuildsResponse.ok) {
      return NextResponse.json(
        { error: "Failed to fetch Discord servers" },
        { status: userGuildsResponse.status }
      );
    }

    const userGuilds = (await userGuildsResponse.json()) as DiscordGuild[];

    // If manageable is true, filter guilds where the user has the Manage Server permission (0x20)
    // or is the owner of the server
    let filteredGuilds = userGuilds;
    if (manageable) {
      filteredGuilds = userGuilds.filter((guild: DiscordGuild) => {
        const permissions = BigInt(guild.permissions);
        return (
          guild.owner ||
          // Check for MANAGE_CHANNELS (0x10) or ADMINISTRATOR (0x8) permissions
          (permissions & BigInt(0x10)) === BigInt(0x10) ||
          (permissions & BigInt(0x8)) === BigInt(0x8)
        );
      });
    }

    // Format the response
    const servers = filteredGuilds.map((guild) => ({
      id: guild.id,
      name: guild.name,
      icon: guild.icon
        ? `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png?size=128`
        : null,
      owner: guild.owner,
    }));

    return NextResponse.json({ servers });
  } catch (error) {
    console.error("Error fetching user servers:", error);
    return NextResponse.json(
      { error: "Failed to fetch user servers" },
      { status: 500 }
    );
  }
}
