import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/prisma';
import { z } from 'zod';

const donationEmailActionSchema = z.object({
  action: z.enum(['unlink', 'set_to_oauth']),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validationResult = donationEmailActionSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.format(),
        },
        { status: 400 }
      );
    }

    const { action } = validationResult.data;
    const userId = session.user.id;

    switch (action) {
      case 'unlink':
        // Unlink donation email (set to null)
        await db.user.update({
          where: { id: userId },
          data: { donationEmail: null },
        });
        return NextResponse.json({
          success: true,
          message: 'Donation email unlinked successfully'
        });

      case 'set_to_oauth':
        // Set donation email to OAuth email
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { email: true },
        });

        if (!user?.email) {
          return NextResponse.json(
            { error: 'No OAuth email found' },
            { status: 400 }
          );
        }

        await db.user.update({
          where: { id: userId },
          data: { donationEmail: user.email },
        });

        return NextResponse.json({
          success: true,
          message: 'Donation email set to your OAuth email',
          donationEmail: user.email,
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error managing donation email:', error);
    return NextResponse.json(
      { error: 'Failed to manage donation email' },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch current donation email status
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        email: true,
        donationEmail: true,
      },
    });

    // Get latest donation email if available
    const latestDonation = await db.donation.findFirst({
      where: {
        discordUserId: session.user.id,
        email: { not: null },
      },
      orderBy: { kofiTimestamp: 'desc' },
      select: { email: true },
    });

    return NextResponse.json({
      oAuthEmail: user?.email,
      donationEmail: user?.donationEmail,
      latestDonationEmail: latestDonation?.email,
      canSetToOAuth: !!user?.email,
      canSetToLatestDonation: !!latestDonation?.email,
    });
  } catch (error) {
    console.error('Error fetching donation email status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch donation email status' },
      { status: 500 }
    );
  }
}
