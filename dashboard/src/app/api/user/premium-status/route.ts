import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { withAuthRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";
import { getUserPremiumStatus } from "@/lib/donation-utils";

/**
 * Check if user has Ko-fi Supporter tier (premium status)
 * This endpoint provides premium status information for frontend components
 */

async function checkPremiumStatus(userId: string): Promise<string | null> {
  return await getUserPremiumStatus(userId);
}

async function handleGET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const donationTier = await checkPremiumStatus(session.user.id);
    const hasPremium = !!donationTier; // True if donationTier is not null

    return NextResponse.json({
      hasPremium,
      canCustomizeHubName: hasPremium,
      supporterTier: donationTier ? donationTier.toLowerCase() : null,
    });
  } catch (error) {
    console.error("Error fetching premium status:", error);
    return NextResponse.json(
      { error: "Failed to fetch premium status" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handler
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.USERS.GET_PROFILE,
});
