import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/prisma';
import { withAuthRateLimit } from '@/lib/rate-limit-middleware';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';

/**
 * Get pending Ko-fi claims for the authenticated user
 */
async function handleGET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's email for checking pending claims
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { email: true },
    });

    if (!user?.email) {
      return NextResponse.json({
        pendingClaims: [],
        message: 'No email linked to account - link your email first to check for pending claims',
      });
    }

    // Find pending claims for this user's email
    const pendingClaims = await db.pendingClaim.findMany({
      where: {
        email: user.email,
        claimed: false,
        expiresAt: { gt: new Date() },
      },
      select: {
        id: true,
        tierName: true,
        amount: true,
        currency: true,
        fromName: true,
        expiresAt: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({
      pendingClaims,
      count: pendingClaims.length,
    });
  } catch {
    // SECURITY: Don't log sensitive user data
    console.error('Error fetching pending claims');
    return NextResponse.json({ error: 'Failed to fetch pending claims' }, { status: 500 });
  }
}

/**
 * Claim a specific pending Ko-fi subscription
 */
async function handlePOST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { claimId } = await request.json();
    if (!claimId) {
      return NextResponse.json({ error: 'Claim ID is required' }, { status: 400 });
    }

    // SECURITY: Validate claimId format to prevent injection
    if (typeof claimId !== 'string' || claimId.length < 10 || claimId.length > 30) {
      return NextResponse.json({ error: 'Invalid claim ID format' }, { status: 400 });
    }

    // Get the pending claim
    const pendingClaim = await db.pendingClaim.findUnique({
      where: { id: claimId },
    });

    if (!pendingClaim) {
      return NextResponse.json({ error: 'Claim not found' }, { status: 404 });
    }

    if (pendingClaim.claimed) {
      return NextResponse.json({ error: 'Claim already processed' }, { status: 400 });
    }

    if (pendingClaim.expiresAt < new Date()) {
      return NextResponse.json({ error: 'Claim has expired' }, { status: 400 });
    }

    // Determine donation tier
    let donationTier: 'SUPPORTER' | null = null;
    if (pendingClaim.tierName) {
      switch (pendingClaim.tierName.toLowerCase()) {
        case 'supporter':
          donationTier = 'SUPPORTER';
          break;
      }
    }

    if (!donationTier) {
      return NextResponse.json({ error: 'Unable to determine tier from claim' }, { status: 400 });
    }

    // Process the claim in a transaction
    const result = await db.$transaction(async (tx) => {
      // SECURITY: Double-check claim hasn't been processed by another request
      const freshClaim = await tx.pendingClaim.findUnique({
        where: { id: claimId },
      });

      if (!freshClaim || freshClaim.claimed) {
        throw new Error('Claim was already processed by another request');
      }

      // Mark claim as processed
      await tx.pendingClaim.update({
        where: { id: claimId },
        data: {
          claimed: true,
          claimedBy: session.user.id,
          claimedAt: new Date(),
        },
      });

      // Grant premium tier (30 days)
      const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      // Find the SUPPORTER tier
      const supporterTier = await tx.donationTierDefinition.findUnique({
        where: { name: 'SUPPORTER' },
      });

      if (!supporterTier) {
        throw new Error('SUPPORTER tier not found');
      }

      await tx.user.upsert({
        where: { id: session.user.id },
        create: {
          id: session.user.id,
          donationTierId: supporterTier.id,
          donationExpiresAt: expiresAt,
        },
        update: {
          donationTierId: supporterTier.id,
          donationExpiresAt: expiresAt,
        },
      });

      // Create donation record
      await tx.donation.create({
        data: {
          kofiTransactionId: pendingClaim.kofiTransactionId,
          messageId: `claimed-${pendingClaim.kofiTransactionId}`,
          amount: pendingClaim.amount,
          currency: pendingClaim.currency,
          fromName: pendingClaim.fromName,
          email: pendingClaim.email,
          isPublic: true,
          kofiTimestamp: new Date(),
          discordUserId: session.user.id,
          processed: true,
        },
      });

      return { donationTier: 'SUPPORTER', expiresAt };
    });

    return NextResponse.json({
      success: true,
      message: `Successfully claimed ${pendingClaim.tierName} tier subscription!`,
      tier: result.donationTier,
      expiresAt: result.expiresAt,
    });
  } catch (error) {
    // SECURITY: Don't log sensitive information
    if (error instanceof Error && error.message === 'Claim was already processed by another request') {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }

    // Log for debugging but don't expose details to client
    console.error('Error processing claim');
    return NextResponse.json({ error: 'Failed to process claim' }, { status: 500 });
  }
}

// Apply rate limiting
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.USERS.GET_PROFILE,
});

export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
});
