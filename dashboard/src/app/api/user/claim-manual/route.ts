import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/lib/prisma';
import { withAuthRateLimit } from '@/lib/rate-limit-middleware';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';
import { inferDonationTier } from '@/lib/donation-utils';

/**
 * Manual claim endpoint for Ko-fi subscriptions using email and transaction ID
 */
async function handlePOST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { email, transactionId } = await request.json();

    if (!email || !transactionId) {
      return NextResponse.json(
        {
          error: 'Email and transaction ID are required',
        },
        { status: 400 },
      );
    }

    // SECURITY: Input validation and sanitization
    if (typeof email !== 'string' || typeof transactionId !== 'string') {
      return NextResponse.json({ error: 'Invalid input types' }, { status: 400 });
    }

    const sanitizedEmail = email.toLowerCase().trim();
    const sanitizedTransactionId = transactionId.trim();

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitizedEmail)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    // Validate transaction ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(sanitizedTransactionId)) {
      return NextResponse.json({ error: 'Invalid transaction ID format' }, { status: 400 });
    }

    // First check if there's a pending claim with this email and transaction ID
    const pendingClaim = await db.pendingClaim.findFirst({
      where: {
        email: sanitizedEmail,
        kofiTransactionId: sanitizedTransactionId,
        claimed: false,
        expiresAt: { gt: new Date() },
      },
    });

    if (pendingClaim) {
      // Process the pending claim - infer tier from amount
      const inferredTier = await inferDonationTier(pendingClaim.amount);

      if (!inferredTier) {
        return NextResponse.json(
          {
            error: 'Donation amount does not qualify for any tier',
          },
          { status: 400 },
        );
      }

      // Process the claim in a transaction
      const result = await db.$transaction(async (tx) => {
        // Mark claim as processed
        await tx.pendingClaim.update({
          where: { id: pendingClaim.id },
          data: {
            claimed: true,
            claimedBy: session.user.id,
            claimedAt: new Date(),
          },
        });

        // Find and link the existing donation record
        const existingDonation = await tx.donation.findFirst({
          where: {
            kofiTransactionId: pendingClaim.kofiTransactionId,
          },
        });

        if (existingDonation) {
          // Link the donation to the user and mark as processed
          await tx.donation.update({
            where: { id: existingDonation.id },
            data: {
              discordUserId: session.user.id,
              processed: true,
            },
          });
        }

        // Grant premium tier (30 days)
        const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

        await tx.user.upsert({
          where: { id: session.user.id },
          create: {
            id: session.user.id,
            donationTierId: inferredTier.id,
            donationExpiresAt: expiresAt,
            donationEmail: sanitizedEmail, // Set donation email to the email used for claiming
          },
          update: {
            donationTierId: inferredTier.id,
            donationExpiresAt: expiresAt,
            donationEmail: sanitizedEmail, // Update donation email to the email used for claiming
          },
        });

        console.log(`Pending claim: Set donationEmail to ${sanitizedEmail} for user ${session.user.id}`);

        return { donationTier: inferredTier.name, expiresAt };
      });

      return NextResponse.json({
        success: true,
        message: `Successfully claimed ${pendingClaim.tierName} tier subscription via manual claim!`,
        tier: result.donationTier,
        expiresAt: result.expiresAt,
      });
    }

    // If no pending claim found, check if there's an unprocessed donation record
    const donation = await db.donation.findFirst({
      where: {
        email: sanitizedEmail,
        kofiTransactionId: sanitizedTransactionId,
        discordUserId: null, // Not yet claimed
        processed: false,
      },
    });

    if (!donation) {
      return NextResponse.json(
        {
          error:
            'No matching unclaimed Ko-fi subscription found with the provided email and transaction ID. Please verify your information.',
        },
        { status: 404 },
      );
    }

    // Determine tier from donation amount using utility function
    const inferredDonationTier = await inferDonationTier(donation.amount);

    if (!inferredDonationTier) {
      return NextResponse.json(
        {
          error: 'This donation does not qualify for any premium tier benefits',
        },
        { status: 400 },
      );
    }

    // Grant premium and link donation to user
    const result = await db.$transaction(async (tx) => {
      // Update donation to link to user and mark as processed
      await tx.donation.update({
        where: { id: donation.id },
        data: {
          discordUserId: session.user.id,
          processed: true,
        },
      });

      // Grant premium tier (30 days)
      const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      await tx.user.upsert({
        where: { id: session.user.id },
        create: {
          id: session.user.id,
          donationTierId: inferredDonationTier.id,
          donationExpiresAt: expiresAt,
          donationEmail: sanitizedEmail, // Set donation email to the email used for claiming
        },
        update: {
          donationTierId: inferredDonationTier.id,
          donationExpiresAt: expiresAt,
          donationEmail: sanitizedEmail, // Update donation email to the email used for claiming
        },
      });

      console.log(`Manual claim: Set donationEmail to ${sanitizedEmail} for user ${session.user.id}`);

      return { donationTier: inferredDonationTier.name, expiresAt };
    });

    return NextResponse.json({
      success: true,
      message: `Successfully claimed ${inferredDonationTier.name.toLowerCase()} tier subscription!`,
      tier: result.donationTier,
      expiresAt: result.expiresAt,
    });
  } catch (error) {
    console.error('Error processing manual claim:', error);
    return NextResponse.json(
      {
        error: 'Failed to process manual claim',
      },
      { status: 500 },
    );
  }
}

// Apply rate limiting
export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
});
