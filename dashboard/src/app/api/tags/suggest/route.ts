import { NextRequest, NextResponse } from 'next/server';
import { tagManagementService } from '@/lib/services/TagManagementService';

/**
 * Generate tag suggestions based on hub content
 * POST /api/tags/suggest
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { hubName, hubDescription } = body;

    const suggestions = await tagManagementService.generateTagSuggestions(
      hubName,
      hubDescription
    );

    return NextResponse.json({
      suggestions,
      metadata: {
        count: suggestions.length,
        hubName,
        hubDescription: hubDescription ? 'provided' : 'not provided',
      },
    });

  } catch (error) {
    console.error('Error generating tag suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate tag suggestions' },
      { status: 500 }
    );
  }
}
