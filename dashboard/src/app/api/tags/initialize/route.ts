import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { tagManagementService } from '@/lib/services/TagManagementService';

/**
 * Initialize official tags
 * POST /api/tags/initialize
 */
export async function POST() {
  try {
    const session = await auth();
    
    // Only allow authenticated users to initialize tags
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await tagManagementService.initializeOfficialTags();

    return NextResponse.json({
      success: true,
      message: 'Official tags initialized successfully',
    });

  } catch (error) {
    console.error('Error initializing official tags:', error);
    return NextResponse.json(
      { error: 'Failed to initialize official tags' },
      { status: 500 }
    );
  }
}
