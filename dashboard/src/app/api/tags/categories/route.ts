import { NextResponse } from 'next/server';
import { tagManagementService } from '@/lib/services/TagManagementService';
import { withPublicRateLimit } from '@/lib/rate-limit-middleware';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';

/**
 * Get tags organized by category
 * GET /api/tags/categories
 */
async function handleGET() {
  try {
    const categorizedTags = await tagManagementService.getTagsByCategory();

    return NextResponse.json({
      categories: categorizedTags,
      metadata: {
        totalCategories: Object.keys(categorizedTags).length,
        totalTags: Object.values(categorizedTags).reduce((sum, tags) => sum + tags.length, 0),
      },
    });

  } catch (error) {
    console.error('Error fetching tag categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tag categories' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handler
export const GET = withPublicRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.TAGS.CATEGORIES,
});
