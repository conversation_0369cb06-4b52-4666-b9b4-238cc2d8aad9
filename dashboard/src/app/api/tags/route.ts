import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { tagManagementService } from '@/lib/services/TagManagementService';
import { withPublicRateLimit, withAuthRateLimit } from '@/lib/rate-limit-middleware';
import { ENDPOINT_RATE_LIMITS } from '@/lib/rate-limit-config';

/**
 * Enhanced Tags API for hub discovery and management
 *
 * GET /api/tags - Get popular tags or search tags
 * Query parameters:
 * - search: string (optional) - Search query for tag autocomplete
 * - limit: number (default: 20, max: 100) - Number of tags to return
 * - category: string (optional) - Filter by category
 * - popular: boolean (default: false) - Get popular tags instead of search
 */
async function handleGET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    const search = searchParams.get('search');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20', 10), 100);
    const category = searchParams.get('category');
    const popular = searchParams.get('popular') === 'true';

    let tags;

    if (search && search.length >= 2) {
      // Search for tags (autocomplete)
      tags = await tagManagementService.searchTags(search, limit);
    } else if (popular) {
      // Get popular tags
      tags = await tagManagementService.getPopularTags(limit);
    } else if (category) {
      // Get tags by category
      const categorizedTags = await tagManagementService.getTagsByCategory();
      tags = categorizedTags[category] || [];
    } else {
      // Get all popular tags by default
      tags = await tagManagementService.getPopularTags(limit);
    }

    return NextResponse.json({
      tags,
      metadata: {
        count: tags.length,
        query: search,
        category,
        popular,
        limit,
      },
    });

  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tags' },
      { status: 500 }
    );
  }
}

/**
 * Create new tags or add tags to a hub
 * POST /api/tags
 */
async function handlePOST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { hubId, tags, action = 'add' } = body;

    if (!hubId || !Array.isArray(tags) || tags.length === 0) {
      return NextResponse.json(
        { error: 'Hub ID and tags array are required' },
        { status: 400 }
      );
    }

    if (tags.length > 5) {
      return NextResponse.json(
        { error: 'Maximum 5 tags allowed per hub' },
        { status: 400 }
      );
    }

    // Validate tag names
    const validTags = tags.filter(tag =>
      typeof tag === 'string' &&
      tag.trim().length > 0 &&
      tag.trim().length <= 30
    );

    if (validTags.length === 0) {
      return NextResponse.json(
        { error: 'No valid tags provided' },
        { status: 400 }
      );
    }

    // TODO: Verify user has permission to modify this hub
    // This would check if user is hub owner or moderator

    if (action === 'add') {
      await tagManagementService.addTagsToHub(hubId, validTags);
    } else if (action === 'remove') {
      await tagManagementService.removeTagsFromHub(hubId, validTags);
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "add" or "remove"' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Successfully ${action}ed ${validTags.length} tags`,
      tags: validTags,
    });

  } catch (error) {
    console.error('Error managing tags:', error);
    return NextResponse.json(
      { error: 'Failed to manage tags' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withPublicRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.TAGS.LIST,
});

export const POST = withAuthRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.TAGS.CREATE,
  customMessage: "Tag creation rate limit exceeded. Please wait before adding more tags.",
});
