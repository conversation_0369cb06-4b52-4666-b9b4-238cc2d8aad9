import { auth } from "@/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";

// Discord API endpoints
const DISCORD_API = "https://discord.com/api/v10";



interface DiscordChannel {
  id: string;
  name: string;
  type: number;
  position: number;
  parent_id?: string;
  topic?: string;
  guild_id: string;
}

// Helper function to fetch channels for a specific server
async function fetchServerChannels(serverId: string, botToken: string): Promise<DiscordChannel[]> {
  const response = await fetch(`${DISCORD_API}/guilds/${serverId}/channels`, {
    headers: {
      "Authorization": `Bot ${botToken}`,
    },
  });

  if (!response.ok) {
    if (response.status === 403) {
      return []; // Bot missing access to server
    } else if (response.status === 404) {
      return []; // Server not found
    } else if (response.status === 429) {
      return []; // Rate limited - return empty for now
    }
    throw new Error(`Failed to fetch channels for server ${serverId}: ${response.status}`);
  }

  return await response.json();
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get serverId from query parameters
    const { searchParams } = new URL(request.url);
    const serverId = searchParams.get("serverId");

    if (!serverId) {
      return NextResponse.json(
        { error: "Server ID is required" },
        { status: 400 }
      );
    }

    const botToken = process.env.DISCORD_BOT_TOKEN;
    if (!botToken) {
      return NextResponse.json(
        { error: "Discord bot token not configured" },
        { status: 500 }
      );
    }

    // Verify user has access to this specific server
    const { getServerDetails } = await import("@/actions/server-actions");
    const serverResult = await getServerDetails(serverId);

    if ("error" in serverResult) {
      return NextResponse.json(
        { error: serverResult.error },
        { status: serverResult.status }
      );
    }

    const server = serverResult.data;

    // Get all existing connections to filter out already connected channels
    const existingConnections = await db.connection.findMany({
      select: { channelId: true },
    });
    const connectedChannelIds = new Set(existingConnections.map(conn => conn.channelId));

    // Fetch channels for the specific server only
    try {
      const channels = await fetchServerChannels(serverId, botToken);

      // Filter channels: only text channels (type 0), not already connected
      const validChannels = channels
        .filter(channel => {
          const isTextChannel = channel.type === 0; // Text channels only
          const notAlreadyConnected = !connectedChannelIds.has(channel.id); // Not already connected

          return isTextChannel && notAlreadyConnected;
        })
        .map(channel => ({
          id: channel.id,
          name: channel.name,
          topic: channel.topic,
          serverId: serverId,
          serverName: server.name,
          position: channel.position,
        }));



      // Sort channels by position
      validChannels.sort((a, b) => a.position - b.position);

      return NextResponse.json({
        channels: validChannels,
        serverName: server.name,
        serverId: serverId
      });
    } catch {
      return NextResponse.json(
        { error: "Failed to fetch channels for this server" },
        { status: 500 }
      );
    }
  } catch {
    return NextResponse.json(
      { error: "Failed to fetch channels" },
      { status: 500 }
    );
  }
}
