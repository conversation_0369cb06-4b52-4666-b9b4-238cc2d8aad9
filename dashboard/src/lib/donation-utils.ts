import { db } from '@/lib/prisma';
import type { DonationTierDefinition } from '@/lib/generated/prisma/client';

/**
 * Infer the appropriate donation tier based on the donation amount.
 * Returns the highest tier that the amount qualifies for.
 *
 * @param amount - The donation amount
 * @returns The matching DonationTierDefinition or null if no tier matches
 */
export async function inferDonationTier(amount: number): Promise<DonationTierDefinition | null> {
  const tiers = await db.donationTierDefinition.findMany({
    orderBy: { price: 'desc' }, // Start with highest price tier
  });

  // Find the highest tier that the amount qualifies for
  for (const tier of tiers) {
    if (amount >= tier.price) {
      return tier;
    }
  }

  return null; // Amount doesn't qualify for any tier
}

/**
 * Get a specific donation tier by name.
 * This is useful when you know the exact tier name (e.g., from Ko-fi tier_name).
 *
 * @param tierName - The name of the tier (e.g., "SUPPORTER", "PREMIUM")
 * @returns The matching DonationTierDefinition or null if not found
 */
export async function getDonationTierByName(tierName: string): Promise<DonationTierDefinition | null> {
  return await db.donationTierDefinition.findUnique({
    where: { name: tierName.toUpperCase() }
  });
}

/**
 * Get all available donation tiers, sorted by price.
 *
 * @param ascending - If true, sort by price ascending; if false, descending (default)
 * @returns Array of DonationTierDefinition
 */
export async function getAllDonationTiers(ascending: boolean = false): Promise<DonationTierDefinition[]> {
  return await db.donationTierDefinition.findMany({
    orderBy: { price: ascending ? 'asc' : 'desc' }
  });
}

/**
 * Check if a user currently has premium status (any active donation tier).
 *
 * @param userId - The Discord user ID
 * @returns The user's current tier name or null if no active tier
 */
export async function getUserPremiumStatus(userId: string): Promise<string | null> {
  const user = await db.user.findUnique({
    where: { id: userId },
    select: {
      donationTier: {
        select: { name: true }
      },
      donationExpiresAt: true,
    },
  });

  if (!user || !user.donationTier) {
    return null; // User has no donation tier
  }

  // If donationExpiresAt is null, it's a permanent donation
  if (!user.donationExpiresAt) {
    return user.donationTier.name;
  }

  // Check if the donation is still active
  if (user.donationExpiresAt > new Date()) {
    return user.donationTier.name;
  }

  return null; // Donation has expired
}
