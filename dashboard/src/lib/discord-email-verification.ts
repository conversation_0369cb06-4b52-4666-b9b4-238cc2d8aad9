import { db } from '@/lib/prisma';

/**
 * Discord user profile interface for email verification
 */
interface DiscordUserProfile {
  id: string;
  username: string;
  email: string;
  verified: boolean;
}

/**
 * Email verification result interface
 */
interface EmailVerificationResult {
  success: boolean;
  email?: string;
  error?: string;
  isVerified?: boolean;
}

/**
 * Service for handling Discord Discord email verification
 * Ensures Ko-fi donations are matched to verified Discord accounts only
 */
export class DiscordEmailVerificationService {
  /**
   * Fetch verified Discord email using OAuth access token
   */
  async fetchDiscordUserEmail(accessToken: string): Promise<EmailVerificationResult> {
    try {
      const response = await fetch('https://discord.com/api/v10/users/@me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        console.error(`Discord API error: ${response.status} ${response.statusText}`);
        return {
          success: false,
          error: 'Failed to fetch Discord user information',
        };
      }

      const profile: DiscordUserProfile = await response.json();

      // Ensure email is present and verified
      if (!profile.email) {
        return {
          success: false,
          error: 'Discord account does not have an email address',
        };
      }

      if (!profile.verified) {
        return {
          success: false,
          error: 'Discord email address is not verified',
        };
      }

      return {
        success: true,
        email: profile.email,
        isVerified: true,
      };
    } catch (error) {
      console.error('Error fetching Discord user email:', error);
      return {
        success: false,
        error: 'Failed to verify Discord email',
      };
    }
  }

  /**
   * Update user's verified Discord email in database
   */
  async updateUserVerifiedEmail(userId: string, verifiedEmail: string): Promise<boolean> {
    try {
      // Check if email is already linked to another user
      const existingUser = await db.user.findFirst({
        where: {
          email: verifiedEmail,
          id: { not: userId },
        },
        select: { id: true },
      });

      if (existingUser) {
        console.warn(
          `Email ${verifiedEmail} is already linked to user ${existingUser.id}, cannot link to ${userId}`,
        );
        return false;
      }

      // Update user with verified Discord email
      await db.user.update({
        where: { id: userId },
        data: { email: verifiedEmail },
      });

      console.log(`Updated user ${userId} with verified Discord email: ${verifiedEmail}`);
      return true;
    } catch (error) {
      console.error(`Error updating user ${userId} verified email:`, error);
      return false;
    }
  }

  /**
   * Check if Discord account has email scope
   */
  async checkEmailScope(userId: string): Promise<{ hasEmailScope: boolean; error?: string }> {
    try {
      const account = await db.account.findFirst({
        where: {
          userId: userId,
          provider: 'discord',
        },
        select: {
          access_token: true,
          scope: true,
        },
      });

      if (!account || !account.access_token) {
        return {
          hasEmailScope: false,
          error:
            'Discord account not connected or access token missing. Please re-authorize with Discord.',
        };
      }

      // Check if scope includes email
      const scopes = account.scope?.split(' ') || [];
      if (!scopes.includes('email')) {
        return {
          hasEmailScope: false,
          error: 'Discord email permission not granted. Please re-authorize to grant email access.',
        };
      }

      return { hasEmailScope: true };
    } catch (error) {
      console.error(`Error checking email scope for user ${userId}:`, error);
      return {
        hasEmailScope: false,
        error: 'Failed to check Discord permissions',
      };
    }
  }

  /**
   * Verify and link Discord email for Ko-fi donations
   * This replaces manual email input with secure OAuth verification
   */
  async verifyAndLinkDiscordEmail(userId: string): Promise<EmailVerificationResult> {
    try {
      // First check if user has email scope
      const scopeCheck = await this.checkEmailScope(userId);
      if (!scopeCheck.hasEmailScope) {
        return {
          success: false,
          error: scopeCheck.error || 'Discord email permission required',
        };
      }

      // Get user's Discord account access token
      const account = await db.account.findFirst({
        where: {
          userId: userId,
          provider: 'discord',
        },
        select: {
          access_token: true,
        },
      });

      if (!account || !account.access_token) {
        return {
          success: false,
          error:
            'Discord account not connected or access token missing. Please re-authorize with Discord.',
        };
      }

      // Fetch verified Discord email
      const emailResult = await this.fetchDiscordUserEmail(account.access_token);
      if (!emailResult.success || !emailResult.email) {
        return emailResult;
      }

      // Update user with verified email
      const updateSuccess = await this.updateUserVerifiedEmail(userId, emailResult.email);
      if (!updateSuccess) {
        return {
          success: false,
          error: 'Email is already linked to another Discord account',
        };
      }

      return {
        success: true,
        email: emailResult.email,
        isVerified: true,
      };
    } catch (error) {
      console.error(`Error verifying Discord email for user ${userId}:`, error);
      return {
        success: false,
        error: 'Failed to verify Discord email',
      };
    }
  }

  /**
   * Find Discord user by verified email for Ko-fi webhook processing
   * Only matches users with verified Discord emails
   */
  async findUserByVerifiedEmail(email: string): Promise<string | null> {
    try {
      const user = await db.user.findFirst({
        where: { email: email },
        select: { id: true },
      });

      return user?.id || null;
    } catch (error) {
      console.error(`Error finding user by verified email ${email}:`, error);
      return null;
    }
  }

  /**
   * Process existing unlinked Ko-fi donations for a user
   * Called after successful Discord email verification
   */
  async processExistingDonations(
    userId: string,
    verifiedEmail: string,
  ): Promise<{
    processed: number;
    premiumGranted: boolean;
  }> {
    try {
      // Find unprocessed donations with matching email
      const unprocessedDonations = await db.donation.findMany({
        where: {
          email: verifiedEmail,
          discordUserId: null,
          processed: false,
        },
        select: {
          id: true,
          amount: true,
          currency: true,
        },
      });

      let processedCount = 0;
      const premiumGranted = false;

      for (const donation of unprocessedDonations) {
        // Link donation to Discord user
        await db.donation.update({
          where: { id: donation.id },
          data: {
            discordUserId: userId,
            processed: true,
          },
        });

        processedCount++;

        // Grant premium access for Ko-fi Supporter tier
        // This logic is now handled by the bot based on donation tier
        // if (donation.tierName?.toLowerCase() === 'supporter') {
        //   const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
        //   await prisma.user.update({
        //     where: { id: donation.discordUserId },
        //     data: {
        //       hasMediaPremium: true,
        //       mediaPremiumExpiresAt: expiresAt,
        //     },
        //   });

        //   premiumGranted = true;
        //   console.log(`Premium access granted to user ${donation.discordUserId} from existing donation`);
        // }
      }

      console.log(
        `Processed ${processedCount} existing donations for user ${userId}, premium granted: ${premiumGranted}`,
      );

      return { processed: processedCount, premiumGranted };
    } catch (error) {
      console.error(`Error processing existing donations for user ${userId}:`, error);
      return { processed: 0, premiumGranted: false };
    }
  }
}

/**
 * Singleton instance for Discord email verification service
 */
export const discordEmailVerificationService = new DiscordEmailVerificationService();
