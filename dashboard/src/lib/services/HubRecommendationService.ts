import { db } from '@/lib/prisma';

export interface HubRecommendation {
  hubId: string;
  hub: {
    id: string;
    tags: { name: string }[];
    _count: {
      connections: number;
      messages: number;
      upvotes: number;
    };
    verified: boolean;
    partnered: boolean;
    language: string | null;
    connections: { lastActive: Date }[];
  };
  score: number;
  reason: string;
}

/**
 * Enhanced Hub Recommendation Service for Dashboard
 * Provides comprehensive hub recommendations addressing user retention crisis
 * Focuses on activity indicators, personalized discovery, and community building
 */
export class HubRecommendationService {
  private readonly MAX_RECOMMENDATIONS = 8; // Increased for better discovery

  /**
   * Get personalized hub recommendations for a user
   * Currently uses basic logic - will be enhanced after schema migration
   */
  async getPersonalizedRecommendations(userId: string): Promise<HubRecommendation[]> {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        locale: true,
        activityLevel: true,
        preferredLanguages: true,
        hubEngagementScore: true,
      },
    });

    if (!user) {
      return this.getTrendingHubs();
    }

    const recommendations = await this.generateBasicRecommendations(user);

    return recommendations;
  }

  /**
   * Generate basic recommendations using current schema
   */
  private async generateBasicRecommendations(user: {
    id: string;
    locale: string | null;
  }): Promise<HubRecommendation[]> {
    // Get all public, active hubs with enhanced data
    const hubs = await db.hub.findMany({
      where: {
        private: false,
        locked: false,
      },
      select: {
        id: true,
        tags: true,
        verified: true,
        partnered: true,
        language: true,
        activityMetrics: true,
        _count: {
          select: {
            connections: { where: { connected: true } },
            messages: {
              where: {
                createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
              },
            },
            upvotes: {
              where: {
                createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
              },
            },
          },
        },
        connections: {
          where: { connected: true },
          select: {
            serverId: true,
            lastActive: true,
          },
        },
      },
    });

    // Use all available hubs for now (no favorite filtering until schema migration)
    const availableHubs = hubs;

    const recommendations: HubRecommendation[] = [];

    for (const hub of availableHubs) {
      const score = this.calculateBasicScore(hub, user);
      const reason = this.getBasicReason(hub, user);

      if (score > 0) {
        recommendations.push({
          hubId: hub.id,
          hub,
          score,
          reason,
        });
      }
    }

    // Sort by score and return top recommendations
    return recommendations.sort((a, b) => b.score - a.score).slice(0, this.MAX_RECOMMENDATIONS);
  }

  /**
   * Calculate enhanced hub score with activity indicators and engagement metrics
   */
  private calculateHubScore(
    hub: HubRecommendation['hub'],
    user: {
      interests: string[];
      activityLevel: string;
      preferredLanguages?: string[];
      hubEngagementScore?: number;
    },
  ): number {
    let score = 0;

    // Interest and language matching
    score += this.calculateInterestScore(hub, user);
    score += this.calculateLanguageScore(hub, user);

    // Activity and engagement scoring
    score += this.calculateActivityScore(hub);
    score += this.calculateEngagementScore(hub);

    // Popularity and verification bonuses
    score += this.calculatePopularityScore(hub);
    score += this.calculateVerificationBonus(hub);

    return Math.round(score);
  }

  private calculateInterestScore(
    hub: HubRecommendation['hub'],
    user: { interests: string[] },
  ): number {
    const hubTags = hub.tags?.map((tag) => tag.name.toLowerCase()) || [];
    const userInterests = user.interests.map((interest) => interest.toLowerCase());

    const matchingInterests = hubTags.filter((tag) =>
      userInterests.some((interest) => interest.includes(tag) || tag.includes(interest)),
    );

    return matchingInterests.length > 0 ? matchingInterests.length * 40 : 0;
  }

  private calculateLanguageScore(
    hub: HubRecommendation['hub'],
    user: { preferredLanguages?: string[] },
  ): number {
    if (user.preferredLanguages && hub.language && user.preferredLanguages.includes(hub.language)) {
      return 25;
    }
    return 0;
  }

  private calculateActivityScore(hub: HubRecommendation['hub']): number {
    let score = 0;
    const activeConnections = hub._count?.connections || 0;
    const recentMessages = hub._count?.messages || 0;

    // Connection activity score
    if (activeConnections >= 10) score += 30;
    else if (activeConnections >= 5) score += 20;
    else if (activeConnections >= 2) score += 10;

    // Message activity score
    if (recentMessages >= 100) score += 25;
    else if (recentMessages >= 50) score += 20;
    else if (recentMessages >= 20) score += 15;
    else if (recentMessages >= 5) score += 10;

    return score;
  }

  private calculateEngagementScore(hub: HubRecommendation['hub']): number {
    let score = 0;
    const recentUpvotes = hub._count?.upvotes || 0;

    // Upvotes indicate quality
    if (recentUpvotes >= 10) score += 15;
    else if (recentUpvotes >= 5) score += 10;
    else if (recentUpvotes >= 2) score += 5;

    // Freshness bonus
    const recentlyActiveConnections = hub.connections.filter(
      (conn) => conn.lastActive > new Date(Date.now() - 24 * 60 * 60 * 1000),
    ).length;

    if (recentlyActiveConnections > 0) {
      score += Math.min(recentlyActiveConnections * 3, 15);
    }

    return score;
  }

  private calculatePopularityScore(hub: HubRecommendation['hub']): number {
    return Math.min(hub._count.messages ?? 1 / 2000, 15);
  }

  private calculateVerificationBonus(hub: HubRecommendation['hub']): number {
    let bonus = 0;
    if (hub.verified) bonus += 10;
    if (hub.partnered) bonus += 15;
    return bonus;
  }

  /**
   * Get enhanced human-readable reason for recommendation with activity indicators
   */
  private getRecommendationReason(
    hub: HubRecommendation['hub'],
    user: {
      interests: string[];
      activityLevel: string;
      preferredLanguages?: string[];
    },
  ): string {
    // Check for interest matching first
    const interestReason = this.getInterestMatchReason(hub, user);
    if (interestReason) return interestReason;

    // Check for language matching
    const languageReason = this.getLanguageMatchReason(hub, user);
    if (languageReason) return languageReason;

    // Check for activity-based reasons
    const activityReason = this.getActivityBasedReason(hub);
    if (activityReason) return activityReason;

    // Check for quality/verification reasons
    const qualityReason = this.getQualityBasedReason(hub);
    if (qualityReason) return qualityReason;

    // Fallback reason
    return this.getFallbackReason(hub);
  }

  private getInterestMatchReason(
    hub: HubRecommendation['hub'],
    user: { interests: string[] },
  ): string | null {
    const hubTags = hub.tags?.map((tag) => tag.name.toLowerCase()) || [];
    const userInterests = user.interests.map((interest) => interest.toLowerCase());

    const matchingInterests = hubTags.filter((tag) =>
      userInterests.some((interest) => interest.includes(tag) || tag.includes(interest)),
    );

    if (matchingInterests.length > 0) {
      const activityLevel = this.getActivityLevelDescription(hub);
      return `${matchingInterests.slice(0, 2).join(', ')} • ${activityLevel}`;
    }
    return null;
  }

  private getLanguageMatchReason(
    hub: HubRecommendation['hub'],
    user: { preferredLanguages?: string[] },
  ): string | null {
    if (user.preferredLanguages && hub.language && user.preferredLanguages.includes(hub.language)) {
      const activityLevel = this.getActivityLevelDescription(hub);
      return `${hub.language.toUpperCase()} community • ${activityLevel}`;
    }
    return null;
  }

  private getActivityBasedReason(hub: HubRecommendation['hub']): string | null {
    const activeConnections = hub._count?.connections || 0;
    const recentMessages = hub._count?.messages || 0;

    if (activeConnections >= 10 && recentMessages >= 50) {
      return `Very active community • ${activeConnections} servers, ${recentMessages} recent messages`;
    }

    if (activeConnections >= 5 && recentMessages >= 20) {
      return `Active community • ${activeConnections} servers, ${recentMessages} recent messages`;
    }

    return null;
  }

  private getQualityBasedReason(hub: HubRecommendation['hub']): string | null {
    const recentUpvotes = hub._count.upvotes || 0;

    if (recentUpvotes >= 5) {
      return `Well-rated community • ${recentUpvotes} recent upvotes`;
    }

    if (hub.verified && hub.partnered) {
      return 'Verified & Partnered hub • Trusted community';
    }
    if (hub.verified) {
      return 'Verified hub • Trusted community';
    }
    if (hub.partnered) {
      return 'Partnered hub • Featured community';
    }

    return null;
  }

  private getFallbackReason(hub: HubRecommendation['hub']): string {
    const activeConnections = hub._count?.connections || 0;
    return activeConnections >= 2
      ? `Growing community • ${activeConnections} connected servers`
      : 'Discover new conversations';
  }

  private getActivityLevelDescription(
    hub: HubRecommendation['hub'] & { connections: { lastActive: Date }[] },
  ): string {
    const recentMessages = hub._count?.messages || 0;
    const recentlyActive = hub.connections.filter(
      (conn) => conn.lastActive > new Date(Date.now() - 24 * 60 * 60 * 1000),
    ).length;

    if (recentMessages >= 100 || recentlyActive >= 5) {
      return 'Very Active';
    }
    if (recentMessages >= 20 || recentlyActive >= 2) {
      return 'Active';
    }
    if (recentMessages >= 5 || recentlyActive >= 1) {
      return 'Moderately Active';
    }
    return 'New Community';
  }

  /**
   * Calculate basic hub score using current schema
   */
  private calculateBasicScore(
    hub: HubRecommendation['hub'],
    user: { locale: string | null },
  ): number {
    let score = 0;

    const activeConnections = hub._count?.connections || 0;
    const recentMessages = hub._count?.messages || 0;
    const recentUpvotes = hub._count.upvotes || 0;

    // Activity scoring
    if (activeConnections >= 10) score += 40;
    else if (activeConnections >= 5) score += 30;
    else if (activeConnections >= 2) score += 20;

    if (recentMessages >= 100) score += 35;
    else if (recentMessages >= 50) score += 25;
    else if (recentMessages >= 20) score += 15;
    else if (recentMessages >= 5) score += 10;

    // Quality indicators
    if (recentUpvotes >= 10) score += 20;
    else if (recentUpvotes >= 5) score += 15;
    else if (recentUpvotes >= 2) score += 10;

    // Language matching
    if (user.locale && hub.language) {
      const userLang = user.locale.split('-')[0];
      if (hub.language === userLang) {
        score += 25;
      }
    }

    // Verification bonus
    if (hub.verified) score += 15;
    if (hub.partnered) score += 20;

    // Base popularity
    score += Math.min(hub._count.messages / 1000, 20);

    return Math.round(score);
  }

  /**
   * Generate basic recommendation reason
   */
  private getBasicReason(hub: HubRecommendation['hub'], user: { locale: string | null }): string {
    const activeConnections = hub._count?.connections || 0;
    const recentMessages = hub._count?.messages || 0;
    const recentUpvotes = hub._count.upvotes || 0;

    // Language matching
    if (user.locale && hub.language) {
      const userLang = user.locale.split('-')[0];
      if (hub.language === userLang) {
        const activityLevel = this.getActivityLevel(hub);
        return `${hub.language.toUpperCase()} community • ${activityLevel}`;
      }
    }

    // High activity
    if (activeConnections >= 10 && recentMessages >= 50) {
      return `Very active community • ${activeConnections} servers, ${recentMessages} recent messages`;
    }

    if (activeConnections >= 5 && recentMessages >= 20) {
      return `Active community • ${activeConnections} servers, ${recentMessages} recent messages`;
    }

    // Quality indicators
    if (recentUpvotes >= 5) {
      return `Well-rated community • ${recentUpvotes} recent upvotes`;
    }

    // Verification status
    if (hub.verified && hub.partnered) {
      return 'Verified & Partnered hub • Trusted community';
    }
    if (hub.verified) {
      return 'Verified hub • Trusted community';
    }
    if (hub.partnered) {
      return 'Partnered hub • Featured community';
    }

    // Fallback
    return activeConnections >= 2
      ? `Growing community • ${activeConnections} connected servers`
      : 'Discover new conversations';
  }

  /**
   * Get activity level description (basic version)
   */
  private getActivityLevel(hub: HubRecommendation['hub']): string {
    const recentMessages = hub._count?.messages || 0;
    const activeConnections = hub._count?.connections || 0;

    if (recentMessages >= 100 || activeConnections >= 10) {
      return 'Very Active';
    }
    if (recentMessages >= 20 || activeConnections >= 5) {
      return 'Active';
    }
    if (recentMessages >= 5 || activeConnections >= 2) {
      return 'Moderately Active';
    }
    return 'New Community';
  }

  /**
   * Get trending hubs for users without preferences
   * Uses the same filtering logic as the main trending algorithm
   */
  async getTrendingHubs(limit = 5): Promise<HubRecommendation[]> {
    const now = new Date();
    const trendingWindowStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const hubs = await db.hub.findMany({
      where: {
        private: false,
        locked: false,
        // Require at least one connection AND some activity indicators
        AND: [
          // Must have at least one connection
          { connections: { some: { connected: true } } },
          // AND must have some activity indicators
          {
            OR: [
              // Has recent activity (last 14 days)
              { lastActive: { gte: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000) } },
              // Has engagement
              { upvotes: { some: {} } },
              { reviews: { some: {} } },
              // Is verified/partnered (quality signal)
              { verified: true },
              { partnered: true },
            ],
          },
        ],
      },
      include: {
        tags: true,
        _count: {
          select: {
            connections: {
              where: {
                connected: true,
                lastActive: { gte: trendingWindowStart },
              },
            },
            messages: {
              where: {
                createdAt: { gte: trendingWindowStart },
              },
            },
            upvotes: { where: { createdAt: { gte: trendingWindowStart } } },
            reviews: { where: { createdAt: { gte: trendingWindowStart } } },
          },
        },
        connections: {
          where: { connected: true },
          select: { lastActive: true },
        },
      },
    });

    // Calculate trending scores using the same algorithm as getSortedHubs
    const WEIGHT_RECENT_CONNECTION = 15;
    const WEIGHT_RECENT_UPVOTE = 8;
    const WEIGHT_RECENT_REVIEW = 5;
    const WEIGHT_MESSAGE_FREQUENCY = 12;
    const WEIGHT_CONNECTION_GROWTH = 10;
    const TRENDING_DECAY_BASE = 1.5;
    const TRENDING_DECAY_RATE = 0.4;

    const scoredHubs = hubs.map((hub) => {
      // Recent activity scores
      const recentConnectionsScore = (hub._count?.connections ?? 0) * WEIGHT_RECENT_CONNECTION;
      const recentUpvotesScore = (hub._count?.upvotes ?? 0) * WEIGHT_RECENT_UPVOTE;
      const recentReviewsScore = (hub._count?.reviews ?? 0) * WEIGHT_RECENT_REVIEW;

      // Enhanced message frequency calculation
      const daysSinceLastActive = Math.max(
        0,
        (now.getTime() - hub.lastActive.getTime()) / (1000 * 60 * 60 * 24),
      );
      const messageFrequencyScore =
        daysSinceLastActive < 1
          ? WEIGHT_MESSAGE_FREQUENCY
          : daysSinceLastActive < 3
            ? WEIGHT_MESSAGE_FREQUENCY * 0.7
            : daysSinceLastActive < 7
              ? WEIGHT_MESSAGE_FREQUENCY * 0.4
              : 0;

      // Connection growth rate calculation
      const totalConnections = hub.connections?.length || 0;
      const recentConnections = hub._count?.connections ?? 0;
      const connectionGrowthRate = totalConnections > 0 ? recentConnections / totalConnections : 0;
      const connectionGrowthScore = connectionGrowthRate * WEIGHT_CONNECTION_GROWTH;

      // Enhanced time decay calculation
      const ageInHours = Math.max(1, (now.getTime() - hub.createdAt.getTime()) / (1000 * 60 * 60));
      const timeDecay = 1 / Math.pow(ageInHours + TRENDING_DECAY_BASE, TRENDING_DECAY_RATE);

      // Bonus for verified/partnered hubs
      const verificationBonus = (hub.verified ? 5 : 0) + (hub.partnered ? 8 : 0);

      // Activity requirement bonus - heavily penalize hubs with no real activity
      const totalActivityScore =
        recentConnectionsScore +
        recentUpvotesScore +
        recentReviewsScore +
        messageFrequencyScore +
        connectionGrowthScore;
      const hasConnections = totalConnections > 0;
      const activityMultiplier =
        totalActivityScore > 0 && hasConnections
          ? 1
          : hasConnections
            ? 0.5 // Has connections but no recent activity
            : totalActivityScore > 0
              ? 0.3 // Has activity but no connections
              : 0.05; // No connections and no activity

      // Calculate final trending score
      const baseScore = (totalActivityScore + verificationBonus) * activityMultiplier;
      const trendingScore = baseScore * timeDecay;

      return {
        hub,
        score: trendingScore,
        reason: `Trending hub with ${totalConnections} servers and recent activity`,
      };
    });

    // Sort by trending score and return top results
    const sortedHubs = scoredHubs.sort((a, b) => b.score - a.score).slice(0, limit);

    return sortedHubs.map((item) => ({
      hubId: item.hub.id,
      hub: item.hub,
      score: item.score,
      reason: item.reason,
    }));
  }

  /**
   * Get hubs similar to user's favorites
   */
  async getSimilarHubs(userId: string, limit = 3): Promise<HubRecommendation[]> {
    // TODO: Implement after schema migration with user favorites
    // For now, return trending hubs as similar
    return this.getTrendingHubs(limit);
  }

  /**
   * Get activity-based hub recommendations with enhanced metrics
   */
  async getActivityBasedRecommendations(limit = 5): Promise<HubRecommendation[]> {
    // Get hubs with high recent activity
    const hubs = await db.hub.findMany({
      where: {
        private: false,
        locked: false,
        // Must have connections
        connections: { some: { connected: true } },
        lastActive: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Active in last 24 hours
        },
      },
      include: {
        tags: true,
        _count: {
          select: {
            connections: { where: { connected: true } },
            messages: {
              where: {
                createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
              },
            },
            upvotes: {
              where: {
                createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
              },
            },
          },
        },
        connections: {
          where: { connected: true },
          select: {
            serverId: true,
            lastActive: true,
          },
        },
      },
      orderBy: [{ lastActive: 'desc' }],
      take: limit * 2, // Get more to filter and rank
    });

    // Score and filter hubs based on activity
    const scoredHubs = hubs
      .map((hub) => {
        const recentMessages = hub._count?.messages || 0;
        const activeConnections = hub._count?.connections || 0;
        const recentUpvotes = hub._count?.upvotes || 0;

        // Calculate activity score
        let activityScore = 0;
        activityScore += recentMessages * 2; // Weight recent messages heavily
        activityScore += activeConnections * 5; // Weight connections
        activityScore += recentUpvotes * 3; // Weight quality indicators

        return {
          hubId: hub.id,
          hub,
          score: activityScore,
          reason: `Very active today • ${recentMessages} messages, ${activeConnections} servers`,
        };
      })
      .filter((item) => item.score > 10) // Only include reasonably active hubs
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return scoredHubs;
  }

  /**
   * Invalidate user's recommendation cache (no-op for dashboard)
   */
  async invalidateUserCache(userId: string): Promise<void> {
    // No caching in dashboard, so nothing to invalidate
    console.log(`Cache invalidation requested for user ${userId} (no-op)`);
  }
}

// Export singleton instance
export const hubRecommendationService = new HubRecommendationService();
