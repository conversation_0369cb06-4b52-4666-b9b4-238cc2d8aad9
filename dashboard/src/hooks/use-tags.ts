"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchApi } from "@/lib/api";
import { useErrorNotification } from "./use-error-notification";
import { useToast } from "@/components/ui/use-toast";

// Types
export interface Tag {
  name: string;
  usageCount?: number;
  category?: string;
  isOfficial?: boolean;
  color?: string;
}

export interface TagsResponse {
  tags: Tag[];
  metadata: {
    count: number;
    query?: string;
    category?: string;
    popular?: boolean;
    limit: number;
  };
}

export interface CategorizedTags {
  categories: Record<string, Array<{ name: string; usageCount: number }>>;
  metadata: {
    totalCategories: number;
    totalTags: number;
  };
}

// Query keys
export const tagKeys = {
  all: ['tags'] as const,
  search: (query: string) => [...tagKeys.all, 'search', query] as const,
  popular: (limit: number) => [...tagKeys.all, 'popular', limit] as const,
  categories: () => [...tagKeys.all, 'categories'] as const,
  suggestions: (text: string) => [...tagKeys.all, 'suggestions', text] as const,
};

/**
 * Hook for searching tags (autocomplete)
 */
export function useTagSearch(query: string, options?: { enabled?: boolean }) {
  const queryResult = useQuery({
    queryKey: tagKeys.search(query),
    queryFn: () => searchTags(query),
    enabled: (options?.enabled ?? true) && query.length >= 2,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  useErrorNotification({
    isError: queryResult.isError,
    error: queryResult.error,
    title: "Search Error",
    description: "Failed to search tags",
  });

  return {
    tags: queryResult.data?.tags || [],
    metadata: queryResult.data?.metadata,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
  };
}

/**
 * Hook for getting popular tags
 */
export function usePopularTags(limit = 20) {
  const queryResult = useQuery({
    queryKey: tagKeys.popular(limit),
    queryFn: () => getPopularTags(limit),
    staleTime: 1000 * 60 * 10, // 10 minutes
  });

  useErrorNotification({
    isError: queryResult.isError,
    error: queryResult.error,
    title: "Tags Error",
    description: "Failed to load popular tags",
  });

  return {
    tags: queryResult.data?.tags || [],
    metadata: queryResult.data?.metadata,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
  };
}

/**
 * Hook for getting tags by category
 */
export function useTagCategories() {
  const queryResult = useQuery({
    queryKey: tagKeys.categories(),
    queryFn: getTagCategories,
    staleTime: 1000 * 60 * 15, // 15 minutes
  });

  useErrorNotification({
    isError: queryResult.isError,
    error: queryResult.error,
    title: "Categories Error",
    description: "Failed to load tag categories",
  });

  return {
    categories: queryResult.data?.categories || {},
    metadata: queryResult.data?.metadata,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
  };
}

/**
 * Hook for tag suggestions based on content
 */
export function useTagSuggestions(hubName?: string, hubDescription?: string) {
  const text = `${hubName || ''} ${hubDescription || ''}`.trim();

  const queryResult = useQuery({
    queryKey: tagKeys.suggestions(text),
    queryFn: () => getTagSuggestions(hubName, hubDescription),
    enabled: text.length > 0,
    staleTime: 1000 * 60 * 30, // 30 minutes
  });

  return {
    suggestions: queryResult.data?.suggestions || [],
    metadata: queryResult.data?.metadata,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
  };
}

/**
 * Hook for managing hub tags
 */
export function useHubTagManagement() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const addTagsMutation = useMutation({
    mutationFn: ({ hubId, tags }: { hubId: string; tags: string[] }) =>
      manageHubTags(hubId, tags, 'add'),
    onSuccess: (data, variables) => {
      toast({
        title: "Tags Added",
        description: `Successfully added ${variables.tags.length} tags to the hub`,
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: tagKeys.all });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add tags to hub",
        variant: "destructive",
      });
    },
  });

  const removeTagsMutation = useMutation({
    mutationFn: ({ hubId, tags }: { hubId: string; tags: string[] }) =>
      manageHubTags(hubId, tags, 'remove'),
    onSuccess: (data, variables) => {
      toast({
        title: "Tags Removed",
        description: `Successfully removed ${variables.tags.length} tags from the hub`,
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: tagKeys.all });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to remove tags from hub",
        variant: "destructive",
      });
    },
  });

  return {
    addTagsMutation,
    removeTagsMutation,
    addTags: addTagsMutation.mutate,
    removeTags: removeTagsMutation.mutate,
    isAdding: addTagsMutation.isPending,
    isRemoving: removeTagsMutation.isPending,
    addError: addTagsMutation.error,
    removeError: removeTagsMutation.error,
  };
}

/**
 * Hook for initializing official tags (admin only)
 */
export function useInitializeOfficialTags() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: initializeOfficialTags,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Official tags initialized successfully",
      });

      // Invalidate all tag queries
      queryClient.invalidateQueries({ queryKey: tagKeys.all });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to initialize official tags",
        variant: "destructive",
      });
    },
  });

  return {
    initialize: mutation.mutate,
    isInitializing: mutation.isPending,
    error: mutation.error,
  };
}

// API functions
async function searchTags(query: string): Promise<TagsResponse> {
  const params = new URLSearchParams({
    search: query,
    limit: '20',
  });

  return fetchApi<TagsResponse>(`/api/tags?${params.toString()}`);
}

async function getPopularTags(limit: number): Promise<TagsResponse> {
  const params = new URLSearchParams({
    popular: 'true',
    limit: limit.toString(),
  });

  return fetchApi<TagsResponse>(`/api/tags?${params.toString()}`);
}

async function getTagCategories(): Promise<CategorizedTags> {
  return fetchApi<CategorizedTags>('/api/tags/categories');
}

async function getTagSuggestions(
  hubName?: string,
  hubDescription?: string
): Promise<{ suggestions: string[]; metadata: { basedOn: string; count: number } }> {
  return fetchApi('/api/tags/suggest', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      hubName,
      hubDescription,
    }),
  });
}

async function manageHubTags(
  hubId: string,
  tags: string[],
  action: 'add' | 'remove'
): Promise<{ success: boolean; message: string }> {
  return fetchApi('/api/tags', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      hubId,
      tags,
      action,
    }),
  });
}

async function initializeOfficialTags(): Promise<{ success: boolean; message: string }> {
  return fetchApi('/api/tags/initialize', {
    method: 'POST',
  });
}

/**
 * Utility function to validate tag name
 */
export function validateTagName(name: string): { valid: boolean; error?: string } {
  if (!name || typeof name !== 'string') {
    return { valid: false, error: 'Tag name is required' };
  }

  const trimmed = name.trim();

  if (trimmed.length === 0) {
    return { valid: false, error: 'Tag name cannot be empty' };
  }

  if (trimmed.length > 30) {
    return { valid: false, error: 'Tag name must be 30 characters or less' };
  }

  if (!/^[a-zA-Z0-9\s\-_]+$/.test(trimmed)) {
    return { valid: false, error: 'Tag name can only contain letters, numbers, spaces, hyphens, and underscores' };
  }

  return { valid: true };
}

/**
 * Utility function to get tag color based on category
 */
export function getTagColor(category?: string): string {
  const colors: Record<string, string> = {
    Gaming: '#10B981',
    Technology: '#3B82F6',
    Creative: '#8B5CF6',
    Entertainment: '#F59E0B',
    Social: '#84CC16',
    Learning: '#6366F1',
    Sports: '#14B8A6',
    Other: '#6B7280',
  };

  return colors[category || 'Other'] || colors.Other;
}
