"use client";

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useToast } from '@/components/ui/use-toast';
import { useQuery, useMutation, useQueryClient } from '@/lib/tanstack-query';
import { fetchApi } from '@/lib/api';

interface UserSettings {
  id: string;
  mentionOnReply: boolean;
  locale: string;
  showNsfwHubs: boolean;
}

// Query keys for user settings
export const userSettingsKeys = {
  all: ['userSettings'] as const,
  user: (userId: string) => [...userSettingsKeys.all, userId] as const,
};

// Fetch user settings function
async function fetchUserSettings(userId: string): Promise<UserSettings> {
  const response = await fetchApi<{ user: UserSettings }>(`/api/users/${userId}/settings`);
  return response.user;
}

// Update user settings function
async function updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings> {
  const response = await fetchApi<{ user: UserSettings }>(`/api/users/${userId}/settings`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(settings),
  });
  return response.user;
}

/**
 * Hook to manage user NSFW content preferences
 * Uses React Query for caching and preventing duplicate requests
 */
export function useNSFWPreference() {
  const { data: session, status } = useSession();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUpdating, setIsUpdating] = useState(false);

  // Query for user settings with caching
  const {
    data: userSettings,
    isLoading,
  } = useQuery({
    queryKey: userSettingsKeys.user(session?.user?.id || ''),
    queryFn: () => fetchUserSettings(session!.user!.id),
    enabled: !!session?.user?.id && status === 'authenticated',
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    retry: 1,
  });

  // Mutation for updating NSFW preference
  const updateMutation = useMutation({
    mutationFn: (showNsfwHubs: boolean) =>
      updateUserSettings(session!.user!.id, { showNsfwHubs }),
    onMutate: async (showNsfwHubs) => {
      // Optimistic update
      await queryClient.cancelQueries({
        queryKey: userSettingsKeys.user(session!.user!.id)
      });

      const previousSettings = queryClient.getQueryData(
        userSettingsKeys.user(session!.user!.id)
      );

      queryClient.setQueryData(
        userSettingsKeys.user(session!.user!.id),
        (old: UserSettings | undefined) =>
          old ? { ...old, showNsfwHubs } : undefined
      );

      return { previousSettings };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousSettings) {
        queryClient.setQueryData(
          userSettingsKeys.user(session!.user!.id),
          context.previousSettings
        );
      }
      console.error('Error updating NSFW preference:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update NSFW settings",
        variant: "destructive",
      });
    },
    onSuccess: (data, showNsfwHubs) => {
      toast({
        title: showNsfwHubs ? "NSFW Content Enabled" : "NSFW Content Disabled",
        description: showNsfwHubs
          ? "NSFW hubs will now appear in your search results."
          : "NSFW hubs are now hidden from your search results.",
      });
    },
  });

  // Update NSFW preference
  const updateNSFWPreference = async (showNsfwHubs: boolean): Promise<boolean> => {
    if (!session?.user?.id) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to change NSFW content settings.",
        variant: "destructive",
      });
      return false;
    }

    setIsUpdating(true);

    try {
      await updateMutation.mutateAsync(showNsfwHubs);
      setIsUpdating(false);
      return true;
    } catch {
      setIsUpdating(false);
      return false;
    }
  };

  // Toggle NSFW preference with age verification
  const toggleNSFWPreference = async (skipVerification = false): Promise<boolean> => {
    const currentValue = userSettings?.showNsfwHubs ?? false;
    const newValue = !currentValue;

    // If enabling NSFW and not skipping verification, caller should handle age verification
    if (newValue && !skipVerification) {
      return false; // Caller should show age verification modal
    }

    return await updateNSFWPreference(newValue);
  };

  return {
    showNsfwHubs: userSettings?.showNsfwHubs ?? false,
    isLoading: isLoading && status !== 'loading',
    isUpdating: isUpdating || updateMutation.isPending,
    isAuthenticated: !!session?.user?.id,
    updateNSFWPreference,
    toggleNSFWPreference,
  };
}
