"use client";

import { useQuery, useMutation, useQueryClient } from "@/lib/tanstack-query";
import { fetchApi } from "@/lib/api";
import { useErrorNotification } from "./use-error-notification";
import { useToast } from "@/components/ui/use-toast";

// Types
export interface User {
  id: string;
  name: string | null;
  image: string | null;
}

export interface Moderator {
  id: string;
  userId: string;
  role: "MODERATOR" | "MANAGER";
  user: User;
}

export interface HubMembers {
  owner: User;
  moderators: Moderator[];
}

// Query keys
export const hubMembersKeys = {
  all: ["hubMembers"] as const,
  lists: () => [...hubMembersKeys.all, "list"] as const,
  list: (hubId: string) => ["hubMembers", hubId] as const, // Simplified to match server prefetching
  details: () => [...hubMembersKeys.all, "detail"] as const,
  detail: (id: string) => [...hubMembersKeys.details(), id] as const,
};

// Fetch hub members
export async function getHubMembers(hubId: string): Promise<HubMembers> {
  const response = await fetchApi<{ members: HubMembers }>(`/api/hubs/${hubId}/members`);
  return response.members;
}

// Hook for fetching hub members
export function useHubMembers(hubId: string) {
  const query = useQuery({
    queryKey: hubMembersKeys.list(hubId),
    queryFn: () => getHubMembers(hubId),
  });

  // Handle error notification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to load hub members",
  });

  return query;
}

// Add a member to a hub
export async function addHubMember(hubId: string, data: { userId: string; role: "MODERATOR" | "MANAGER" }) {
  return fetchApi<{ moderator: Moderator }>(`/api/hubs/${hubId}/members`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
}

// Hook for adding a hub member
export function useAddHubMember(hubId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: { userId: string; role: "MODERATOR" | "MANAGER" }) =>
      addHubMember(hubId, data),
    onSuccess: (data, variables) => {
      toast({
        title: "Member Added",
        description: `User has been added as a ${variables.role.toLowerCase()}.`,
      });

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: hubMembersKeys.list(hubId) });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add member",
        variant: "destructive",
      });
    },
  });
}

// Update a member's role
export async function updateMemberRole(hubId: string, memberId: string, role: "MODERATOR" | "MANAGER") {
  return fetchApi<{ moderator: Moderator }>(`/api/hubs/${hubId}/members/${memberId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ role }),
  });
}

// Hook for updating a member's role
export function useUpdateMemberRole(hubId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ memberId, role }: { memberId: string; role: "MODERATOR" | "MANAGER" }) =>
      updateMemberRole(hubId, memberId, role),
    onSuccess: (data, variables) => {
      toast({
        title: "Role Updated",
        description: `Member's role has been updated to ${variables.role.toLowerCase()}.`,
      });

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: hubMembersKeys.list(hubId) });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update role",
        variant: "destructive",
      });
    },
  });
}

// Remove a member
export async function removeMember(hubId: string, memberId: string) {
  return fetchApi(`/api/hubs/${hubId}/members/${memberId}`, {
    method: "DELETE",
  });
}

// Hook for removing a member
export function useRemoveMember(hubId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (memberId: string) => removeMember(hubId, memberId),
    onSuccess: () => {
      toast({
        title: "Member Removed",
        description: "Member has been removed from the hub.",
      });

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: hubMembersKeys.list(hubId) });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove member",
        variant: "destructive",
      });
    },
  });
}
