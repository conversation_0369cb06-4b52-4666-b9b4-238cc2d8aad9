"use client";

import { SortOptions } from "@/app/hubs/constants";
import { fetchApi } from "@/lib/api";
import { useQuery } from "@/lib/tanstack-query";
import { useErrorNotification } from "./use-error-notification";
import { hubKeys } from "./use-hub";

// Interface for search parameters
export interface HubSearchParams {
  search?: string;
  tags?: string[];
  sort?: SortOptions;
  skip?: number;
  take?: number;
}

// Fetch hubs with search parameters
export async function searchHubs(params: HubSearchParams) {
  const searchParams = new URLSearchParams();

  if (params.search) {
    searchParams.append("search", params.search);
  }

  if (params.tags && params.tags.length > 0) {
    searchParams.append("tags", params.tags.join(","));
  }

  if (params.sort) {
    searchParams.append("sort", params.sort);
  }

  if (params.skip !== undefined) {
    searchParams.append("skip", params.skip.toString());
  }

  if (params.take !== undefined) {
    searchParams.append("take", params.take.toString());
  }

  const queryString = searchParams.toString();
  const url = `/api/hubs${queryString ? `?${queryString}` : ""}`;

  return fetchApi(url);
}

// Hook for searching hubs
export function useHubSearch(params: HubSearchParams) {
  const query = useQuery({
    queryKey: hubKeys.list(params),
    queryFn: () => searchHubs(params),
    // Disable automatic refetching when window is focused
    refetchOnWindowFocus: false,
  });

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to search hubs",
  });

  return query;
}
