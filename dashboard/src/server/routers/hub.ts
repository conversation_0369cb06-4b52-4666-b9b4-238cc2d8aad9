/**
 * Hub router for tRPC
 */
import { SortOptions } from '@/app/hubs/constants';
import { buildWhereClause, getSortedHubs } from '@/app/hubs/utils';
import { db } from '@/lib/prisma';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';
import { protectedProcedure, publicProcedure, router } from '../trpc';

// Schema for creating a hub
const createHubSchema = z.object({
  name: z.string().min(3).max(32),
  description: z.string().min(10).max(500),
  shortDescription: z.string().min(10).max(100).optional(),
  private: z.boolean().default(true),
  rules: z.array(z.string()).optional(),
});

// Schema for hub search
const hubSearchSchema = z.object({
  search: z.string().optional(),
  tags: z.array(z.string()).optional(),
  skip: z.number().optional().default(0),
  limit: z.number().optional().default(12),
  sort: z.nativeEnum(SortOptions).optional().default(SortOptions.Trending),
});

export const hubRouter = router({
  // Get a list of hubs with pagination and filtering
  getHubs: publicProcedure.input(hubSearchSchema).query(async ({ input }) => {
    const { search, tags, skip, sort } = input;

    // Build the where clause for filtering
    const whereClause = buildWhereClause({
      search: search || undefined,
      tags: tags || undefined,
    });

    // Get sorted hubs with pagination
    const { hubs, totalCount } = await getSortedHubs(whereClause, skip, sort);

    return {
      hubs,
      pagination: {
        totalItems: totalCount,
        hasMore: skip + hubs.length < totalCount,
      },
    };
  }),

  // Search for hubs by term
  searchHubs: publicProcedure
    .input(z.object({ term: z.string().min(1) }))
    .query(async ({ input }) => {
      const { term } = input;

      if (!term) {
        return { hubs: [] };
      }

      const hubs = await db.hub.findMany({
        where: {
          private: false,
          OR: [
            { name: { contains: term, mode: 'insensitive' } },
            { description: { contains: term, mode: 'insensitive' } },
            { shortDescription: { contains: term, mode: 'insensitive' } },
            { tags: { some: { name: term } } },
          ],
        },
        select: {
          id: true,
          name: true,
          description: true,
          shortDescription: true,
          iconUrl: true,
          bannerUrl: true,
          private: true,
          locked: true,
          nsfw: true,
          verified: true,
          partnered: true,
          language: true,
          region: true,
          createdAt: true,
          lastActive: true,
          connections: {
            where: { connected: true },
            orderBy: { lastActive: 'desc' },
            select: {
              id: true,
              serverId: true,
              connected: true,
              compact: true,
              createdAt: true,
              lastActive: true,
              server: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          upvotes: {
            select: {
              id: true,
              userId: true,
              createdAt: true,
            },
          },
          moderators: {
            select: {
              id: true,
              userId: true,
              role: true,

              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          },
          tags: {
            select: {
              name: true,
            },
          },
          _count: {
            select: {
              connections: { where: { connected: true } },
              upvotes: true,
              reviews: true,
            },
          },
        },
        take: 12,
      });

      return { hubs };
    }),

  // Get hub reviews
  getHubReviews: publicProcedure.input(z.object({ hubId: z.string() })).query(async ({ input }) => {
    const { hubId } = input;

    // Check if the hub exists
    const hub = await db.hub.findUnique({
      where: { id: hubId },
      select: { id: true },
    });

    if (!hub) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Hub not found',
      });
    }

    const reviews = await db.hubReview.findMany({
      where: { hubId },
      select: {
        id: true,
        rating: true,
        text: true,
        createdAt: true,
        updatedAt: true,

        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return reviews;
  }),

  // Create a hub review
  createHubReview: protectedProcedure
    .input(
      z.object({
        hubId: z.string(),
        rating: z.number().min(1).max(5),
        text: z.string().min(1).max(1000),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { hubId, rating, text } = input;
      const userId = ctx.session.user.id;

      // Check if the hub exists
      const hub = await db.hub.findUnique({
        where: { id: hubId },
        select: { id: true },
      });

      if (!hub) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Hub not found',
        });
      }

      // Check if the user has already reviewed this hub
      const existingReview = await db.hubReview.findUnique({
        where: {
          hubId_userId: {
            hubId,
            userId,
          },
        },
      });

      if (existingReview) {
        // Update the existing review
        const updatedReview = await db.hubReview.update({
          where: {
            hubId_userId: {
              hubId,
              userId,
            },
          },
          data: {
            rating,
            text,
          },
          select: {
            id: true,
            rating: true,
            text: true,
            createdAt: true,
            updatedAt: true,

            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        });

        return updatedReview;
      }
      // Create a new review
      const newReview = await db.hubReview.create({
        data: {
          hubId,
          userId,
          rating,
          text,
        },
        select: {
          id: true,
          rating: true,
          text: true,
          createdAt: true,
          updatedAt: true,

          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });

      return newReview;
    }),

  // Get a single hub by ID
  getHub: publicProcedure.input(z.object({ id: z.string() })).query(async ({ input }) => {
    const hub = await db.hub.findUnique({
      where: { id: input.id },
      select: {
        id: true,
        name: true,
        description: true,
        shortDescription: true,
        ownerId: true,
        iconUrl: true,
        bannerUrl: true,
        private: true,
        locked: true,
        nsfw: true,
        verified: true,
        partnered: true,
        language: true,
        region: true,
        createdAt: true,
        lastActive: true,
        rules: true,

        connections: {
          where: { connected: true },
          orderBy: { lastActive: 'desc' },
          select: {
            id: true,
            serverId: true,
            connected: true,
            compact: true,
            createdAt: true,
            lastActive: true,
            server: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        upvotes: {
          select: {
            id: true,
            userId: true,
            createdAt: true,
          },
        },
        moderators: {
          select: {
            id: true,
            userId: true,
            role: true,

            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            connections: { where: { connected: true } },
            upvotes: true,
            reviews: true,
          },
        },
      },
    });

    if (!hub) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Hub not found',
      });
    }

    return hub;
  }),

  // Create a new hub
  createHub: protectedProcedure.input(createHubSchema).mutation(async ({ input, ctx }) => {
    const { name, description, shortDescription, private: isPrivate, rules } = input;

    // Check if the hub name is already taken (case insensitive)
    const existingHub = await db.hub.findFirst({
      where: {
        name: { equals: name, mode: 'insensitive' },
      },
    });

    if (existingHub) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Hub name already taken',
      });
    }

    // Create the hub
    const hub = await db.hub.create({
      data: {
        name,
        description,
        shortDescription,
        ownerId: ctx.session.user.id,
        iconUrl: `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(name)}`,
        private: isPrivate,
        settings: 0,
        rules: rules || [],
      },
      select: {
        id: true,
        name: true,
        description: true,
        shortDescription: true,
        ownerId: true,
        iconUrl: true,
        bannerUrl: true,
        private: true,
        locked: true,
        settings: true,
        createdAt: true,
        updatedAt: true,
        rules: true,

        moderators: {
          select: {
            id: true,
            userId: true,
            role: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });

    return hub;
  }),

  // Upvote a hub
  upvoteHub: protectedProcedure
    .input(z.object({ hubId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const { hubId } = input;
      const userId = ctx.session.user.id;

      // Check if the hub exists
      const hub = await db.hub.findUnique({
        where: { id: hubId },
        select: { id: true },
      });

      if (!hub) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Hub not found',
        });
      }

      // Check if the user has already upvoted this hub
      const existingUpvote = await db.hubUpvote.findUnique({
        where: {
          hubId_userId: {
            hubId,
            userId,
          },
        },
      });

      if (existingUpvote) {
        // Remove the upvote
        await db.hubUpvote.delete({
          where: {
            hubId_userId: {
              hubId,
              userId,
            },
          },
        });

        return { upvoted: false };
      }
      // Add the upvote
      await db.hubUpvote.create({
        data: {
          hubId,
          userId,
        },
      });

      return { upvoted: true };
    }),
});
