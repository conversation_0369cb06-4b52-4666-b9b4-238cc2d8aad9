"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { motion } from "motion/react";
import { useInView } from "react-intersection-observer";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  MessageSquare,
  Server,
  Users,
  Activity,
  Zap,
  Shield,
  Globe,
  Bell,
} from "lucide-react";

// Map of icon names to components
const IconMap = {
  BarChart3,
  MessageSquare,
  Server,
  Users,
  Activity,
  Zap,
  Shield,
  Globe,
  Bell,
};

type IconName = keyof typeof IconMap;

interface AnimatedStatCardProps {
  title: string;
  value: string;
  description: string;
  iconName: IconName;
  index: number;
  color?: "purple" | "blue" | "indigo" | "pink";
}

export function AnimatedStatCard({
  title,
  value,
  description,
  iconName,
  index,
  color = "purple",
}: AnimatedStatCardProps) {
  // Get the icon component from the map
  const Icon = IconMap[iconName];
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const colorVariants = {
    purple: {
      bg: "from-gray-900 to-gray-950",
      border: "border-purple-500/20 hover:border-purple-500/30",
      icon: "bg-purple-500/10 text-purple-400",
      accent: "text-purple-400"
    },
    blue: {
      bg: "from-gray-900 to-gray-950",
      border: "border-blue-500/20 hover:border-blue-500/30",
      icon: "bg-blue-500/10 text-blue-400",
      accent: "text-blue-400"
    },
    indigo: {
      bg: "from-gray-900 to-gray-950",
      border: "border-indigo-500/20 hover:border-indigo-500/30",
      icon: "bg-indigo-500/10 text-indigo-400",
      accent: "text-indigo-400"
    },
    pink: {
      bg: "from-gray-900 to-gray-950",
      border: "border-pink-500/20 hover:border-pink-500/30",
      icon: "bg-pink-500/10 text-pink-400",
      accent: "text-pink-400"
    },
  };

  const variant = colorVariants[color];

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -2, transition: { duration: 0.2 } }}
      className="h-full"
    >
      <Card
        className={cn(
          "h-full border bg-gradient-to-b transition-all duration-300 backdrop-blur-sm",
          variant.bg,
          variant.border,
          "hover:shadow-lg hover:shadow-black/20"
        )}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className="text-sm font-semibold text-gray-300 uppercase tracking-wider">
                {title}
              </CardTitle>
              <p className="text-xs text-gray-500">{description}</p>
            </div>
            <div className={cn("p-3 rounded-xl", variant.icon)}>
              <Icon className="h-6 w-6" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <motion.div
            className={cn("text-4xl font-bold tracking-tight", variant.accent)}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={
              inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }
            }
            transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
          >
            {value}
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
