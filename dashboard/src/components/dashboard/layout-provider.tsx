"use client";

import { createContext, useContext, useState, useEffect, type ReactNode } from "react";

interface DashboardLayoutContextType {
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  sidebarWidth: number;
  hubSidebarCollapsed: boolean;
  setHubSidebarCollapsed: (collapsed: boolean) => void;
  hubSidebarWidth: number;
  isHydrated: boolean;
}

const DashboardLayoutContext = createContext<DashboardLayoutContextType | undefined>(undefined);

export function useDashboardLayout() {
  const context = useContext(DashboardLayoutContext);
  if (context === undefined) {
    throw new Error("useDashboardLayout must be used within a DashboardLayoutProvider");
  }
  return context;
}

interface DashboardLayoutProviderProps {
  children: ReactNode;
}

export function DashboardLayoutProvider({ children }: DashboardLayoutProviderProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [hubSidebarCollapsed, setHubSidebarCollapsed] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    const savedState = localStorage.getItem("sidebarCollapsed");
    const savedHubState = localStorage.getItem("hubSidebarCollapsed");
    if (savedState) {
      setSidebarCollapsed(JSON.parse(savedState));
    }
    if (savedHubState) {
      setHubSidebarCollapsed(JSON.parse(savedHubState));
    }
    setIsHydrated(true);
  }, []);

  const sidebarWidth = sidebarCollapsed ? 80 : 256;
  const hubSidebarWidth = hubSidebarCollapsed ? 64 : 256;

  useEffect(() => {
    if (isHydrated) {
      localStorage.setItem("sidebarCollapsed", JSON.stringify(sidebarCollapsed));
    }
  }, [sidebarCollapsed, isHydrated]);

  useEffect(() => {
    if (isHydrated) {
      localStorage.setItem("hubSidebarCollapsed", JSON.stringify(hubSidebarCollapsed));
    }
  }, [hubSidebarCollapsed, isHydrated]);

  const value = {
    sidebarCollapsed,
    setSidebarCollapsed,
    sidebarWidth,
    hubSidebarCollapsed,
    setHubSidebarCollapsed,
    hubSidebarWidth,
    isMounted: isHydrated,
    isHydrated,
  };

  return (
    <DashboardLayoutContext.Provider value={value}>
      {children}
    </DashboardLayoutContext.Provider>
  );
}
