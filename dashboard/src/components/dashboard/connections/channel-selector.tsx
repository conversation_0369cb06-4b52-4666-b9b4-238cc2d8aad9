"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Hash, Loader2, Search } from "lucide-react";
import { useState, useEffect, useCallback } from "react";

interface ChannelWithServer {
  id: string;
  name: string;
  topic?: string;
  serverId: string;
  serverName: string;
  position: number;
}



interface ChannelSelectorProps {
  currentChannelId: string;
  connectionId: string;
  serverId: string;
  onChannelChange: (newChannelId: string) => void;
}

export function ChannelSelector({
  currentChannelId,
  connectionId,
  serverId,
  onChannelChange,
}: ChannelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isChanging, setIsChanging] = useState(false);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [channels, setChannels] = useState<ChannelWithServer[]>([]);
  const [serverName, setServerName] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch available channels when dialog opens
  const fetchChannels = useCallback(async () => {
    try {
      setIsLoadingChannels(true);
      setError(null);

      const response = await fetch(`/api/dashboard/channels?serverId=${serverId}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to fetch channels";

        // Provide more specific error messages
        if (response.status === 408) {
          throw new Error("Request timed out. Discord API is slow - please try again.");
        } else if (response.status === 429) {
          throw new Error("Rate limited. Please wait a moment before trying again.");
        } else if (response.status === 500) {
          throw new Error("Server error. Please try again later.");
        } else if (response.status === 403) {
          throw new Error("You don't have permission to access this server.");
        } else if (response.status === 400) {
          throw new Error("Invalid server ID provided.");
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setChannels(data.channels || []);
      setServerName(data.serverName || "");
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to load channels");
    } finally {
      setIsLoadingChannels(false);
    }
  }, [serverId]);

  // Load channels when dialog opens
  useEffect(() => {
    if (isOpen) {
      fetchChannels();
    }
  }, [isOpen, fetchChannels]);

  const handleChannelChange = async () => {
    if (!selectedChannelId) {
      toast({
        title: "Error",
        description: "Please select a channel.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsChanging(true);

      const response = await fetch(
        `/api/dashboard/connections/${connectionId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            channelId: selectedChannelId,
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to change channel");
      }

      onChannelChange(selectedChannelId);
      setIsOpen(false);
      setSelectedChannelId("");
      setSearchQuery("");

      toast({
        title: "Channel Changed",
        description: "The connection channel has been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to change channel",
        variant: "destructive",
      });
    } finally {
      setIsChanging(false);
    }
  };

  // Filter channels based on search query
  const filteredChannels = channels.filter(channel =>
    channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (channel.topic && channel.topic.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Get selected channel info for display
  const getSelectedChannelInfo = () => {
    const channel = channels.find(c => c.id === selectedChannelId);
    if (channel) {
      return `#${channel.name}`;
    }
    return "Select a channel";
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50"
        >
          <Hash className="h-4 w-4 mr-2" />
          Change Channel
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm border border-gray-800/50">
        <DialogHeader>
          <DialogTitle>Change Discord Channel</DialogTitle>
          <DialogDescription>
            Select a new Discord channel from <strong>{serverName}</strong> to connect to this hub. Only available text channels are shown.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="currentChannel">Current Channel</Label>
            <Input
              id="currentChannel"
              value={currentChannelId}
              readOnly
              className="bg-[#0a0a0c] border-gray-800 text-gray-400"
            />
            <p className="text-xs text-gray-400">
              Channel ID: {currentChannelId}
            </p>
          </div>

          {isLoadingChannels ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="ml-2 text-sm text-gray-400">Loading channels...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-sm text-red-400 mb-2">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchChannels}
                className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="search">Search Channels</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by channel name or topic..."
                    className="bg-[#0a0a0c] border-gray-800 pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelSelect">Select Channel</Label>
                <Select value={selectedChannelId} onValueChange={setSelectedChannelId}>
                  <SelectTrigger className="bg-[#0a0a0c] border-gray-800">
                    <SelectValue placeholder={getSelectedChannelInfo()} />
                  </SelectTrigger>
                  <SelectContent className="bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm border border-gray-800/50 max-h-60">
                    {filteredChannels.length === 0 ? (
                      <div className="p-4 text-center text-gray-400">
                        {searchQuery ? (
                          <div>
                            <p className="font-medium">No channels match your search</p>
                            <p className="text-xs mt-1">Try a different search term</p>
                          </div>
                        ) : (
                          <div>
                            <p className="font-medium">No available channels</p>
                            <p className="text-xs mt-1">
                              This could mean:
                            </p>
                            <ul className="text-xs mt-1 space-y-1">
                              <li>• You don&apos;t have &quot;Manage Channels&quot; permission on this server</li>
                              <li>• All channels are already connected to hubs</li>
                              <li>• The bot lacks access to this server</li>
                            </ul>
                          </div>
                        )}
                      </div>
                    ) : (
                      filteredChannels.map((channel) => (
                        <SelectItem
                          key={channel.id}
                          value={channel.id}
                          className="hover:bg-gray-800/50 focus:bg-gray-800/50"
                        >
                          <div className="flex flex-col">
                            <span className="font-medium"># {channel.name}</span>
                            {channel.topic && (
                              <span className="text-xs text-gray-400 truncate max-w-60">
                                {channel.topic}
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-400">
                  Only text channels from this server are shown.
                  Channels already connected to other hubs are excluded.
                </p>
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleChannelChange}
            disabled={isChanging || !selectedChannelId || isLoadingChannels}
          >
            {isChanging ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Changing...
              </>
            ) : (
              "Change Channel"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
