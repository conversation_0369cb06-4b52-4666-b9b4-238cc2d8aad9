"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import {
  Copy,
  ExternalLink,
  Hash,
  Loader2,
  Palette,
  Plus,
  Save,
  X,
  Settings,
  Zap,
  Eye,
  Link,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useState } from "react";
import { ChannelSelector } from "./channel-selector";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface EnhancedConnectionEditFormProps {
  connection: Connection & { hub: Hub; server: ServerData };
  onUpdate: (updatedConnection: Connection & { hub: Hub; server: ServerData }) => void;
  onSave: () => void;
  isSaving: boolean;
  setSaving: (saving: boolean) => void;
}

export function ConnectionEditForm({
  connection,
  onUpdate,
  onSave,
  isSaving,
  setSaving,
}: EnhancedConnectionEditFormProps) {
  // Move all hooks to the top before any conditional returns
  const [isConnected, setIsConnected] = useState(connection?.connected || false);
  const [isCompact, setIsCompact] = useState(connection?.compact || false);
  const [embedColor, setEmbedColor] = useState(connection?.embedColor || "#5865F2");
  const [inviteUrl, setInviteUrl] = useState(connection?.invite || "");
  const [channelId, setChannelId] = useState(connection?.channelId || "");
  const [isGeneratingInvite, setIsGeneratingInvite] = useState(false);

  const { toast } = useToast();

  if (!connection) {
    return (
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  const handleGenerateInvite = async () => {
    try {
      setIsGeneratingInvite(true);

      const response = await fetch(
        `/api/dashboard/connections/${connection.id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            action: "generate_invite",
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        const errorMessage = error.error || "Failed to generate invite";

        // Handle specific error cases with user-friendly messages
        if (response.status === 403) {
          throw new Error("Bot needs 'Create Instant Invite' permission in this channel");
        } else if (response.status === 404) {
          throw new Error("Channel not found or bot lacks access");
        } else if (response.status === 429) {
          throw new Error("Discord API rate limit exceeded. Please try again later.");
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setInviteUrl(data.inviteUrl);
      onUpdate(data.connection);

      toast({
        title: "Invite Generated",
        description: "A new invite link has been generated for this connection.",
      });
    } catch (error) {
      console.error("Error generating invite:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to generate invite",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingInvite(false);
    }
  };

  const handleRemoveInvite = () => {
    setInviteUrl("");
  };

  const handleChannelChange = (newChannelId: string) => {
    setChannelId(newChannelId);
    // Update the connection object to reflect the change
    const updatedConnection = { ...connection, channelId: newChannelId };
    onUpdate(updatedConnection);
  };



  const handleCopyInvite = async () => {
    if (inviteUrl) {
      try {
        await navigator.clipboard.writeText(inviteUrl);
        toast({
          title: "Copied!",
          description: "Invite link copied to clipboard.",
        });
      } catch {
        toast({
          title: "Error",
          description: "Failed to copy invite link.",
          variant: "destructive",
        });
      }
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const response = await fetch(
        `/api/dashboard/connections/${connection.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            connected: isConnected,
            compact: isCompact,
            embedColor: embedColor || null,
            invite: inviteUrl || null,
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update connection");
      }

      const data = await response.json();
      onUpdate(data.connection);

      toast({
        title: "Connection Updated",
        description: "The connection has been updated successfully.",
      });

      // Call onSave after successful update and toast
      onSave();
    } catch (error) {
      console.error("Error updating connection:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update connection",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Status Header */}
      <Card className="border-gray-800 bg-gradient-to-r from-[#0f1117] to-[#1a1d29] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5" />
        <CardHeader className="relative px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <Zap className="h-5 w-5 text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-xl">Connection Settings</CardTitle>
                <CardDescription className="text-gray-400">
                  Configure how this connection works between your server and hub
                </CardDescription>
              </div>
            </div>
            <Badge
              variant={isConnected ? "default" : "secondary"}
              className={`${isConnected
                ? "bg-green-500/10 text-green-400 border-green-500/20"
                : "bg-gray-500/10 text-gray-400 border-gray-500/20"
              }`}
            >
              {isConnected ? (
                <>
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </>
              ) : (
                <>
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Inactive
                </>
              )}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Main Settings Card */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="px-6 py-4">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-400" />
            <CardTitle className="text-lg">Basic Configuration</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-8 px-6 pb-6">
        {/* Connection Controls */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-green-500/10 border border-green-500/20">
                <Zap className="h-4 w-4 text-green-400" />
              </div>
              <div className="space-y-0.5">
                <Label htmlFor="connected" className="text-base font-medium">Connection Enabled</Label>
                <div className="text-sm text-gray-400">
                  Enable or disable message relay for this connection
                </div>
              </div>
            </div>
            <Switch
              id="connected"
              checked={isConnected}
              onCheckedChange={setIsConnected}
              className="data-[state=checked]:bg-green-600 self-start sm:self-center"
            />
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 hover:bg-gray-900/30 transition-colors gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-blue-500/10 border border-blue-500/20">
                <Eye className="h-4 w-4 text-blue-400" />
              </div>
              <div className="space-y-0.5">
                <Label htmlFor="compact" className="text-base font-medium">Compact Mode</Label>
                <div className="text-sm text-gray-400">
                  Show messages in a more compact, condensed format
                </div>
              </div>
            </div>
            <Switch
              id="compact"
              checked={isCompact}
              onCheckedChange={setIsCompact}
              className="self-start sm:self-center"
            />
          </div>
        </div>


          {/* Embed Color */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-purple-400" />
              <Label htmlFor="embedColor" className="text-base font-medium">Embed Color</Label>
            </div>
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 space-y-3">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Input
                    id="embedColor"
                    type="color"
                    value={embedColor}
                    onChange={(e) => setEmbedColor(e.target.value)}
                    className="w-12 h-12 p-1 bg-[#0a0a0c] border-gray-700 rounded-lg cursor-pointer"
                  />
                  <div
                    className="absolute inset-1 rounded-md border-2 border-white/20 pointer-events-none"
                    style={{ backgroundColor: embedColor }}
                  />
                </div>
                <div className="flex-1">
                  <Input
                    type="text"
                    value={embedColor}
                    onChange={(e) => setEmbedColor(e.target.value)}
                    placeholder="#5865F2"
                    className="bg-[#0a0a0c] border-gray-700 font-mono"
                    pattern="^#[0-9A-Fa-f]{6}$"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <div className="w-3 h-3 rounded border border-gray-600" style={{ backgroundColor: embedColor }} />
                <span>Preview: This color will appear on embeds sent from your server</span>
              </div>
            </div>
          </div>

          <Separator className="bg-gray-800" />

          {/* Discord Channel */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Hash className="h-5 w-5 text-indigo-400" />
              <Label className="text-base font-medium">Discord Channel</Label>
            </div>
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 space-y-3">
              <div className="flex items-center gap-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 p-3 bg-[#0a0a0c] border border-gray-700 rounded-lg">
                    <Hash className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300 font-medium">{channelId}</span>
                    <Badge variant="outline" className="ml-auto text-xs border-gray-600 text-gray-400">
                      Current
                    </Badge>
                  </div>
                </div>
                <ChannelSelector
                  currentChannelId={channelId}
                  connectionId={connection.id}
                  serverId={connection.serverId}
                  onChannelChange={handleChannelChange}
                />
              </div>
              <div className="flex items-start gap-2 text-xs text-gray-400 bg-blue-500/5 border border-blue-500/10 rounded-lg p-3">
                <AlertCircle className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-blue-400 mb-1">Channel Selection</p>
                  <p>Click &quot;Change Channel&quot; to select a different text channel from <strong>{connection.server.name}</strong>. Only channels where you have &quot;Manage Channels&quot; permission will be available.</p>
                </div>
              </div>
            </div>
          </div>

          <Separator className="bg-gray-800" />

          {/* Invite Link Management */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Link className="h-5 w-5 text-green-400" />
              <Label className="text-base font-medium">Server Invite Link</Label>
            </div>
            <div className="p-4 rounded-lg border border-gray-800/50 bg-gray-900/20 space-y-3">
              {inviteUrl ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Input
                      value={inviteUrl}
                      readOnly
                      className="bg-[#0a0a0c] border-gray-700 font-mono text-sm"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleCopyInvite}
                      className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 px-3"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(inviteUrl, "_blank")}
                      className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 px-3"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleRemoveInvite}
                      className="border-red-700 bg-red-800/50 hover:bg-red-700/50 text-red-300 px-3"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-green-400 bg-green-500/5 border border-green-500/10 rounded-lg p-3">
                    <CheckCircle className="h-4 w-4 flex-shrink-0" />
                    <span>Invite link is active - hub members can use this to join your server</span>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleGenerateInvite}
                    disabled={isGeneratingInvite}
                    className="w-full border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 h-12"
                  >
                    {isGeneratingInvite ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating invite link...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Generate Server Invite Link
                      </>
                    )}
                  </Button>
                  <div className="flex items-center gap-2 text-xs text-gray-400 bg-gray-500/5 border border-gray-500/10 rounded-lg p-3">
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                    <span>No invite link configured - hub members cannot join your server directly</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator className="bg-gray-800" />

          {/* Save Button */}
          <div className="pt-4">
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none text-white font-medium"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  <span className="hidden sm:inline">Saving changes...</span>
                  <span className="inline sm:hidden">Saving...</span>
                </>
              ) : (
                <>
                  <Save className="h-5 w-5 mr-2" />
                  <span className="hidden sm:inline">Save All Changes</span>
                  <span className="inline sm:hidden">Save Changes</span>
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
