"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Home, Menu } from "lucide-react";
import type { User } from "next-auth";
import { useState } from "react";
import { UserNav } from "../user-nav";
import { MobileSidebar } from "./mobile-sidebar";
import { NotificationDropdown } from "./notifications/notification-dropdown";
import { OnboardingHelpMenu } from "./onboarding/onboarding-help-menu";

export function DashboardTopBar({ user }: { user: User }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="sticky top-0 z-40 flex h-16 flex-shrink-0 border-b border-gray-800/50 bg-gradient-to-r from-gray-900 to-gray-950 backdrop-blur-sm">
      <div className="flex flex-1 items-center px-4">
        <div className="flex items-center lg:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-400 hover:text-white"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open sidebar</span>
          </Button>
        </div>

        <div className="ml-auto flex items-center gap-4">
          {/* Notifications */}
          <div data-tour="notifications">
            <NotificationDropdown />
          </div>

          {/* Help */}
          <OnboardingHelpMenu />

          {/* User dropdown */}
          <div className="flex items-center" data-tour="user-menu">
            <UserNav
              user={user}
              firstPage={{ name: "Home", icon: Home, href: "/" }}
            />
          </div>
        </div>

        {/* Mobile sidebar using portal to render outside the DOM hierarchy */}
        <MobileSidebar
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
          user={user}
        />
      </div>
    </div>
  );
}
