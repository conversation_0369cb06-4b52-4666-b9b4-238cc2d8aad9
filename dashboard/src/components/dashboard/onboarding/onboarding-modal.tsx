"use client";

import { useOnboarding } from "./onboarding-provider";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { motion, AnimatePresence } from "motion/react";
import {
  ArrowRight,
  ArrowLeft,
  X,
  Sparkles,
  MessageSquare,
  Settings,
  Search,
  HelpCircle
} from "lucide-react";
import { useState } from "react";

export function OnboardingModal() {
  const {
    isOnboardingActive,
    currentStep,
    steps,
    nextStep,
    previousStep,
    skipOnboarding,
    canGoNext,
    canGoPrevious,
    progress
  } = useOnboarding();

  const [isAnimating, setIsAnimating] = useState(false);

  if (!isOnboardingActive) return null;

  const currentStepData = steps[currentStep];

  const handleNext = async () => {
    setIsAnimating(true);
    setTimeout(() => {
      nextStep();
      setIsAnimating(false);
    }, 150);
  };

  const handlePrevious = async () => {
    setIsAnimating(true);
    setTimeout(() => {
      previousStep();
      setIsAnimating(false);
    }, 150);
  };

  const getStepIcon = (stepId: string) => {
    switch (stepId) {
      case "welcome":
        return <Sparkles className="h-6 w-6 text-purple-400" />;
      case "hub-management":
        return <MessageSquare className="h-6 w-6 text-blue-400" />;
      case "first-hub":
        return <Settings className="h-6 w-6 text-green-400" />;
      case "customization":
        return <Settings className="h-6 w-6 text-orange-400" />;
      case "discovery":
        return <Search className="h-6 w-6 text-indigo-400" />;
      default:
        return <HelpCircle className="h-6 w-6 text-gray-400" />;
    }
  };

  return (
    <Dialog open={isOnboardingActive} onOpenChange={() => {}}>
      <DialogContent
        className="max-w-2xl w-full mx-4 bg-gray-900 border-gray-800 text-white shadow-2xl"
        style={{ zIndex: 10000 }}
      >
        {/* Header with progress */}
        <DialogHeader className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStepIcon(currentStepData.id)}
              <div>
                <DialogTitle className="text-xl font-semibold text-white">
                  {currentStepData.title}
                </DialogTitle>
                <DialogDescription className="text-gray-400 text-sm">
                  {currentStepData.description}
                </DialogDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={skipOnboarding}
              className="h-8 w-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800"
              aria-label="Skip onboarding"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-gray-400">
              <span>Step {currentStep + 1} of {steps.length}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress
              value={progress}
              className="h-2 bg-gray-800"
            />
          </div>
        </DialogHeader>

        {/* Content with animation */}
        <div className="py-6 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: isAnimating ? 0 : 1, x: isAnimating ? -20 : 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="h-full"
            >
              {currentStepData.content}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer with navigation */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-800">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              onClick={skipOnboarding}
              className="text-gray-400 hover:text-white"
            >
              Skip Tour
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              onClick={handlePrevious}
              disabled={!canGoPrevious}
              className="text-gray-400 hover:text-white disabled:opacity-50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <Button
              onClick={handleNext}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
            >
              {canGoNext ? (
                <>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              ) : (
                <>
                  Get Started
                  <Sparkles className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Step indicators */}
        <div className="flex justify-center gap-2 pt-4">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`h-2 w-2 rounded-full transition-colors ${
                index === currentStep
                  ? "bg-purple-500"
                  : index < currentStep
                  ? "bg-purple-300"
                  : "bg-gray-600"
              }`}
            />
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
