"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
    ArrowLeft,
    ArrowRight,
    HelpCircle,
    MessageSquare,
    Plus,
    Search,
    Settings,
    Sparkles,
    X,
    Zap
} from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useState } from "react";
import { OnboardingSuccess, QuickHubCreation } from "./contextual-onboarding-actions";
import {
    InteractiveFirstHubContent
} from "./interactive-onboarding-content";
import { useOnboarding } from "./onboarding-provider";

type ModalMode = "normal" | "quick-create" | "success";

export function EnhancedOnboardingModal() {
  const {
    isOnboardingActive,
    currentStep,
    steps,
    nextStep,
    previousStep,
    skipOnboarding,
    completeOnboarding,
    canGoNext,
    canGoPrevious,
    progress
  } = useOnboarding();

  const [isAnimating, setIsAnimating] = useState(false);
  const [modalMode, setModalMode] = useState<ModalMode>("normal");
  const [createdHubData, setCreatedHubData] = useState<{ id: string; name: string } | null>(null);

  if (!isOnboardingActive) return null;

  const currentStepData = steps[currentStep];

  const handleNext = async () => {
    setIsAnimating(true);
    setTimeout(() => {
      nextStep();
      setIsAnimating(false);
    }, 150);
  };

  const handlePrevious = async () => {
    setIsAnimating(true);
    setTimeout(() => {
      previousStep();
      setIsAnimating(false);
    }, 150);
  };

  const handleQuickCreateSuccess = (hubId: string) => {
    setCreatedHubData({ id: hubId, name: "Your Hub" }); // We could get the name from the creation
    setModalMode("success");
  };

  const handleSuccessContinue = () => {
    setModalMode("normal");
    completeOnboarding();
  };

  const getStepIcon = (stepId: string) => {
    switch (stepId) {
      case "welcome":
        return <Sparkles className="h-6 w-6 text-purple-400" />;
      case "hub-management":
        return <MessageSquare className="h-6 w-6 text-blue-400" />;
      case "first-hub":
        return <Settings className="h-6 w-6 text-green-400" />;
      case "customization":
        return <Settings className="h-6 w-6 text-orange-400" />;
      case "discovery":
        return <Search className="h-6 w-6 text-indigo-400" />;
      default:
        return <HelpCircle className="h-6 w-6 text-gray-400" />;
    }
  };

  const renderContent = () => {
    if (modalMode === "quick-create") {
      return (
        <QuickHubCreation
          onSuccess={handleQuickCreateSuccess}
          onCancel={() => setModalMode("normal")}
        />
      );
    }

    if (modalMode === "success") {
      return (
        <OnboardingSuccess
          hubName={createdHubData?.name}
          hubId={createdHubData?.id}
          onContinue={handleSuccessContinue}
        />
      );
    }

    // Render the normal step content with special handling for first-hub step
    if (currentStepData.id === "first-hub") {
      return (
        <div className="space-y-4">
          <InteractiveFirstHubContent />

          {/* Quick create option */}
          <div className="border-t border-gray-700/50 pt-4">
            <div className="text-center space-y-3">
              <p className="text-sm text-gray-400">
                Want to create a hub right now? Try our quick setup:
              </p>
              <Button
                onClick={() => setModalMode("quick-create")}
                variant="outline"
                className="border-purple-500/50 bg-purple-900/20 hover:bg-purple-800/30 text-purple-300 hover:text-purple-200"
              >
                <Zap className="h-4 w-4 mr-2" />
                Quick Hub Setup
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return currentStepData.content;
  };

  const getModalTitle = () => {
    if (modalMode === "quick-create") return "Quick Hub Creation";
    if (modalMode === "success") return "Success!";
    return currentStepData.title;
  };

  const getModalDescription = () => {
    if (modalMode === "quick-create") return "Create your first hub in just a few steps";
    if (modalMode === "success") return "You're all set up and ready to go!";
    return currentStepData.description;
  };

  const showNavigation = modalMode === "normal";
  const showProgress = modalMode === "normal";

  return (
    <Dialog open={isOnboardingActive} onOpenChange={() => {}}>
      <DialogContent
        className="max-w-2xl w-full mx-4 bg-gray-900 border-gray-800 text-white shadow-2xl"
        style={{ zIndex: 10000 }}
      >
        {/* Header with progress */}
        <DialogHeader className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {modalMode === "quick-create" ? (
                <Plus className="h-6 w-6 text-purple-400" />
              ) : modalMode === "success" ? (
                <Sparkles className="h-6 w-6 text-green-400" />
              ) : (
                getStepIcon(currentStepData.id)
              )}
              <div>
                <DialogTitle className="text-xl font-semibold text-white">
                  {getModalTitle()}
                </DialogTitle>
                <DialogDescription className="text-gray-400 text-sm">
                  {getModalDescription()}
                </DialogDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={skipOnboarding}
              className="h-8 w-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800"
              aria-label="Skip onboarding"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Progress bar - only show in normal mode */}
          {showProgress && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-400">
                <span>Step {currentStep + 1} of {steps.length}</span>
                <span>{Math.round(progress)}% complete</span>
              </div>
              <Progress
                value={progress}
                className="h-2 bg-gray-800"
              />
            </div>
          )}
        </DialogHeader>

        {/* Content with animation */}
        <div className="py-6 min-h-[300px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={`${modalMode}-${currentStep}`}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: isAnimating ? 0 : 1, x: isAnimating ? -20 : 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderContent()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer with navigation - only show in normal mode */}
        {showNavigation && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-800">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                onClick={skipOnboarding}
                className="text-gray-400 hover:text-white"
              >
                Skip Tour
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                onClick={handlePrevious}
                disabled={!canGoPrevious}
                className="text-gray-400 hover:text-white disabled:opacity-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <Button
                onClick={handleNext}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
              >
                {canGoNext ? (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </>
                ) : (
                  <>
                    Get Started
                    <Sparkles className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Step indicators - only show in normal mode */}
        {showProgress && (
          <div className="flex justify-center gap-2 pt-4">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`h-2 w-2 rounded-full transition-colors ${
                  index === currentStep
                    ? "bg-purple-500"
                    : index < currentStep
                    ? "bg-purple-300"
                    : "bg-gray-600"
                }`}
              />
            ))}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
