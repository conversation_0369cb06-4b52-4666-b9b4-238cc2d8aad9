"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "motion/react";
import { 
  Plus, 
  Search, 
  BookOpen, 
  MessageSquare, 
  Users,
  ExternalLink
} from "lucide-react";
import Link from "next/link";
import { useOnboarding } from "./onboarding-provider";

export function OnboardingQuickActions() {
  const { isNewUser } = useOnboarding();

  const quickActions = [
    {
      title: "Create Your First Hub",
      description: "Start your own community hub",
      icon: Plus,
      href: "/dashboard/hubs/create",
      color: "from-purple-600 to-blue-600",
      highlight: true
    },
    {
      title: "Browse Hubs",
      description: "Discover active communities",
      icon: Search,
      href: "/hubs",
      color: "from-green-600 to-teal-600",
      highlight: true
    },
    {
      title: "My Hubs",
      description: "Manage your existing hubs",
      icon: MessageSquare,
      href: "/dashboard/hubs",
      color: "from-indigo-600 to-purple-600",
      highlight: false
    },
    {
      title: "Documentation",
      description: "Learn how to use InterChat",
      icon: BookOpen,
      href: "/docs",
      color: "from-orange-600 to-red-600",
      highlight: false,
      external: true
    }
  ];

  return (
    <Card 
      id="hub-management-section"
      className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm"
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-xl font-semibold">
          <Users className="h-5 w-5 text-purple-400" />
          Quick Actions
        </CardTitle>
        {isNewUser && (
          <p className="text-sm text-gray-400">
            Get started with these essential actions
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-3">
        {quickActions.map((action, index) => {
          const Icon = action.icon;
          const shouldHighlight = isNewUser && action.highlight;
          
          const buttonContent = (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="w-full"
            >
              <Button
                variant="ghost"
                className={`
                  w-full justify-start h-auto p-4 text-left transition-all duration-200
                  ${shouldHighlight 
                    ? 'bg-gradient-to-r ' + action.color + ' hover:opacity-90 text-white shadow-lg ring-2 ring-purple-500/20' 
                    : 'hover:bg-gray-800/50 text-gray-300 hover:text-white'
                  }
                `}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className={`
                    p-2 rounded-lg 
                    ${shouldHighlight 
                      ? 'bg-white/20' 
                      : 'bg-gray-800/50'
                    }
                  `}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm">
                      {action.title}
                    </div>
                    <div className={`
                      text-xs 
                      ${shouldHighlight 
                        ? 'text-white/80' 
                        : 'text-gray-500'
                      }
                    `}>
                      {action.description}
                    </div>
                  </div>
                  {action.external && (
                    <ExternalLink className="h-3 w-3 opacity-60" />
                  )}
                </div>
              </Button>
            </motion.div>
          );

          if (action.external) {
            return (
              <a
                key={action.title}
                href={action.href}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                {buttonContent}
              </a>
            );
          }

          return (
            <Link key={action.title} href={action.href} className="block">
              {buttonContent}
            </Link>
          );
        })}

        {isNewUser && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg"
          >
            <div className="flex items-start gap-2">
              <div className="p-1 bg-blue-500/20 rounded">
                <BookOpen className="h-3 w-3 text-blue-400" />
              </div>
              <div className="flex-1 text-xs">
                <p className="text-blue-300 font-medium mb-1">New to InterChat?</p>
                <p className="text-blue-200/80">
                  Start by creating your first hub or browsing existing communities. 
                  Check out our documentation for detailed guides.
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
