"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
    ArrowRight,
    BarChart3,
    ExternalLink,
    Globe,
    MessageSquare,
    Palette,
    Plus,
    Search,
    Settings,
    Shield,
    Users
} from "lucide-react";
import { QuickHubCreation } from "./contextual-onboarding-actions";
import { useOnboarding } from "./onboarding-provider";

interface InteractiveActionCardProps {
  title: string;
  description: string;
  icon: React.ElementType;
  actionText: string;
  route?: string;
  external?: boolean;
  variant?: "primary" | "secondary" | "outline";
  onClick?: () => void;
}

function InteractiveActionCard({
  title,
  description,
  icon: Icon,
  actionText,
  route,
  external = false,
  variant = "primary",
  onClick
}: InteractiveActionCardProps) {
  const { navigateToRoute, pauseOnboarding } = useOnboarding();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (route) {
      pauseOnboarding();
      navigateToRoute(route, external);
    }
  };

  const buttonVariants = {
    primary: "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white",
    secondary: "bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white",
    outline: "border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
  };

  return (
    <Card className="border border-gray-700/50 bg-gradient-to-b from-gray-800/50 to-gray-900/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gray-800/50 border border-gray-700/50">
            <Icon className="h-5 w-5 text-purple-400" />
          </div>
          <div>
            <CardTitle className="text-lg font-semibold text-white">{title}</CardTitle>
            <CardDescription className="text-gray-400 text-sm">{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Button
          onClick={handleClick}
          className={`w-full ${buttonVariants[variant]} border-none`}
        >
          {actionText}
          {external ? (
            <ExternalLink className="h-4 w-4 ml-2" />
          ) : (
            <ArrowRight className="h-4 w-4 ml-2" />
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

export const InteractiveWelcomeContent = () => (
  <div className="space-y-6">
    <div className="text-center space-y-3">
      <h3 className="text-xl font-semibold text-white">Welcome to InterChat Dashboard!</h3>
      <p className="text-gray-300">
        Your central hub for managing Discord communities that connect across multiple servers.
      </p>
    </div>

    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
      <h4 className="font-semibold text-white mb-3">What you can do here:</h4>
      <div className="grid gap-2 text-sm text-gray-300">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-purple-400" />
          <span>Create and manage hubs (chat rooms)</span>
        </div>
        <div className="flex items-center gap-2">
          <Settings className="h-4 w-4 text-blue-400" />
          <span>Customize your community settings</span>
        </div>
        <div className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4 text-green-400" />
          <span>Monitor activity and analytics</span>
        </div>
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-orange-400" />
          <span>Connect with other Discord servers</span>
        </div>
      </div>
    </div>

    <div className="text-center">
      <Badge variant="secondary" className="bg-purple-900/30 text-purple-300 border-purple-500/30">
        Let&apos;s get you started with a quick tour!
      </Badge>
    </div>
  </div>
);

export const InteractiveHubManagementContent = () => (
  <div className="space-y-6">
    <div className="space-y-3">
      <h3 className="text-xl font-semibold text-white">Hub Management</h3>
      <p className="text-gray-300">
        Hubs are the core of InterChat - they&apos;re like chat rooms that connect multiple Discord servers together.
      </p>
    </div>

    <div className="grid gap-4">
      <InteractiveActionCard
        title="Create Your First Hub"
        description="Start your own community hub with custom settings"
        icon={Plus}
        actionText="Create New Hub"
        route="/dashboard/hubs/create"
        variant="primary"
      />

      <InteractiveActionCard
        title="Browse Existing Hubs"
        description="Discover and join popular communities"
        icon={Search}
        actionText="Explore Hubs"
        route="/hubs/search"
        variant="secondary"
      />

      <InteractiveActionCard
        title="View Your Hubs"
        description="Manage hubs you own or moderate"
        icon={MessageSquare}
        actionText="My Hubs"
        route="/dashboard/hubs"
        variant="outline"
      />
    </div>
  </div>
);

export const InteractiveFirstHubContent = ({ showQuickCreate = false, onQuickCreateSuccess }: {
  showQuickCreate?: boolean;
  onQuickCreateSuccess?: (hubId: string) => void;
} = {}) => {
  if (showQuickCreate) {
    return (
      <QuickHubCreation
        onSuccess={onQuickCreateSuccess}
        onCancel={() => {
          // This would be handled by the parent modal to switch back to the normal view
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-3">
        <h3 className="text-xl font-semibold text-white">Ready to Get Started?</h3>
        <p className="text-gray-300">
          Choose your path to begin your InterChat journey. You can create a new hub or join existing communities.
        </p>
      </div>

      <div className="grid gap-4">
        <InteractiveActionCard
          title="🆕 Create New Hub"
          description="Perfect for starting your own community or connecting your existing Discord servers"
          icon={Plus}
          actionText="Start Creating"
          route="/dashboard/hubs/create"
          variant="primary"
        />

        <InteractiveActionCard
          title="🌟 Join Popular Hub"
          description="Browse and join active communities that match your interests"
          icon={Globe}
          actionText="Browse Communities"
          route="/hubs/search?sort=trending"
          variant="secondary"
        />
      </div>

      <div className="text-center text-sm text-gray-400">
        Don&apos;t worry - you can always do both! Many users start by joining communities to see how InterChat works.
      </div>
    </div>
  );
};

export const InteractiveCustomizationContent = () => (
  <div className="space-y-6">
    <div className="space-y-3">
      <h3 className="text-xl font-semibold text-white">Customize Your Experience</h3>
      <p className="text-gray-300">
        Once you have a hub, you can customize it to match your community&apos;s style and needs.
      </p>
    </div>

    <div className="grid gap-4">
      <InteractiveActionCard
        title="Visual Customization"
        description="Add banners, icons, and descriptions to your hubs"
        icon={Palette}
        actionText="Learn More"
        route="/docs/features/customization"
        external={true}
        variant="outline"
      />

      <InteractiveActionCard
        title="Community Settings"
        description="Configure rules, moderation, and language preferences"
        icon={Shield}
        actionText="View Settings"
        route="/dashboard/settings"
        variant="outline"
      />

      <InteractiveActionCard
        title="Hub Analytics"
        description="Track your community's growth and engagement"
        icon={BarChart3}
        actionText="View Dashboard"
        route="/dashboard"
        variant="outline"
      />
    </div>
  </div>
);

export const InteractiveDiscoveryContent = () => (
  <div className="space-y-6">
    <div className="space-y-3">
      <h3 className="text-xl font-semibold text-white">Hub Discovery & Growth</h3>
      <p className="text-gray-300">
        Your hub can grow through our discovery system and community sharing.
      </p>
    </div>

    <div className="grid gap-4">
      <InteractiveActionCard
        title="🔍 Hub Directory"
        description="List your hub in our public directory for others to discover"
        icon={Search}
        actionText="Browse Directory"
        route="/hubs/search"
        variant="primary"
      />

      <InteractiveActionCard
        title="📊 Analytics Dashboard"
        description="Track your hub's growth and engagement metrics"
        icon={BarChart3}
        actionText="View Analytics"
        route="/dashboard"
        variant="secondary"
      />

      <InteractiveActionCard
        title="📚 Documentation"
        description="Learn advanced features and best practices"
        icon={ExternalLink}
        actionText="Read Docs"
        route="/docs"
        external={true}
        variant="outline"
      />
    </div>

    <div className="text-center">
      <Badge variant="secondary" className="bg-green-900/30 text-green-300 border-green-500/30">
        🎉 You&apos;re all set! Start building your community today.
      </Badge>
    </div>
  </div>
);
