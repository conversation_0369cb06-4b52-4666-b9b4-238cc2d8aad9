"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { HelpCircle, PlayCircle, BookOpen, MessageCircle, ExternalLink, Target } from "lucide-react";
import { useOnboarding } from "./onboarding-provider";
import { useGuidedTour } from "./guided-tour-provider";

export function OnboardingHelpMenu() {
  const { startOnboarding } = useOnboarding();
  const { startTour } = useGuidedTour();

  const helpItems = [
    {
      label: "Restart Tour",
      description: "Take the dashboard tour again",
      icon: PlayCircle,
      action: startOnboarding,
      color: "text-purple-400"
    },
    {
      label: "Interactive Guide",
      description: "Step-by-step guided tour with highlights",
      icon: Target,
      action: startTour,
      color: "text-orange-400"
    },
    {
      label: "Documentation",
      description: "Read our comprehensive guides",
      icon: Book<PERSON><PERSON>,
      href: "/docs",
      external: true,
      color: "text-blue-400"
    },
    {
      label: "Support Server",
      description: "Get help from our community",
      icon: MessageCircle,
      href: "https://discord.gg/cgYgC6YZyX",
      external: true,
      color: "text-green-400"
    }
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="text-gray-400 hover:text-white rounded-full h-9 w-9"
        >
          <HelpCircle className="h-5 w-5" />
          <span className="sr-only">Help menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-64 bg-gray-900 border-gray-800 text-white"
      >
        <DropdownMenuLabel className="text-gray-300">
          Help & Support
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-800" />

        {helpItems.map((item) => {
          const Icon = item.icon;

          if (item.href) {
            return (
              <DropdownMenuItem key={item.label} asChild>
                <a
                  href={item.href}
                  target={item.external ? "_blank" : undefined}
                  rel={item.external ? "noopener noreferrer" : undefined}
                  className="flex items-center gap-3 px-3 py-2 text-sm cursor-pointer hover:bg-gray-800 focus:bg-gray-800"
                >
                  <Icon className={`h-4 w-4 ${item.color}`} />
                  <div className="flex-1">
                    <div className="font-medium text-white">
                      {item.label}
                    </div>
                    <div className="text-xs text-gray-400">
                      {item.description}
                    </div>
                  </div>
                  {item.external && (
                    <ExternalLink className="h-3 w-3 text-gray-500" />
                  )}
                </a>
              </DropdownMenuItem>
            );
          }

          return (
            <DropdownMenuItem
              key={item.label}
              onClick={item.action}
              className="flex items-center gap-3 px-3 py-2 text-sm cursor-pointer hover:bg-gray-800 focus:bg-gray-800"
            >
              <Icon className={`h-4 w-4 ${item.color}`} />
              <div className="flex-1">
                <div className="font-medium text-white">
                  {item.label}
                </div>
                <div className="text-xs text-gray-400">
                  {item.description}
                </div>
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
