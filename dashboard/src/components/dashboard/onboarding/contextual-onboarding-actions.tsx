"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import {
  Plus,
  Loader2,
  Check,
  ArrowRight,
  Settings,
  Globe,
  Lock
} from "lucide-react";
import { useOnboarding } from "./onboarding-provider";

interface QuickHubCreationProps {
  onSuccess?: (hubId: string) => void;
  onCancel?: () => void;
}

export function QuickHubCreation({ onSuccess, onCancel }: QuickHubCreationProps) {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isNameValid, setIsNameValid] = useState(false);
  const { toast } = useToast();
  const { navigateToRoute } = useOnboarding();

  const validateName = (hubName: string) => {
    const isValid = hubName.length >= 3 && hubName.length <= 32;
    setIsNameValid(isValid);
    return isValid;
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setName(value);
    validateName(value);
  };

  const handleQuickCreate = async () => {
    if (!validateName(name) || !description.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide a valid name (3-32 characters) and description.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      const response = await fetch("/api/hubs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: name.trim(),
          description: description.trim(),
          private: isPrivate,
          rules: [],
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create hub");
      }

      const data = await response.json();

      toast({
        title: "Hub Created Successfully!",
        description: `Your hub "${name}" has been created. You can customize it further in the dashboard.`,
      });

      if (onSuccess) {
        onSuccess(data.id);
      } else {
        // Navigate to the new hub's page
        navigateToRoute(`/dashboard/hubs/${data.id}`);
      }
    } catch (error) {
      console.error("Error creating hub:", error);
      toast({
        title: "Creation Failed",
        description: error instanceof Error ? error.message : "Failed to create hub. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleFullCreation = () => {
    navigateToRoute("/dashboard/hubs/create");
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-white">Create Your First Hub</h3>
        <p className="text-gray-300 text-sm">
          Get started quickly with a basic hub, or use the full creation flow for more options.
        </p>
      </div>

      <Card className="border border-gray-700/50 bg-gradient-to-b from-gray-800/50 to-gray-900/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg text-white">Quick Hub Setup</CardTitle>
          <CardDescription className="text-gray-400">
            Create a basic hub in just a few steps. You can customize it later.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="quick-name" className="text-sm font-medium text-gray-300">
              Hub Name
            </Label>
            <Input
              id="quick-name"
              placeholder="e.g., Gaming Community, Art Club"
              value={name}
              onChange={handleNameChange}
              maxLength={32}
              className={`bg-gray-800/50 border-gray-700/50 ${
                name.length > 0 ? (isNameValid ? "border-green-500" : "border-red-500") : ""
              }`}
            />
            <div className="flex justify-between text-xs">
              <span className={`${isNameValid ? "text-green-400" : "text-gray-400"}`}>
                {name.length > 0 && (isNameValid ? "✓ Valid name" : "Name must be 3-32 characters")}
              </span>
              <span className="text-gray-400">{name.length}/32</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quick-description" className="text-sm font-medium text-gray-300">
              Description
            </Label>
            <Textarea
              id="quick-description"
              placeholder="Briefly describe what your hub is about..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={200}
              rows={3}
              className="bg-gray-800/50 border-gray-700/50 resize-none"
            />
            <div className="text-xs text-gray-400 text-right">
              {description.length}/200
            </div>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg border border-gray-700/50">
            <div className="flex items-center gap-3">
              {isPrivate ? (
                <Lock className="h-4 w-4 text-orange-400" />
              ) : (
                <Globe className="h-4 w-4 text-green-400" />
              )}
              <div>
                <Label htmlFor="quick-private" className="text-sm font-medium text-gray-300">
                  {isPrivate ? "Private Hub" : "Public Hub"}
                </Label>
                <p className="text-xs text-gray-400">
                  {isPrivate
                    ? "Only visible to invited servers"
                    : "Discoverable in the hub directory"
                  }
                </p>
              </div>
            </div>
            <Switch
              id="quick-private"
              checked={isPrivate}
              onCheckedChange={setIsPrivate}
            />
          </div>

          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={onCancel}
              className="flex-1 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleQuickCreate}
              disabled={!isNameValid || !description.trim() || isCreating}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Hub
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Button
          variant="ghost"
          onClick={handleFullCreation}
          className="text-gray-400 hover:text-white"
        >
          <Settings className="h-4 w-4 mr-2" />
          Use Full Creation Flow
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}

interface OnboardingSuccessProps {
  hubName?: string;
  hubId?: string;
  onContinue?: () => void;
}

export function OnboardingSuccess({ hubName, hubId, onContinue }: OnboardingSuccessProps) {
  const { navigateToRoute } = useOnboarding();

  const handleViewHub = () => {
    if (hubId) {
      navigateToRoute(`/dashboard/hubs/${hubId}`);
    }
  };

  const handleViewDashboard = () => {
    navigateToRoute("/dashboard");
  };

  return (
    <div className="space-y-6 text-center">
      <div className="space-y-3">
        <div className="mx-auto w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
          <Check className="h-8 w-8 text-white" />
        </div>
        <h3 className="text-xl font-semibold text-white">Congratulations!</h3>
        {hubName ? (
          <p className="text-gray-300">
            Your hub <span className="font-semibold text-white">&quot;{hubName}&quot;</span> has been created successfully.
          </p>
        ) : (
          <p className="text-gray-300">
            You&apos;re all set up and ready to start using InterChat!
          </p>
        )}
      </div>

      <div className="space-y-3">
        <Badge variant="secondary" className="bg-green-900/30 text-green-300 border-green-500/30">
          🎉 Welcome to the InterChat community!
        </Badge>

        <div className="text-sm text-gray-400 space-y-1">
          <p>Next steps you might want to take:</p>
          <ul className="text-left space-y-1 max-w-md mx-auto">
            <li>• Customize your hub with banners and icons</li>
            <li>• Set up community rules and moderation</li>
            <li>• Invite Discord servers to join your hub</li>
            <li>• Explore other hubs in the directory</li>
          </ul>
        </div>
      </div>

      <div className="flex gap-3 justify-center">
        {hubId && (
          <Button
            onClick={handleViewHub}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
          >
            <Settings className="h-4 w-4 mr-2" />
            Customize Hub
          </Button>
        )}
        <Button
          onClick={onContinue || handleViewDashboard}
          variant="outline"
          className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50"
        >
          <ArrowRight className="h-4 w-4 mr-2" />
          Continue to Dashboard
        </Button>
      </div>
    </div>
  );
}
