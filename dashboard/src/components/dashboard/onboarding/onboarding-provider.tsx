"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import {
  InteractiveWelcomeContent,
  InteractiveHubManagementContent,
  InteractiveFirstHubContent,
  InteractiveCustomizationContent,
  InteractiveDiscoveryContent
} from "./interactive-onboarding-content";

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  content: ReactNode;
  canSkip?: boolean;
  targetElement?: string;
  position?: "center" | "top" | "bottom" | "left" | "right";
  actionType?: "navigation" | "modal" | "highlight" | "none";
  actionData?: {
    route?: string;
    external?: boolean;
    callback?: () => void;
  };
}

interface OnboardingContextType {
  isOnboardingActive: boolean;
  currentStep: number;
  steps: OnboardingStep[];
  isNewUser: boolean;
  startOnboarding: () => void;
  nextStep: () => void;
  previousStep: () => void;
  skipOnboarding: () => void;
  completeOnboarding: () => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
  progress: number;
  navigateToRoute: (route: string, external?: boolean) => void;
  pauseOnboarding: () => void;
  resumeOnboarding: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error("useOnboarding must be used within an OnboardingProvider");
  }
  return context;
}

interface OnboardingProviderProps {
  children: ReactNode;
  userHubCount?: number;
  userConnectionCount?: number;
}

export function OnboardingProvider({
  children,
  userHubCount = 0,
  userConnectionCount = 0
}: OnboardingProviderProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isOnboardingActive, setIsOnboardingActive] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isNewUser, setIsNewUser] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isPaused, setIsPaused] = useState(false);

  // Define onboarding steps
  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: "Welcome to InterChat Dashboard!",
      description: "Let's get you started with managing your cross-server communities",
      content: <InteractiveWelcomeContent />,
      position: "center"
    },
    {
      id: "hub-management",
      title: "Hub Management",
      description: "Learn about creating and managing your hubs",
      content: <InteractiveHubManagementContent />,
      targetElement: "hub-management-section",
      position: "right"
    },
    {
      id: "first-hub",
      title: "Create Your First Hub",
      description: "Let's help you create or connect to your first hub",
      content: <InteractiveFirstHubContent />,
      position: "center"
    },
    {
      id: "customization",
      title: "Customize Your Experience",
      description: "Learn about personalizing your hubs and settings",
      content: <InteractiveCustomizationContent />,
      targetElement: "customization-section",
      position: "left"
    },
    {
      id: "discovery",
      title: "Hub Discovery & Growth",
      description: "Learn how others will find and join your hub",
      content: <InteractiveDiscoveryContent />,
      position: "center"
    }
  ];

  // Check if user is new based on hub count, connection count, and localStorage
  useEffect(() => {
    if (!session?.user) return;

    const hasSeenDashboardOnboarding = localStorage.getItem("hasSeenDashboardOnboarding");
    const hasVisitedDashboard = localStorage.getItem("hasVisitedDashboard");

    // Consider user new if:
    // 1. They haven't seen onboarding before
    // 2. They have no hubs or connections
    // 3. This is their first dashboard visit
    const isUserNew = !hasSeenDashboardOnboarding &&
                     (userHubCount === 0 || userConnectionCount === 0 || !hasVisitedDashboard);

    setIsNewUser(isUserNew);

    // Mark that they've visited the dashboard
    if (!hasVisitedDashboard) {
      localStorage.setItem("hasVisitedDashboard", "true");
    }

    // Auto-start onboarding for new users after a short delay
    if (isUserNew) {
      const timer = setTimeout(() => {
        setIsOnboardingActive(true);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [session, userHubCount, userConnectionCount]);

  const startOnboarding = () => {
    setCurrentStep(0);
    setIsOnboardingActive(true);
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeOnboarding();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipOnboarding = () => {
    localStorage.setItem("hasSeenDashboardOnboarding", "true");
    setIsOnboardingActive(false);
  };

  const completeOnboarding = () => {
    localStorage.setItem("hasSeenDashboardOnboarding", "true");
    setIsOnboardingActive(false);
  };

  const navigateToRoute = (route: string, external = false) => {
    if (external) {
      window.open(route, '_blank', 'noopener,noreferrer');
    } else {
      router.push(route);
    }
  };

  const pauseOnboarding = () => {
    setIsPaused(true);
  };

  const resumeOnboarding = () => {
    setIsPaused(false);
  };

  const canGoNext = currentStep < steps.length - 1;
  const canGoPrevious = currentStep > 0;
  const progress = ((currentStep + 1) / steps.length) * 100;

  const contextValue: OnboardingContextType = {
    isOnboardingActive,
    currentStep,
    steps,
    isNewUser,
    startOnboarding,
    nextStep,
    previousStep,
    skipOnboarding,
    completeOnboarding,
    canGoNext,
    canGoPrevious,
    progress,
    navigateToRoute,
    pauseOnboarding,
    resumeOnboarding
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}
