"use client";

import { Server<PERSON>rash, Shield, UserX } from "lucide-react";
import { ModernHero } from "../shared/modern-hero";

interface BlacklistHeroProps {
  totalBlacklisted: number;
  blacklistedUsers: number;
  blacklistedServers: number;
}

export function BlacklistHero({
  totalBlacklisted,
  blacklistedUsers,
  blacklistedServers,
}: BlacklistHeroProps) {
  return (
    <ModernHero
      title="Blacklist Management"
      subtitle="Manage blacklisted users and servers across your hubs"
      stats={[
        {
          icon: <Shield className="h-5 w-5 text-red-400" />,
          value: totalBlacklisted,
          label: "Total",
          color: "bg-none",
        },
        {
          icon: <UserX className="h-5 w-5 text-orange-400" />,
          value: blacklistedUsers,
          label: "Users",
          color: "bg-none",
        },
        {
          icon: <ServerCrash className="h-5 w-5 text-purple-400" />,
          value: blacklistedServers,
          label: "Servers",
          color: "bg-none",
        },
      ]}
      gradientColors={{
        from: "from-red-900/30",
        via: "via-orange-900/20",
        to: "to-purple-900/30",
      }}
      particleColors={["bg-red-500/20", "bg-orange-500/20", "bg-purple-500/20"]}
    />
  );
}
