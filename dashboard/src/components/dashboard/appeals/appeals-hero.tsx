"use client";

import { MessageCircle, CheckCircle, XCircle } from "lucide-react";
import { ModernHero } from "../shared/modern-hero";

interface AppealsHeroProps {
  totalAppeals: number;
  pendingAppeals: number;
  resolvedAppeals: number;
}

export function AppealsHero({
  totalAppeals,
  pendingAppeals,
  resolvedAppeals,
}: AppealsHeroProps) {
  return (
    <ModernHero
      title="Appeals Management"
      subtitle="Review and manage appeals for blacklisted users and servers"
      stats={[
        {
          icon: <MessageCircle className="h-5 w-5 text-blue-400" />,
          value: totalAppeals,
          label: "Total",
          color: "bg-none",
        },
        {
          icon: <XCircle className="h-5 w-5 text-orange-400" />,
          value: pendingAppeals,
          label: "Pending",
          color: "bg-none",
        },
        {
          icon: <CheckCircle className="h-5 w-5 text-green-400" />,
          value: resolvedAppeals,
          label: "Resolved",
          color: "bg-none",
        },
      ]}
      gradientColors={{
        from: "from-green-900/30",
        via: "via-blue-900/20",
        to: "to-indigo-900/30",
      }}
      particleColors={[
        "bg-green-500/20",
        "bg-blue-500/20",
        "bg-indigo-500/20",
      ]}
    />
  );
}
