"use client";

import { Skeleton } from "@/components/ui/skeleton";

export function AnimatedServersHeroSkeleton() {
  return (
    <div className="relative min-h-[250px] h-auto sm:h-[30vh] md:h-[35vh] mb-8 overflow-hidden rounded-xl shadow-lg">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/30 via-indigo-900/20 to-purple-900/30 z-0" />
      
      {/* Mesh gradient overlay */}
      <div className="absolute inset-0 bg-mesh-gradient opacity-30 mix-blend-overlay z-0" />
      
      <div className="relative z-10 h-full flex flex-col items-center justify-center text-center p-6">
        <div className="max-w-4xl mx-auto">
          <Skeleton className="h-8 w-64 mx-auto mb-2" />
          <Skeleton className="h-4 w-80 mx-auto mb-8" />
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 w-full px-2 mb-6">
            {[1, 2, 3].map((index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center gap-2 bg-gray-900/60 backdrop-blur-md px-4 py-4 rounded-xl border border-gray-700/50 shadow-lg"
              >
                <div className="p-2 rounded-full bg-opacity-20 mb-1">
                  <Skeleton className="h-5 w-5 rounded-full" />
                </div>
                <Skeleton className="h-6 w-12" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
