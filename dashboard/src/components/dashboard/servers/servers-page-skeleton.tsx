"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsContent } from "@/components/ui/tabs";
import { ServersSkeleton } from "./servers-skeleton";
import { ConnectionsSkeleton } from "./connections-skeleton";
import { AnimatedServersHeroSkeleton } from "./animated-servers-hero-skeleton";
import { UnderlinedTabsSkeleton } from "@/components/dashboard/underlined-tabs-skeleton";

export function ServersPageSkeleton() {
  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <AnimatedServersHeroSkeleton />

      {/* Optional Hub Selection Card */}
      <Card className="border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardContent className="pt-6 pb-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-64 mt-1" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search Input */}
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-10 w-full max-w-md" />
        <Skeleton className="h-10 w-32" />
      </div>

      <UnderlinedTabsSkeleton
        tabs={[
          {
            value: "all",
            label: "All Servers",
            color: "indigo",
          },
          {
            value: "connected",
            label: "Connected",
            color: "blue",
          },
          {
            value: "not-connected",
            label: "Not Connected",
            color: "purple",
          },
          {
            value: "connections",
            label: "Connections",
            color: "green",
          },
        ]}
      >
        <TabsContent value="all" className="space-y-6">
          <ServersSkeleton />
        </TabsContent>

        <TabsContent value="connected" className="space-y-6">
          <ServersSkeleton />
        </TabsContent>

        <TabsContent value="not-connected" className="space-y-6">
          <ServersSkeleton />
        </TabsContent>

        <TabsContent value="connections" className="space-y-6">
          <ConnectionsSkeleton />
        </TabsContent>
      </UnderlinedTabsSkeleton>
    </div>
  );
}
