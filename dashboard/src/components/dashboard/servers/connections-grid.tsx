"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import { formatDistanceToNow } from "date-fns";
import { motion } from "motion/react";
import { Clock, Home, Link2, MessageSquare, Settings } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useInView } from "react-intersection-observer";

interface Props {
  connections: (Connection & {
    hub: Hub;
    server: ServerData | null;
  })[];
}

export function ConnectionsGrid({ connections }: Props) {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter connections based on search query
  const filteredConnections = connections.filter(
    (connection) =>
      connection.hub.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      connection.server?.name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()),
  );

  if (connections.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="rounded-full bg-gray-800/50 p-3 mb-4">
          <MessageSquare className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium mb-2">No connections found</h3>
        <p className="text-gray-400 max-w-md mb-4">
          You don&apos;t have any connections between your servers and hubs yet.
        </p>
        <Button
          asChild
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
        >
          <Link href="/dashboard/servers">Manage Servers</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="relative">
        <Input
          type="text"
          placeholder="Search connections..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 pl-10"
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {filteredConnections.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-gray-800/50 p-3 mb-4">
            <MessageSquare className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No matching connections</h3>
          <p className="text-gray-400 max-w-md">
            No connections match your search query. Try a different search term.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredConnections.map((connection, index) => (
            <ConnectionCard
              key={connection.id}
              connection={connection}
              index={index}
            />
          ))}
        </div>
      )}
    </div>
  );
}

interface ConnectionCardProps {
  connection: Props["connections"][0];
  index: number;
}

function ConnectionCard({ connection, index }: ConnectionCardProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  // Format times
  const createdAt = formatDistanceToNow(new Date(connection.createdAt), {
    addSuffix: true,
  });
  const lastActive = connection.lastActive
    ? formatDistanceToNow(new Date(connection.lastActive), { addSuffix: true })
    : "Never";

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className="h-full"
    >
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm overflow-hidden flex flex-col h-full transition-all duration-300">
        <CardHeader className="pb-2 relative">
          <div className="flex items-center justify-between">
            <Badge
              variant={connection.connected ? "default" : "destructive"}
              className={`mb-2 ${connection.connected ? "bg-green-500/20 text-green-300 hover:bg-green-500/30 border-green-500/30" : ""}`}
            >
              {connection.connected ? "Connected" : "Disconnected"}
            </Badge>
            <span className="text-xs text-gray-400">Created {createdAt}</span>
          </div>
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="h-12 w-12 rounded-full border-2 border-gray-700/50 overflow-hidden">
                <Image
                  src={connection.hub.iconUrl}
                  alt={connection.hub.name}
                  width={48}
                  height={48}
                  className="object-cover"
                  style={{ width: "100%", height: "100%" }}
                />
              </div>
            </motion.div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg truncate">
                {connection.hub.name}
              </CardTitle>
              <CardDescription className="truncate">Hub</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="py-4 flex-grow">
          <div className="flex items-center gap-3 mb-4">
            <div className="flex-1 min-w-0">
              <div className="font-medium truncate">
                {connection.server?.name}
              </div>
              <div className="text-xs text-gray-400">Server</div>
            </div>
          </div>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Link2 className="h-3 w-3 text-blue-400" />
                </div>
                Channel ID
              </span>
              <span className="font-mono text-xs text-gray-200">
                {connection.channelId}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-6 w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Clock className="h-3 w-3 text-gray-400" />
                </div>
                Last Active
              </span>
              <span className="text-gray-200">{lastActive}</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-2 pb-4 border-t border-gray-800/50 flex gap-2">
          <Button
            asChild
            className="flex-1 bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border-none"
          >
            <Link href={`/dashboard/servers/${connection.serverId}`}>
              <Home className="h-4 w-4 mr-2" />
              Server
            </Link>
          </Button>
          <Button
            asChild
            className="flex-1 bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border-none"
          >
            <Link href={`/dashboard/hubs/${connection.hubId}`}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Hub
            </Link>
          </Button>
          <Button
            asChild
            className="flex-1 bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border-none"
          >
            <Link href={`/dashboard/connections/${connection.id}/edit`}>
              <Settings className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
