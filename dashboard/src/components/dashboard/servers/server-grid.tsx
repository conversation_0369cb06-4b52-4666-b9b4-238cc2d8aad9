"use client";

import { ServerDataWithConnections } from "@/actions/server-actions";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { formatDistanceToNow } from "date-fns";
import { motion } from "motion/react";
import { Clock, ExternalLink, Home, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useInView } from "react-intersection-observer";

interface ServerGridProps {
  servers: ServerDataWithConnections[];
  showConnectButton?: boolean;
  selectedHubId?: string;
}

export function ServerGrid({
  servers,
  showConnectButton = false,
  selectedHubId,
}: ServerGridProps) {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter servers based on search query
  const filteredServers = servers.filter((server) =>
    server.name?.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="space-y-6">
      <div className="relative">
        <Input
          type="text"
          placeholder="Search servers..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 pl-10"
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {filteredServers.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-gray-800/50 p-3 mb-4">
            <Home className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No matching servers</h3>
          <p className="text-gray-400 max-w-md">
            {selectedHubId && showConnectButton
              ? "No servers match your search query. Try a different search term or add the bot to more servers."
              : "No servers match your search query. Try a different search term."}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
          {filteredServers.map((server, index) => (
            <ServerCard
              key={server.id}
              server={server}
              showConnectButton={showConnectButton}
              selectedHubId={selectedHubId}
              index={index}
            />
          ))}
        </div>
      )}
    </div>
  );
}

interface ServerCardProps {
  server: ServerDataWithConnections;
  showConnectButton?: boolean;
  selectedHubId?: string;
  index: number;
}

function ServerCard({
  server,
  showConnectButton = false,
  selectedHubId,
  index,
}: ServerCardProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Get server icon URL
  const iconUrl = server.icon
    ? `https://cdn.discordapp.com/icons/${server.id}/${server.icon}.png?size=128`
    : `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
        server.id,
      )}`;

  // Format last active time
  const lastActive = server.lastMessageAt
    ? formatDistanceToNow(new Date(server.lastMessageAt), { addSuffix: true })
    : "Never";

  // Check if user is owner
  const isOwner = server.owner;

  // Get connection count
  const connectionCount = server.connections?.length || 0;

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 10 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
      transition={{ duration: 0.3, delay: Math.min(index * 0.03, 0.3) }} // Cap the delay to avoid long waits
      whileHover={{ y: -3, transition: { duration: 0.2 } }} // Reduce hover movement
      className="h-full"
    >
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm overflow-hidden flex flex-col h-full transition-all duration-300 min-h-[280px]">
        <CardHeader className="pb-2 relative">
          <div className="absolute top-3 right-3">
            {isOwner && (
              <div className="px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300 border border-yellow-500/30">
                Owner
              </div>
            )}
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <div>
              <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-full border-2 border-gray-700/50 overflow-hidden flex-shrink-0">
                <Image
                  src={iconUrl}
                  alt={server.name}
                  width={48}
                  height={48}
                  className="object-cover"
                  style={{ width: "100%", height: "100%" }}
                />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-base sm:text-lg truncate">{server.name}</CardTitle>
              <CardDescription className="truncate text-xs sm:text-sm">
                {server.id}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="py-3 sm:py-4 flex-grow">
          <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm max-w-full">
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Users className="h-3 w-3 text-blue-400" />
                </div>
                <span className="hidden sm:inline">Connections</span>
                <span className="sm:hidden">Conn.</span>
              </span>
              <span className="text-gray-200">{connectionCount}</span>
            </div>
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Clock className="h-3 w-3 text-gray-400" />
                </div>
                <span className="hidden sm:inline">Last Active</span>
                <span className="sm:hidden">Active</span>
              </span>
              <span className="text-gray-200 text-right truncate max-w-[120px] sm:max-w-none">{lastActive}</span>
            </div>
            <div className="flex justify-between items-center w-full">
              <span className="text-gray-400 flex items-center gap-1">
                <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-gray-800/80 flex items-center justify-center">
                  <Home className="h-3 w-3 text-indigo-400" />
                </div>
                <span className="hidden sm:inline">Bot Added</span>
                <span className="sm:hidden">Bot</span>
              </span>
              <span
                className={server.botAdded ? "text-green-400" : "text-red-400"}
              >
                {server.botAdded ? "Yes" : "No"}
              </span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0 pb-3 sm:pb-4 border-t border-gray-800/50">
          {showConnectButton && server.botAdded ? (
            // Check if server is already connected to the selected hub
            server.connections.some((conn) => conn.hubId === selectedHubId) ? (
              <Button
                disabled
                className="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-600 hover:to-gray-700 border-none cursor-not-allowed opacity-70"
              >
                <span className="hidden sm:inline">Already Connected</span>
                <span className="sm:hidden">Connected</span>
              </Button>
            ) : (
              <Button
                asChild
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
              >
                <Link
                  href={`/dashboard/servers/${server.id}/connect?hubId=${selectedHubId}`}
                >
                  <span className="hidden sm:inline">Connect to Hub</span>
                  <span className="sm:hidden">Connect</span>
                </Link>
              </Button>
            )
          ) : server.botAdded ? (
            <Button
              asChild
              className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border-none"
            >
              <Link href={`/dashboard/servers/${server.id}`}>
                <Home className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Manage Server</span>
                <span className="sm:hidden">Manage</span>
              </Link>
            </Button>
          ) : (
            <Button
              asChild
              className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 border-none"
            >
              <Link
                href={`https://discord.com/oauth2/authorize?client_id=769921109209907241&guild_id=${server.id}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Bot</span>
                <span className="sm:hidden">Add</span>
              </Link>
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
}
