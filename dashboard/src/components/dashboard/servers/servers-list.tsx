"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { ServerData } from "@/lib/generated/prisma/client";
import { formatDistanceToNow } from "date-fns";
import { ExternalLink, MessageSquare } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

type Server = ServerData & { _count: { connections: number } };

interface ServersProps {
  servers: Server[];
}

export function ServersList({ servers }: ServersProps) {
  if (servers.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="rounded-full bg-gray-800/50 p-3 mb-4">
          <MessageSquare className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium mb-2">No servers found</h3>
        <p className="text-gray-400 max-w-md mb-4">
          There are no servers matching your criteria. Try changing your search
          or filters.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {servers.map((server) => (
        <ServerCard key={server.id} server={server} />
      ))}
    </div>
  );
}

function ServerCard({ server }: { server: Server }) {
  const lastActive = server.lastMessageAt
    ? formatDistanceToNow(new Date(server.lastMessageAt), { addSuffix: true })
    : "Never";

  const iconUrl = `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
    server.id,
  )}`;

  return (
    <Card className="border-gray-800 bg-[#0f1117] overflow-hidden flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-3">
          <Image
            src={iconUrl}
            alt={server.name || "Unknown Server"}
            width={40}
            height={40}
            className="rounded-full"
          />
          <div>
            <CardTitle className="text-lg">
              {server.name || "Unknown Server"}
            </CardTitle>
            <CardDescription>
              {server._count.connections} connections
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="py-4 flex-grow">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Last Active:</span>
            <span>{lastActive}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Messages:</span>
            <span>{server.messageCount.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Status:</span>
            <span className={server.premiumStatus ? "text-yellow-400" : ""}>
              {server.premiumStatus ? "Premium" : "Free"}
            </span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="pt-2 pb-4 border-t border-gray-800">
        <Button asChild className="w-full">
          <Link href={`/dashboard/servers/${server.id}`}>
            View Details
            <ExternalLink className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
