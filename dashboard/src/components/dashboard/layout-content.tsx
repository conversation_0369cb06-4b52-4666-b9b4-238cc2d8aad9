"use client";

import { AutoBreadcrumb } from "@/components/dashboard/breadcrumb";
import { DashboardTopBar } from "@/components/dashboard/topbar";
import { motion } from "motion/react";
import type { User } from "next-auth";
import type { ReactNode } from "react";
import { useState, useEffect } from "react";
import { useDashboardLayout } from "./layout-provider";
import { usePathname } from "next/navigation";

interface DashboardLayoutContentProps {
  user: User;
  children: ReactNode;
}

export function DashboardLayoutContent({ user, children }: DashboardLayoutContentProps) {
  const { sidebarWidth, hubSidebarWidth } = useDashboardLayout();
  const [isDesktop, setIsDesktop] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024); // lg breakpoint to match hub sidebar
    };

    checkIsDesktop();
    window.addEventListener('resize', checkIsDesktop);

    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  // Check if we're on a hub page that would have a hub sidebar
  const isHubPage = pathname?.startsWith('/dashboard/hubs/') && pathname.split('/').length > 3;

  return (
    <motion.div
      className="flex flex-col flex-1 overflow-hidden"
      initial={false}
      animate={{
        marginLeft: isDesktop ? sidebarWidth : 0
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Top bar */}
      <DashboardTopBar user={user} />

      {/* Main content with scrolling */}
      <main className="flex-1 overflow-y-auto dashboard-scrollbar bg-gradient-to-b from-gray-900 via-gray-900/95 to-gray-950 p-6 relative">
        {/* Background pattern - using pointer-events-none to allow clicks to pass through */}
        <div className="fixed inset-0 z-0 opacity-5 pointer-events-none">
          <div
            className="absolute inset-0 bg-grid-white bg-[size:30px_30px] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]"
            style={{ zIndex: -1 }}
          />
        </div>

        {/* Content */}
        <motion.div
          className="relative z-10"
          initial={false}
          animate={{
            marginLeft: isDesktop && isHubPage ? hubSidebarWidth : 0
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <AutoBreadcrumb />
          {children}
        </motion.div>
      </main>
    </motion.div>
  );
}
