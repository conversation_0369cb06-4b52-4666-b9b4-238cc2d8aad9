'use client';

import { motion } from 'motion/react';
import { HubMobileDropdown } from './hub-mobile-dropdown';
import { HubSidebar } from './hub-sidebar';
import { UnifiedHubHeader } from './unified-hub-header';
import { useDashboardLayout } from '../layout-provider';

interface HubLayoutProps {
  hub: {
    id: string;
    name: string;
    description: string;
    iconUrl: string;
    bannerUrl: string | null;
    private: boolean;
    nsfw: boolean;
    connectionCount: number;
  };
  currentTab: string;
  canModerate?: boolean;
  canEdit?: boolean;
  showBackButton?: boolean;
  backHref?: string;
  headerActions?: React.ReactNode;
  onHubUpdate?: (updates: { iconUrl?: string; bannerUrl?: string }) => void;
  children: React.ReactNode;
}

export function HubLayout({
  hub,
  canModerate = false,
  canEdit = false,
  showBackButton = true,
  backHref = '/dashboard/hubs',
  headerActions,
  onHubUpdate,
  children,
}: HubLayoutProps) {
  // Get dashboard layout context for main sidebar width and hub sidebar state
  const { sidebarWidth, isHydrated, hubSidebarCollapsed, setHubSidebarCollapsed } = useDashboardLayout();

  return (
    <div className="relative">
      {/* Fixed Hub Sidebar - Desktop only */}
      {isHydrated && (
        <div
          className={`hidden lg:block fixed top-16 bottom-0 z-30 transition-all duration-300 ${
            hubSidebarCollapsed ? 'w-16' : 'w-64'
          }`}
          style={{
            left: `${sidebarWidth}px`, // Position after main dashboard sidebar
          }}
        >
          <HubSidebar
            hubId={hub.id}
            canModerate={canModerate}
            canEdit={canEdit}
            isCollapsed={hubSidebarCollapsed}
            onToggleCollapse={() => setHubSidebarCollapsed(!hubSidebarCollapsed)}
          />
        </div>
      )}

      {/* Main Content Area - No margin needed since DashboardLayoutContent handles spacing */}
      <div className="transition-all duration-300">
        {/* Unified Hub Header */}
        <UnifiedHubHeader
          hub={hub}
          showBackButton={showBackButton}
          backHref={backHref}
          actions={headerActions}
          canEdit={canEdit}
          onHubUpdate={onHubUpdate}
        />

        {/* Mobile Navigation Dropdown - Enhanced for maximum visibility */}
        <div className="lg:hidden mt-8">
          <div className="sticky top-0 z-20 bg-gradient-to-r from-gray-900/98 via-gray-900/95 to-gray-900/98 backdrop-blur-md border-b border-indigo-500/30 p-4 -mx-4 mb-4 shadow-lg">
            <HubMobileDropdown
              hubId={hub.id}
              canModerate={canModerate}
              canEdit={canEdit}
            />

            {/* Helper text */}
            <motion.p
              className="text-xs text-gray-400 text-center mt-2 flex items-center justify-center gap-1"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.3 }}
            >
              <span>👆</span> Tap to access hub features
            </motion.p>
          </div>
        </div>

        {/* Page Content */}
        <div className="mt-8">
          <div className="space-y-6">{children}</div>
        </div>
      </div>


    </div>
  );
}
