"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import {
  Copy,
  Hash,
  Home,
  MoreHorizontal,
  ShieldAlert,
  Trash
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface ConnectionItemProps {
  connection: {
    id: string;
    serverId: string;
    channelId: string;
    server?: {
      name: string;
    } | null;
  };
  canEdit: boolean;
  hubId: string;
}

export function ConnectionItem({
  connection,
  canEdit,
  hubId,
}: ConnectionItemProps) {
  const { toast } = useToast();
  const [isRemoving, setIsRemoving] = useState(false);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: `${label} copied to clipboard`,
      description: text,
      duration: 3000,
    });
  };

  const handleRemoveConnection = async () => {
    if (!confirm("Are you sure you want to remove this connection?")) return;

    setIsRemoving(true);
    try {
      const response = await fetch(
        `/api/hubs/${hubId}/connections/${connection.id}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to remove connection");
      }

      toast({
        title: "Connection removed",
        description: "The server has been disconnected from this hub",
        duration: 3000,
      });

      // Refresh the page to show updated connections
      window.location.reload();
    } catch {
      toast({
        title: "Error",
        description: "Failed to remove connection",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-md bg-gray-900/50 border border-gray-800/50 gap-4">
      <div className="flex items-center gap-3">
        <div className="relative flex-shrink-0">
          <div className="h-10 w-10 rounded-md bg-gray-800/80 border border-gray-700/50 flex items-center justify-center">
            {connection.server ? (
              <Image
                src={`https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
                  connection.serverId,
                )}`}
                alt={connection.server.name || "Server"}
                width={40}
                height={40}
                className="rounded-md"
                unoptimized
              />
            ) : (
              <Home className="h-5 w-5 text-gray-400" />
            )}
          </div>
          <div className="absolute -bottom-1 -right-1 bg-gray-900/80 rounded-full p-0.5 border border-gray-700/50">
            <Hash className="h-3.5 w-3.5 text-blue-400" />
          </div>
        </div>
        <div className="min-w-0 flex-1">
          <div className="font-medium truncate">
            {connection.server?.name || "Unknown Server"}
          </div>
          <div className="text-xs text-gray-400 truncate">
            Channel ID: {connection.channelId}
          </div>
        </div>
      </div>
      {canEdit && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-800/50"
              disabled={isRemoving}
            >
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50"
          >
            <DropdownMenuItem
              onClick={() => handleRemoveConnection()}
              className="text-red-400 hover:text-red-300 cursor-pointer"
              disabled={isRemoving}
            >
              {isRemoving ? (
                <>
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-red-400 border-t-transparent" />
                  Removing...
                </>
              ) : (
                <>
                  <Trash className="h-4 w-4 mr-2" />
                  Remove connection
                </>
              )}
            </DropdownMenuItem>

            <DropdownMenuSeparator className="bg-gray-800/50" />

            <DropdownMenuItem asChild>
              <Link
                href={`/dashboard/moderation/blacklist/add?serverId=${connection.serverId}`}
                className="cursor-pointer"
              >
                <ShieldAlert className="h-4 w-4 mr-2 text-orange-400" />
                Blacklist server
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator className="bg-gray-800/50" />

            <DropdownMenuItem
              onClick={() => copyToClipboard(connection.serverId, "Server ID")}
              className="cursor-pointer"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy server ID
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={() =>
                copyToClipboard(connection.channelId, "Channel ID")
              }
              className="cursor-pointer"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy channel ID
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
