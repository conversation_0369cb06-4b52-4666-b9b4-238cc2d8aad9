"use client";

import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, AlertTriangle, Trash, Unplug, Users, Shield } from "lucide-react";
import { useState } from "react";

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void | Promise<void>;
  title: string;
  description: string;
  confirmText?: string;
  confirmButtonText?: string;
  confirmButtonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  requiresTextConfirmation?: boolean;
  textConfirmationValue?: string;
  textConfirmationLabel?: string;
  textConfirmationPlaceholder?: string;
  isLoading?: boolean;
  icon?: "warning" | "danger" | "disconnect" | "users" | "shield";
}

const iconMap = {
  warning: AlertTriangle,
  danger: Trash,
  disconnect: Unplug,
  users: Users,
  shield: Shield,
};

const iconColorMap = {
  warning: "text-amber-400",
  danger: "text-red-400",
  disconnect: "text-orange-400",
  users: "text-blue-400",
  shield: "text-purple-400",
};

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  // confirmText = "Confirm", // Unused
  confirmButtonText = "Confirm",
  confirmButtonVariant = "destructive",
  requiresTextConfirmation = false,
  textConfirmationValue = "",
  textConfirmationLabel = "Type to confirm",
  textConfirmationPlaceholder = "Type here...",
  isLoading = false,
  icon = "warning",
}: ConfirmationDialogProps) {
  const [confirmationText, setConfirmationText] = useState("");

  const IconComponent = iconMap[icon];
  const iconColorClass = iconColorMap[icon];

  const isConfirmValid = requiresTextConfirmation
    ? confirmationText === textConfirmationValue
    : true;

  const handleConfirm = async () => {
    if (!isConfirmValid) return;

    try {
      await onConfirm();
    } catch (error) {
      console.error('Confirmation action failed:', error);
    }
  };

  const handleClose = () => {
    setConfirmationText("");
    onClose();
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="bg-gray-900 border border-gray-800 max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className={`p-2 rounded-full bg-gray-800/50`}>
              <IconComponent className={`h-5 w-5 ${iconColorClass}`} />
            </div>
            <AlertDialogTitle className="text-white text-lg">
              {title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-gray-400 text-left">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {requiresTextConfirmation && (
          <div className="space-y-2">
            <Label htmlFor="confirmation-input" className="text-sm text-gray-300">
              {textConfirmationLabel}
            </Label>
            <Input
              id="confirmation-input"
              type="text"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={textConfirmationPlaceholder}
              className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-500 focus:border-gray-600"
              disabled={isLoading}
              autoComplete="off"
            />
            {textConfirmationValue && (
              <p className="text-xs text-gray-500">
                Type &quot;<span className="font-mono text-gray-400">{textConfirmationValue}</span>&quot; to confirm
              </p>
            )}
          </div>
        )}

        <AlertDialogFooter className="flex gap-2 sm:gap-2">
          <AlertDialogCancel
            onClick={handleClose}
            disabled={isLoading}
            className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
          >
            Cancel
          </AlertDialogCancel>
          <Button
            variant={confirmButtonVariant}
            onClick={handleConfirm}
            disabled={!isConfirmValid || isLoading}
            className={
              confirmButtonVariant === "destructive"
                ? "bg-red-600 hover:bg-red-700 text-white"
                : ""
            }
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              confirmButtonText
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Preset configurations for common use cases
export const ConfirmationDialogPresets = {

  removeConnection: (serverName: string) => ({
    title: "Remove Connection",
    description: `This will permanently remove "${serverName}" from this hub. All connection history and settings will be lost. This action cannot be undone.`,
    confirmButtonText: "Remove Connection",
    confirmButtonVariant: "destructive" as const,
    requiresTextConfirmation: false,
    icon: "danger" as const,
  }),



  bulkRemove: (count: number) => ({
    title: "Remove Multiple Connections",
    description: `This will permanently remove ${count} connection${count !== 1 ? 's' : ''} from this hub. All connection history and settings will be lost. This action cannot be undone.`,
    confirmButtonText: `Remove ${count} Connection${count !== 1 ? 's' : ''}`,
    confirmButtonVariant: "destructive" as const,
    requiresTextConfirmation: false,
    icon: "danger" as const,
  }),


};
