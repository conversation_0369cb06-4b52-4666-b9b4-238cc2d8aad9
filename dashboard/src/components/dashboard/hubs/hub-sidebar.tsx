'use client';

import { Button } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Ban,
  Bell,
  ChevronLeft,
  ChevronRight,
  Edit,
  FileText,
  Home,
  MessageSquare,
  Settings,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface HubSidebarProps {
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface SidebarNavItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  active: boolean;
  isCollapsed: boolean;
  color?: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange';
}

function SidebarNavItem({
  href,
  icon: Icon,
  label,
  active,
  isCollapsed,
  color = 'indigo',
}: SidebarNavItemProps) {
  const colorMap = {
    indigo: 'bg-gradient-to-r from-indigo-500/20 to-purple-500/10 text-white',
    blue: 'bg-gradient-to-r from-blue-500/20 to-indigo-500/10 text-white',
    green: 'bg-gradient-to-r from-green-500/20 to-emerald-500/10 text-white',
    purple: 'bg-gradient-to-r from-purple-500/20 to-pink-500/10 text-white',
    red: 'bg-gradient-to-r from-red-500/20 to-pink-500/10 text-white',
    pink: 'bg-gradient-to-r from-pink-500/20 to-rose-500/10 text-white',
    orange: 'bg-gradient-to-r from-orange-500/20 to-red-500/10 text-white',
  };

  const iconColorMap = {
    indigo: 'bg-indigo-500/20 text-indigo-400',
    blue: 'bg-blue-500/20 text-blue-400',
    green: 'bg-green-500/20 text-green-400',
    purple: 'bg-purple-500/20 text-purple-400',
    red: 'bg-red-500/20 text-red-400',
    pink: 'bg-pink-500/20 text-pink-400',
    orange: 'bg-orange-500/20 text-orange-400',
  };

  const content = (
    <Link
      href={href}
      className={cn(
        'flex items-center rounded-lg px-3 py-2.5 text-sm transition-all relative',
        isCollapsed ? 'justify-center' : 'justify-between',
        active
          ? `${colorMap[color]} font-medium`
          : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
      )}
    >
      <div className={cn('flex items-center', isCollapsed ? 'justify-center' : 'gap-3')}>
        <div
          className={cn(
            'flex items-center justify-center w-6 h-6 rounded-md',
            active ? iconColorMap[color] : 'text-gray-400'
          )}
        >
          <Icon className="h-4 w-4" />
        </div>
        {!isCollapsed && <span>{label}</span>}
      </div>
      {active && (
        <motion.div
          layoutId="hubActiveIndicator"
          className="absolute right-0 w-1 h-8 bg-indigo-500 rounded-l-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </Link>
  );

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {content}
          </TooltipTrigger>
          <TooltipContent side="right" className="ml-2">
            <p>{label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
}

interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsed: boolean;
  defaultOpen?: boolean;
}

function SidebarSection({
  title,
  children,
  isCollapsed,
  defaultOpen = true,
}: SidebarSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  if (isCollapsed) {
    return <div className="space-y-1">{children}</div>;
  }

  return (
    <div className="py-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-1.5 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
      >
        <span>{title}</span>
        <motion.div
          animate={{ rotate: isOpen ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronRight className="h-3.5 w-3.5" />
        </motion.div>
      </button>
      <motion.div
        className="mt-1 space-y-1 overflow-hidden"
        initial={{ height: defaultOpen ? 'auto' : 0 }}
        animate={{ height: isOpen ? 'auto' : 0 }}
        transition={{ duration: 0.2 }}
      >
        {children}
      </motion.div>
    </div>
  );
}

export function HubSidebar({
  hubId,
  canModerate = false,
  canEdit = false,
  isCollapsed = false,
  onToggleCollapse,
}: HubSidebarProps) {
  const pathname = usePathname();

  // Define all navigation items with their permissions
  const navigationItems = [
    {
      value: 'overview',
      label: 'Overview',
      color: 'indigo' as const,
      icon: MessageSquare,
      href: `/dashboard/hubs/${hubId}`,
      section: 'main',
      show: true,
    },
    {
      value: 'edit',
      label: 'Edit Hub',
      color: 'blue' as const,
      icon: Edit,
      href: `/dashboard/hubs/${hubId}/edit`,
      section: 'management',
      show: canEdit,
    },
    {
      value: 'members',
      label: 'Members',
      color: 'blue' as const,
      icon: Users,
      href: `/dashboard/hubs/${hubId}/members`,
      section: 'management',
      show: canModerate,
    },
    {
      value: 'connections',
      label: 'Connections',
      color: 'green' as const,
      icon: Home,
      href: `/dashboard/hubs/${hubId}/connections`,
      section: 'management',
      show: canModerate,
    },
    {
      value: 'reports',
      label: 'Reports',
      color: 'purple' as const,
      icon: Shield,
      href: `/dashboard/hubs/${hubId}/reports`,
      section: 'moderation',
      show: canModerate,
    },
    {
      value: 'blacklists',
      label: 'Blacklists',
      color: 'red' as const,
      icon: Ban,
      href: `/dashboard/hubs/${hubId}/blacklists`,
      section: 'moderation',
      show: canModerate,
    },
    {
      value: 'infractions',
      label: 'Infractions',
      color: 'orange' as const,
      icon: AlertTriangle,
      href: `/dashboard/hubs/${hubId}/infractions`,
      section: 'moderation',
      show: canModerate,
    },
    {
      value: 'appeals',
      label: 'Appeals',
      color: 'pink' as const,
      icon: Bell,
      href: `/dashboard/hubs/${hubId}/appeals`,
      section: 'moderation',
      show: canModerate,
    },
    {
      value: 'anti-swear',
      label: 'Anti-Swear',
      color: 'red' as const,
      icon: AlertTriangle,
      href: `/dashboard/hubs/${hubId}/anti-swear`,
      section: 'moderation',
      show: canModerate,
    },
    {
      value: 'logging',
      label: 'Logging',
      color: 'purple' as const,
      icon: FileText,
      href: `/dashboard/hubs/${hubId}/logging`,
      section: 'management',
      show: canEdit,
    },
    {
      value: 'settings',
      label: 'Settings',
      color: 'orange' as const,
      icon: Settings,
      href: `/dashboard/hubs/${hubId}/settings`,
      section: 'misc',
      show: true,
    },
  ];

  // Filter items based on permissions
  const visibleItems = navigationItems.filter(item => item.show);

  // Group items by section
  const mainItems = visibleItems.filter(item => item.section === 'main');
  const managementItems = visibleItems.filter(item => item.section === 'management');
  const moderationItems = visibleItems.filter(item => item.section === 'moderation');
  const miscItems = visibleItems.filter(item => item.section === 'misc');

  return (
    <motion.div
      className="flex flex-col h-full bg-gradient-to-b from-gray-900 to-gray-950 border-r border-gray-800/50 shadow-lg"
      initial={{ x: -20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {/* Header with improved visual hierarchy */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800/50 flex-shrink-0 bg-gray-900/50">
        {!isCollapsed && (
          <h2 className="text-sm font-semibold text-gray-300 truncate flex items-center gap-2">
            <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
            Hub Navigation
          </h2>
        )}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="h-9 w-9 text-gray-400 hover:text-white hover:bg-gray-800/50 flex-shrink-0 rounded-lg border border-transparent hover:border-gray-700/50 transition-all duration-200 shadow-sm"
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Navigation - Improved scrolling behavior */}
      <div className="flex-1 overflow-y-auto p-3 space-y-1 hub-sidebar-scrollbar">
        {/* Main Section */}
        {mainItems.map(item => (
          <SidebarNavItem
            key={item.value}
            href={item.href}
            icon={item.icon}
            label={item.label}
            active={pathname === item.href}
            isCollapsed={isCollapsed}
            color={item.color}
          />
        ))}

        {/* Management Section */}
        {managementItems.length > 0 && (
          <SidebarSection
            title="Management"
            isCollapsed={isCollapsed}
            defaultOpen={true}
          >
            {managementItems.map(item => (
              <SidebarNavItem
                key={item.value}
                href={item.href}
                icon={item.icon}
                label={item.label}
                active={pathname === item.href}
                isCollapsed={isCollapsed}
                color={item.color}
              />
            ))}
          </SidebarSection>
        )}

        {/* Moderation Section */}
        {moderationItems.length > 0 && (
          <SidebarSection
            title="Moderation"
            isCollapsed={isCollapsed}
            defaultOpen={true}
          >
            {moderationItems.map(item => (
              <SidebarNavItem
                key={item.value}
                href={item.href}
                icon={item.icon}
                label={item.label}
                active={pathname === item.href}
                isCollapsed={isCollapsed}
                color={item.color}
              />
            ))}
          </SidebarSection>
        )}

        {/* Misc Section */}
        {miscItems.map(item => (
          <SidebarNavItem
            key={item.value}
            href={item.href}
            icon={item.icon}
            label={item.label}
            active={pathname === item.href}
            isCollapsed={isCollapsed}
            color={item.color}
          />
        ))}
      </div>
    </motion.div>
  );
}
