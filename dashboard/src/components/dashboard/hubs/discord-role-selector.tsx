"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";

interface Role {
  id: string;
  name: string;
  color: number;
  position: number;
}

interface DiscordRoleSelectorProps {
  hubId: string;
  serverId?: string;
  value: string;
  onChange: (value: string) => void;
  label: string;
  placeholder: string;
  description?: string;
}

export function DiscordRoleSelector({
  serverId,
  value,
  onChange,
  label,
  placeholder,
  description,
}: DiscordRoleSelectorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>(value);

  // Fetch roles when a serverId is provided
  useEffect(() => {
    if (!serverId) {
      setRoles([]);
      return;
    }

    const fetchRoles = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/discord/servers/${serverId}/roles`);
        if (!response.ok) {
          throw new Error("Failed to fetch roles");
        }
        const data = await response.json();
        setRoles(data.roles || []);
      } catch (error) {
        console.error("Error fetching roles:", error);
        setRoles([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoles();
  }, [serverId]);

  // Update the selected role when value changes
  useEffect(() => {
    setSelectedRole(value);
  }, [value]);

  // Handle role selection
  const handleRoleChange = (roleId: string) => {
    setSelectedRole(roleId);
    onChange(roleId);
  };

  // Convert a Discord role color (integer) to a hex color string
  const getRoleColor = (colorInt: number) => {
    if (colorInt === 0) return "#99AAB5"; // Default Discord gray
    return `#${colorInt.toString(16).padStart(6, '0')}`;
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        {description && <span className="text-xs text-gray-400">{description}</span>}
      </div>

      {/* Role Selection */}
      {serverId ? (
        <Select value={selectedRole} onValueChange={handleRoleChange}>
          <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50 max-h-[300px]">
            {isLoading ? (
              <div className="flex items-center justify-center py-2">
                <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                <span className="text-gray-400">Loading roles...</span>
              </div>
            ) : roles.length === 0 ? (
              <div className="p-2 text-center text-gray-400">
                No roles found in this server
              </div>
            ) : (
              roles.map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  <div className="flex items-center gap-2">
                    <div
                      className="h-3 w-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: getRoleColor(role.color) }}
                    />
                    <span className="truncate">{role.name}</span>
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      ) : (
        <div className="w-full p-3 bg-gray-800/30 border border-gray-700/50 rounded-md text-center text-gray-400 text-sm">
          Select a channel first to choose roles from that server
        </div>
      )}
    </div>
  );
}
