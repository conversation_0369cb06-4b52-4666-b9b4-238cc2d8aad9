"use client";

import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useUploadThing } from '@/lib/uploadthing-utils';
import { cn } from '@/lib/utils';
import {
    AlertCircle,
    Image as ImageIcon,
    Loader2,
    RefreshCw,
    Save,
    Trash2,
    Upload
} from 'lucide-react';
import Image from 'next/image';
import React, { useCallback, useRef, useState } from 'react';

interface HubBannerManagementProps {
  hubId: string;
  currentBannerUrl?: string;
  onBannerUpdate?: (bannerUrl: string | null) => void;
  className?: string;
}

/**
 * Hub Banner Management Interface
 * Modern design with drag-and-drop upload, preview, and banner management capabilities
 */
export function HubBannerManagement({
  hubId,
  currentBannerUrl,
  onBannerUpdate,
  className
}: HubBannerManagementProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [previewUrl, setPreviewUrl] = useState<string | null>(currentBannerUrl || null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // UploadThing hook
  const { startUpload } = useUploadThing("hubBannerUploader", {
    onClientUploadComplete: (res) => {
      const uploadedFile = res?.[0];
      if (uploadedFile) {
        // Clean up old preview URL
        if (previewUrl && previewUrl.startsWith('blob:')) {
          URL.revokeObjectURL(previewUrl);
        }

        setPreviewUrl(uploadedFile.ufsUrl);
        setSelectedFile(null);
        setHasChanges(false);

        if (onBannerUpdate) {
          onBannerUpdate(uploadedFile.ufsUrl);
        }

        toast({
          title: "Banner Updated",
          description: "Your hub banner has been successfully updated.",
        });
      }
      setIsUploading(false);
    },
    onUploadError: (error: Error) => {
      console.error('Error uploading banner:', error);
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload banner",
        variant: "destructive",
      });
      setIsUploading(false);
    },
  });

  // File validation
  const validateFile = (file: File): string | null => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, or WebP)';
    }

    if (file.size > maxSize) {
      return 'File size must be less than 5MB';
    }

    return null;
  };

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    const error = validateFile(file);
    if (error) {
      toast({
        title: "Invalid File",
        description: error,
        variant: "destructive",
      });
      return;
    }

    setSelectedFile(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setHasChanges(true);
  }, [toast]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Upload banner using UploadThing
  const handleUploadBanner = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    try {
      await startUpload([selectedFile], { hubId });
    } catch (error) {
      console.error('Error starting upload:', error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to start upload",
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  // Remove banner
  const handleRemoveBanner = async () => {
    setIsUploading(true);
    try {
      const response = await fetch('/api/hubs/banner/remove', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ hubId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to remove banner');
      }

      // Clean up preview URL
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }

      setPreviewUrl(null);
      setSelectedFile(null);
      setHasChanges(false);

      if (onBannerUpdate) {
        onBannerUpdate(null);
      }

      toast({
        title: "Banner Removed",
        description: "Your hub banner has been removed.",
      });
    } catch (error) {
      console.error('Error removing banner:', error);
      toast({
        title: "Remove Failed",
        description: error instanceof Error ? error.message : "Failed to remove banner",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Reset changes
  const handleResetChanges = () => {
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(currentBannerUrl || null);
    setSelectedFile(null);
    setHasChanges(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Compact Header */}
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
          <ImageIcon className="w-4 h-4 text-white" />
        </div>
        <div>
          <h3 className="text-base font-semibold text-white">Hub Banner</h3>
          <p className="text-xs text-gray-400">1200x300px recommended</p>
        </div>
      </div>

      {/* Compact Banner Display */}
      <div className="p-4 bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-lg">
        {previewUrl ? (
          <div className="space-y-3">
            <div className="relative h-20 rounded-lg overflow-hidden bg-gray-700/30">
              <Image
                src={previewUrl}
                alt="Hub banner"
                fill
                className="object-cover"
              />
              {hasChanges && (
                <div className="absolute top-1 right-1">
                  <div className="w-3 h-3 bg-blue-600 rounded-full border-2 border-gray-800"></div>
                </div>
              )}
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-white font-medium">Custom Banner</p>
                <p className="text-xs text-gray-400">Click remove to change</p>
              </div>
              <div className="flex items-center gap-2">
                {hasChanges && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleResetChanges}
                    className="text-gray-400 hover:text-white h-8 px-2"
                  >
                    <RefreshCw className="w-3 h-3" />
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRemoveBanner}
                  disabled={isUploading}
                  className="border-red-600/50 text-red-400 hover:bg-red-600/10 hover:border-red-600 h-8 px-3"
                >
                  <Trash2 className="w-3 h-3 mr-1" />
                  Remove
                </Button>
                {hasChanges && (
                  <Button
                    onClick={handleUploadBanner}
                    disabled={!selectedFile || isUploading}
                    size="sm"
                    className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 h-8 px-3"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-3 h-3 mr-1" />
                        Save
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-4">
            <div className="w-16 h-12 rounded-lg border-2 border-gray-600/50 bg-gray-700/30 flex items-center justify-center">
              <ImageIcon className="w-6 h-6 text-gray-500" />
            </div>
            <div className="flex-1">
              <p className="text-sm text-white font-medium">No Banner</p>
              <p className="text-xs text-gray-400">Upload a banner image</p>
            </div>
          </div>
        )}
      </div>

      {/* Upload Area - Only show when no banner exists */}
      {!previewUrl && (
        <div className="p-4 bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-lg">
          <div
            className={cn(
              "relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300",
              isDragging
                ? "border-blue-500 bg-blue-500/10"
                : "border-gray-600 hover:border-gray-500 hover:bg-gray-700/20"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />

            <div className="space-y-3">
              <div className="w-12 h-12 mx-auto rounded-full bg-gray-700/50 flex items-center justify-center">
                <Upload className="w-6 h-6 text-gray-400" />
              </div>

              <div>
                <p className="text-sm font-medium text-white mb-1">
                  {isDragging ? "Drop your image here" : "Upload Banner"}
                </p>
                <p className="text-xs text-gray-400 mb-3">
                  Drag and drop or click to browse
                </p>

                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white h-8 px-3"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Choose File
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Compact Guidelines - Only show when upload area is visible */}
      {!previewUrl && (
        <div className="p-3 bg-gray-800/20 rounded-lg border border-gray-700/30">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-gray-400">
              <p className="text-gray-300 font-medium mb-1">Guidelines:</p>
              <p>1200x300px, max 4MB, JPEG/PNG/WebP formats</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
