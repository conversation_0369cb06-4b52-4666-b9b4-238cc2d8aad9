"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function AnimatedHubsSkeleton() {
  return (
    <div className="space-y-8">
      {/* Hero Section Skeleton */}
      <div className="relative h-[25vh] md:h-[30vh] mb-8 overflow-hidden rounded-xl bg-gradient-to-br from-indigo-900/30 via-blue-900/20 to-purple-900/30">
        <div className="h-full flex flex-col items-center justify-center px-6">
          <Skeleton className="h-10 w-64 mb-4" />
          <Skeleton className="h-6 w-80 mb-6" />
          <div className="flex gap-6">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
          <Skeleton className="h-10 w-40 mt-6" />
        </div>
      </div>

      {/* Tabs Skeleton */}
      <div className="flex border-b border-gray-800 mb-6">
        <Skeleton className="h-10 w-24 mr-4" />
        <Skeleton className="h-10 w-24 mr-4" />
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Hub Cards Skeleton */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card
            key={i}
            className="border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30"
          >
            <CardHeader className="pb-2">
              <div className="flex items-center gap-3">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div>
                  <Skeleton className="h-5 w-32 mb-1" />
                  <Skeleton className="h-4 w-48" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="py-4">
              <div className="space-y-3">
                {Array.from({ length: 4 }).map((_, j) => (
                  <div key={j} className="flex justify-between items-center">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="pt-0 border-t border-gray-800/50">
              <Skeleton className="h-9 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
