'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Ban,
  Bell,
  Edit,
  FileText,
  Home,
  MessageSquare,
  Settings,
  Shield,
  Users,
  X,
} from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface HubMobileSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
}

interface MobileNavItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  active: boolean;
  color?: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange';
  onClick: () => void;
}

function MobileNavItem({
  href,
  icon: Icon,
  label,
  active,
  color = 'indigo',
  onClick,
}: MobileNavItemProps) {
  const colorMap = {
    indigo: 'text-indigo-300 bg-gradient-to-r from-indigo-500/25 to-indigo-600/20 border-indigo-400/40',
    blue: 'text-blue-300 bg-gradient-to-r from-blue-500/25 to-blue-600/20 border-blue-400/40',
    green: 'text-green-300 bg-gradient-to-r from-green-500/25 to-green-600/20 border-green-400/40',
    purple: 'text-purple-300 bg-gradient-to-r from-purple-500/25 to-purple-600/20 border-purple-400/40',
    red: 'text-red-300 bg-gradient-to-r from-red-500/25 to-red-600/20 border-red-400/40',
    pink: 'text-pink-300 bg-gradient-to-r from-pink-500/25 to-pink-600/20 border-pink-400/40',
    orange: 'text-orange-300 bg-gradient-to-r from-orange-500/25 to-orange-600/20 border-orange-400/40',
  };

  const iconColorMap = {
    indigo: 'text-indigo-400',
    blue: 'text-blue-400',
    green: 'text-green-400',
    purple: 'text-purple-400',
    red: 'text-red-400',
    pink: 'text-pink-400',
    orange: 'text-orange-400',
  };

  return (
    <Link href={href} onClick={onClick}>
      <motion.div
        className={cn(
          'flex items-center gap-3 px-4 py-3.5 rounded-xl transition-all duration-300 relative overflow-hidden group',
          'hover:bg-gray-800/60 hover:text-white hover:shadow-lg hover:scale-[1.02]',
          active
            ? `${colorMap[color]} border shadow-lg`
            : 'text-gray-400 border border-transparent hover:border-gray-700/50'
        )}
        whileHover={{ x: 4 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Background glow effect for active items */}
        {active && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-50" />
        )}

        <div className={cn(
          'p-1.5 rounded-lg transition-colors duration-200',
          active ? 'bg-white/10' : 'group-hover:bg-gray-700/50'
        )}>
          <Icon className={cn(
            'h-4 w-4 flex-shrink-0 transition-colors duration-200',
            active ? iconColorMap[color] : 'group-hover:text-white'
          )} />
        </div>
        <span className="text-sm font-medium relative z-10">{label}</span>

        {/* Active indicator */}
        {active && (
          <motion.div
            className="absolute right-3 w-2 h-2 bg-current rounded-full"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.1 }}
          />
        )}
      </motion.div>
    </Link>
  );
}

export function HubMobileSidebar({
  isOpen,
  onClose,
  hubId,
  canModerate = false,
  canEdit = false,
}: HubMobileSidebarProps) {
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();

  // Only mount the portal on the client side
  useEffect(() => {
    setMounted(true);

    // Prevent body scrolling when sidebar is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!mounted) return null;

  // Define all navigation items with their permissions
  const navigationItems = [
    {
      value: 'overview',
      label: 'Overview',
      color: 'indigo' as const,
      icon: MessageSquare,
      href: `/dashboard/hubs/${hubId}`,
      section: 'Main',
      show: true,
    },
    {
      value: 'edit',
      label: 'Edit Hub',
      color: 'blue' as const,
      icon: Edit,
      href: `/dashboard/hubs/${hubId}/edit`,
      section: 'Management',
      show: canEdit,
    },
    {
      value: 'members',
      label: 'Members',
      color: 'blue' as const,
      icon: Users,
      href: `/dashboard/hubs/${hubId}/members`,
      section: 'Management',
      show: canModerate,
    },
    {
      value: 'connections',
      label: 'Connections',
      color: 'green' as const,
      icon: Home,
      href: `/dashboard/hubs/${hubId}/connections`,
      section: 'Management',
      show: canModerate,
    },
    {
      value: 'logging',
      label: 'Logging',
      color: 'purple' as const,
      icon: FileText,
      href: `/dashboard/hubs/${hubId}/logging`,
      section: 'Management',
      show: canEdit,
    },
    {
      value: 'moderation',
      label: 'Moderation',
      color: 'purple' as const,
      icon: Shield,
      href: `/dashboard/hubs/${hubId}/moderation`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'reports',
      label: 'Reports',
      color: 'purple' as const,
      icon: FileText,
      href: `/dashboard/hubs/${hubId}/reports`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'blacklists',
      label: 'Blacklists',
      color: 'red' as const,
      icon: Ban,
      href: `/dashboard/hubs/${hubId}/blacklists`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'infractions',
      label: 'Infractions',
      color: 'orange' as const,
      icon: AlertTriangle,
      href: `/dashboard/hubs/${hubId}/infractions`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'appeals',
      label: 'Appeals',
      color: 'pink' as const,
      icon: Bell,
      href: `/dashboard/hubs/${hubId}/appeals`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'anti-swear',
      label: 'Anti-Swear',
      color: 'red' as const,
      icon: AlertTriangle,
      href: `/dashboard/hubs/${hubId}/anti-swear`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'settings',
      label: 'Settings',
      color: 'orange' as const,
      icon: Settings,
      href: `/dashboard/hubs/${hubId}/settings`,
      section: 'Settings',
      show: true,
    },
  ];

  // Filter items based on permissions and group by section
  const visibleItems = navigationItems.filter(item => item.show);
  const sections = ['Main', 'Management', 'Moderation', 'Settings'];
  const groupedItems = sections.reduce((acc, section) => {
    const items = visibleItems.filter(item => item.section === section);
    if (items.length > 0) {
      acc[section] = items;
    }
    return acc;
  }, {} as Record<string, typeof visibleItems>);

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Enhanced Backdrop with better visual feedback */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-md z-[9998]"
            onClick={onClose}
          />

          {/* Enhanced Sidebar with improved animations */}
          <motion.div
            initial={{ x: '-100%', scale: 0.95 }}
            animate={{ x: 0, scale: 1 }}
            exit={{ x: '-100%', scale: 0.95 }}
            transition={{
              duration: 0.4,
              ease: [0.23, 1, 0.32, 1], // Custom easing for smooth feel
              scale: { duration: 0.3 }
            }}
            className="fixed top-0 left-0 bottom-0 w-[340px] max-w-[90vw] flex flex-col bg-gradient-to-br from-gray-900 via-gray-900 to-gray-950 border-r border-indigo-500/30 shadow-2xl overflow-hidden z-[9999]"
          >
            {/* Enhanced Header with gradient and better visual hierarchy */}
            <motion.div
              className="flex items-center justify-between p-5 border-b border-indigo-500/30 bg-gradient-to-r from-indigo-600/20 to-purple-600/20"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" />
                <h2 className="text-lg font-semibold text-white">Hub Navigation</h2>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-9 w-9 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg transition-all duration-200"
              >
                <X className="h-4 w-4" />
              </Button>
            </motion.div>

            {/* Enhanced Navigation with staggered animations */}
            <div className="flex-1 overflow-y-auto p-5 space-y-6 hub-sidebar-scrollbar">
              {Object.entries(groupedItems).map(([section, items], sectionIndex) => (
                <motion.div
                  key={section}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 + sectionIndex * 0.1, duration: 0.3 }}
                >
                  <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-4 flex items-center gap-2">
                    <div className="w-1 h-1 bg-gray-500 rounded-full" />
                    {section}
                  </h3>
                  <div className="space-y-2">
                    {items.map((item, itemIndex) => (
                      <motion.div
                        key={item.value}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{
                          delay: 0.3 + sectionIndex * 0.1 + itemIndex * 0.05,
                          duration: 0.2
                        }}
                      >
                        <MobileNavItem
                          href={item.href}
                          icon={item.icon}
                          label={item.label}
                          active={pathname === item.href}
                          color={item.color}
                          onClick={onClose}
                        />
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}

              {/* Footer with helpful tip */}
              <motion.div
                className="pt-4 border-t border-gray-800/50"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.3 }}
              >
                <p className="text-xs text-gray-500 text-center flex items-center justify-center gap-1">
                  <span>💡</span> Swipe left or tap outside to close
                </p>
              </motion.div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>,
    document.body
  );
}
