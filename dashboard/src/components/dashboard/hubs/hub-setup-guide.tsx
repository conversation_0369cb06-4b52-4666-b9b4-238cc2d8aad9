"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import {
  Check,
  ChevronRight,
  Copy,
  Home,
  LinkIcon,
  Settings,
  Users
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface HubSetupGuideProps {
  hubId: string;
  hubName: string;
  isPrivate: boolean;
}

export function HubSetupGuide({
  hubId,
  hubName,
  isPrivate,
}: HubSetupGuideProps) {
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const steps = [
    {
      id: 1,
      title: "Customize Hub Settings",
      description: "Set up rules, welcome message, and other settings",
      icon: <Settings className="h-5 w-5" />,
      href: `/dashboard/hubs/${hubId}/edit`,
      buttonText: "Edit Settings",
    },
    {
      id: 2,
      title: "Connect a Server",
      description: "Link your Discord server to this hub",
      icon: <Home className="h-5 w-5" />,
      href: `/dashboard/servers?hubId=${hubId}`,
      buttonText: "Connect Server",
    },
    {
      id: 3,
      title: isPrivate ? "Invite Members" : "Share Your Hub",
      description: isPrivate
        ? "Create invite links for your private hub"
        : "Share your hub with the community",
      icon: <Users className="h-5 w-5" />,
      href: isPrivate ? `/dashboard/hubs/${hubId}/invites` : `/hubs/${hubId}`,
      buttonText: isPrivate ? "Manage Invites" : "View Hub Page",
    },
  ];

  const toggleComplete = (stepId: number) => {
    setCompletedSteps((prev) =>
      prev.includes(stepId)
        ? prev.filter((id) => id !== stepId)
        : [...prev, stepId],
    );
  };

  const copyInviteCommand = async () => {
    const command = `/hub join hub:${hubName}`;
    try {
      await navigator.clipboard.writeText(command);
      setCopied(true);
      toast({
        title: "Command copied!",
        description: "Paste this command in your Discord server.",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast({
        title: "Failed to copy",
        description: "Please copy the command manually.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="border border-indigo-500/20 bg-gradient-to-b from-indigo-950/20 to-gray-950/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <div className="h-6 w-6 rounded-full bg-indigo-500/20 flex items-center justify-center">
            <LinkIcon className="h-3.5 w-3.5 text-indigo-400" />
          </div>
          Hub Setup Guide
        </CardTitle>
        <CardDescription>
          Follow these steps to get your hub up and running
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={cn(
                "flex items-start gap-4 p-4 rounded-lg transition-all",
                completedSteps.includes(step.id)
                  ? "bg-indigo-500/10 border border-indigo-500/20"
                  : "bg-gray-800/30 border border-gray-800/50 hover:bg-gray-800/50",
              )}
            >
              <div className="flex-shrink-0">
                <div
                  className={cn(
                    "h-8 w-8 rounded-full flex items-center justify-center",
                    completedSteps.includes(step.id)
                      ? "bg-indigo-500/20 text-indigo-400"
                      : "bg-gray-800 text-gray-400",
                  )}
                >
                  {completedSteps.includes(step.id) ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3
                    className={cn(
                      "font-medium",
                      completedSteps.includes(step.id)
                        ? "text-indigo-300"
                        : "text-gray-200",
                    )}
                  >
                    {step.title}
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => toggleComplete(step.id)}
                    className="h-6 px-2 text-xs text-gray-400 hover:text-white"
                  >
                    {completedSteps.includes(step.id)
                      ? "Mark incomplete"
                      : "Mark complete"}
                  </Button>
                </div>
                <p className="text-sm text-gray-400 mt-1">{step.description}</p>
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="mt-3 bg-gray-800/50 border-gray-700/50 hover:bg-gray-700/50"
                >
                  <Link href={step.href}>
                    {step.buttonText}
                    <ChevronRight className="h-3 w-3 ml-1" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>

        {isPrivate && (
          <div className="mt-6 bg-gray-800/30 p-4 rounded-lg border border-gray-800/50">
            <h3 className="font-medium text-gray-200">Quick Invite Command</h3>
            <p className="text-sm text-gray-400 mt-1">
              Users can join your hub with this Discord command:
            </p>
            <div className="flex mt-2">
              <div className="flex-1 bg-gray-900 rounded-l-md border border-gray-800 px-3 py-1.5 font-mono text-sm">
                /hub join hub:{hubName}
              </div>
              <Button
                onClick={copyInviteCommand}
                className="rounded-l-none bg-indigo-600 hover:bg-indigo-700"
                size="sm"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between border-t border-gray-800/50 pt-4">
        <div className="text-sm text-gray-400">
          {completedSteps.length} of {steps.length} steps completed
        </div>
        <div className="w-24 h-2 bg-gray-800/50 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-indigo-500 to-purple-500"
            style={{
              width: `${(completedSteps.length / steps.length) * 100}%`,
            }}
          />
        </div>
      </CardFooter>
    </Card>
  );
}
