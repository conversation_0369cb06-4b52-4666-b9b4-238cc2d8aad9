"use client";

import { Home, MessageSquare, Users } from "lucide-react";
import { ModernHero } from "../shared/modern-hero";

interface AnimatedHubsHeroProps {
  totalHubs: number;
  totalConnections: number;
  totalServers: number;
}

export function AnimatedHubsHero({
  totalHubs,
  totalConnections,
  totalServers,
}: AnimatedHubsHeroProps) {
  return (
    <ModernHero
      title="Your Community Hubs"
      subtitle="Manage, moderate, and grow your cross-server communities"
      stats={[
        {
          icon: <MessageSquare className="h-5 w-5 text-indigo-400" />,
          value: totalHubs,
          label: "Hubs",
          color: "bg-none",
        },
        {
          icon: <Home className="h-5 w-5 text-blue-400" />,
          value: totalConnections,
          label: "Connections",
          color: "bg-none",
        },
        {
          icon: <Users className="h-5 w-5 text-purple-400" />,
          value: totalServers,
          label: "Servers",
          color: "bg-none",
        },
      ]}
      gradientColors={{
        from: "from-indigo-900/30",
        via: "via-blue-900/20",
        to: "to-purple-900/30",
      }}
      particleColors={[
        "bg-indigo-500/20",
        "bg-blue-500/20",
        "bg-purple-500/20",
      ]}
    />
  );
}
