"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuFooter,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { useNotifications } from "@/hooks/use-notifications";
import { formatDistanceToNow } from "date-fns";
import { ArrowRight, Bell, Check } from "lucide-react";
import Link from "next/link";
import { NotificationBadge } from "./notification-badge";
import Image from "next/image";

export function NotificationDropdown() {
  const {
    notifications,
    unreadCount,
    isLoading,
    isOpen,
    toggleNotifications,
    markAllAsRead,
  } = useNotifications();

  // Sort notifications to show unread first
  const sortedNotifications = [...notifications].sort((a, b) => {
    // First sort by read status (unread first)
    if (a.isUnread && !b.isUnread) return -1;
    if (!a.isUnread && b.isUnread) return 1;
    // Then sort by date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  // Limit to 5 most recent notifications for the dropdown
  const displayNotifications = sortedNotifications.slice(0, 5);

  // Check if there are any notifications at all
  const hasNotifications = notifications.length > 0;

  return (
    <DropdownMenu open={isOpen} onOpenChange={toggleNotifications}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="text-gray-400 hover:text-white rounded-full h-9 w-9 relative"
        >
          <Bell className="h-5 w-5" />
          <NotificationBadge count={unreadCount} />
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-80 max-h-[70vh] overflow-y-auto bg-gray-900 border-gray-800 text-gray-100"
      >
        <DropdownMenuLabel className="flex items-center justify-between">
          <span className="text-lg font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
            Notifications
          </span>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-xs text-gray-400 hover:text-white"
              onClick={() => markAllAsRead()}
            >
              <Check className="h-3.5 w-3.5 mr-1" />
              Mark all as read
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-800" />
        <DropdownMenuGroup>
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 3 }).map((_, i) => (
              <DropdownMenuItem
                key={`skeleton-${i}`}
                className="flex flex-col items-start gap-1 py-3 px-4 focus:bg-gray-800/50 cursor-default"
              >
                <Skeleton className="h-5 w-3/4 bg-gray-800" />
                <Skeleton className="h-4 w-full bg-gray-800" />
                <Skeleton className="h-4 w-1/2 bg-gray-800" />
              </DropdownMenuItem>
            ))
          ) : !hasNotifications ? (
            <div className="py-8 text-center text-gray-400">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No notifications</p>
            </div>
          ) : (
            displayNotifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={`flex flex-col items-start gap-1 py-3 px-4 focus:bg-gray-800/50 cursor-default relative ${
                  notification.isUnread
                    ? "before:absolute before:left-0 before:top-0 before:h-full before:w-1 before:bg-gradient-to-b before:from-indigo-500 before:to-purple-500"
                    : ""
                }`}
              >
                <div className="flex items-center justify-between w-full">
                  <h4 className="font-medium text-white">
                    {notification.title}
                  </h4>
                  <span className="text-xs text-gray-400">
                    {formatDistanceToNow(new Date(notification.createdAt), {
                      addSuffix: true,
                    })}
                  </span>
                </div>
                <p className="text-sm text-gray-300 line-clamp-2">
                  {notification.content}
                </p>
                {notification.thumbnailUrl && (
                  <div className="mt-1 w-full">
                    <Image
                      src={notification.thumbnailUrl}
                      alt=""
                      width={320}
                      height={160}
                      className="h-20 w-full object-cover rounded-md"
                    />
                  </div>
                )}
                {notification.isUnread && (
                  <div className="absolute top-3 right-3">
                    <div className="h-2 w-2 rounded-full bg-indigo-500" />
                  </div>
                )}
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-800" />
        <DropdownMenuFooter className="p-2">
          <Button
            asChild
            variant="ghost"
            size="sm"
            className="w-full justify-between bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white"
          >
            <Link href="/dashboard/announcements">
              View all notifications
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </Button>
        </DropdownMenuFooter>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
