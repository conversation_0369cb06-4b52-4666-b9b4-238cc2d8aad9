import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Github, UsersRound, Linkedin, Globe } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { DiscordIcon } from './DiscordIcon';

interface Contributor {
  name: string;
  role: string;
  roleType: 'developer' | 'staff' | 'translator';
  avatar?: string;
  github?: string;
  discord?: string;
  linkedin?: string;
  website?: string;
}

const getRoleBadgeStyles = (roleType: Contributor['roleType']) => {
  switch (roleType) {
    case 'developer':
      return 'bg-purple-600 dark:bg-purple-700 text-white';
    case 'staff':
      return 'bg-emerald-600 dark:bg-emerald-700 text-white';
    case 'translator':
      return 'bg-amber-600 dark:bg-amber-700 text-white';
    default:
      return '';
  }
};

const contributors: Contributor[] = [
  // Developers
  {
    name: 'devoid.',
    role: 'Lead Developer',
    roleType: 'developer',
    avatar:
      'https://cdn.discordapp.com/avatars/701727675311587358/789fb02094d8963727a2cf6bf78c99fe.webp?size=4096',
    github: 'dev-737',
    discord: 'cgYgC6YZyX',
  },
  // Staff
  {
    name: 'hecash',
    role: 'Community Manager',
    roleType: 'staff',
    avatar:
      'https://cdn.discordapp.com/avatars/1160735837940617336/d21ca7a5f0734334387cd53e8dbd8a38.webp?size=4096',
    discord: '37cjURADY3',
    website: 'https://jetskiiix.straw.page',
  },
  {
    name: 'orange_mitro',
    role: 'Moderator',
    roleType: 'staff',
    avatar:
      'https://cdn.discordapp.com/avatars/994411851557392434/3a60b2ba79d2e71617334f4eef98ec8c.webp?size=4096',
  },
  {
    name: 'loveitsgood',
    role: 'Bot Moderator',
    roleType: 'staff',
    avatar:
      'https://cdn.discordapp.com/avatars/853178500193583104/9ed1f465b20a1b95c2d960f7ae35ed85.webp?size=4096',
  },
  // Translators
  {
    name: 'spacelemoon',
    role: 'Russian Translator',
    roleType: 'translator',
    avatar:
      'https://cdn.discordapp.com/avatars/845357241132384286/954653d2f58cdf003709515df5820a0c.webp?size=4096',
  },
  {
    name: 'dannybarbosabr',
    role: 'Portuguese Translator', // Corrected spelling
    roleType: 'translator',
    avatar:
      'https://cdn.discordapp.com/avatars/1067849662347878401/ed4f535c935e9c7d946e9ee8bb57ba06.webp?size=4096',
    discord: 'b4dyWb3wGX',
    github: 'DannyBarbosaBR',
    linkedin: 'daniel-barbosa-de-lima-4181b4266',
  },
  {
    name: 'Chenxian.201277050224',
    role: 'Chinese Translator',
    roleType: 'translator',
  },
  {
    name: 'wakabearhasaname',
    role: 'Hindi Translator',
    avatar:
      'https://cdn.discordapp.com/avatars/1065564110844071996/2add66078f6c8a2908e46f87113ddb3f.webp?size=4096',
    roleType: 'translator',
  },
  {
    name: 'lautydev',
    role: 'Spanish Translator',
    roleType: 'translator',
    avatar: 'https://cdn.discordapp.com/avatars/656842811496333322/de43b1b4de1e91581ee9db3ad9852694.webp?size=4096',
    github: 'LautyDev',

  },
  {
    name: 'tnfangel',
    role: 'Spanish Translator',
    roleType: 'translator',
    avatar: 'https://cdn.discordapp.com/avatars/******************/e53fd8d7fad3914bbff129f04cbd058d.webp?size=4096',
    github: 'tnfAngel',
    website: 'https://www.tnfangel.com',
  }
];

export const CreditsSection = () => {
  const sortedContributors = [...contributors].sort((a, b) => {
    if (a.roleType === b.roleType) return 0;
    if (a.roleType === 'developer') return -1;
    if (b.roleType === 'developer') return 1;
    if (a.roleType === 'staff') return -1;
    if (b.roleType === 'staff') return 1;
    return 0;
  });

  return (
    <section
      className="relative overflow-hidden py-32 bg-gradient-to-b from-gray-900 to-gray-950"
      id="team"
    >
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-800/20 via-transparent to-transparent" />
      </div>
      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-20 relative z-10">
          <div className="inline-block mb-6">
            <div className="flex items-center justify-center bg-gray-800/50 text-white px-6 py-3 rounded-full border border-gray-700/50 backdrop-blur-lg">
              <UsersRound className="w-5 h-5 mr-3 text-primary" aria-hidden="true" />
              <span className="font-medium tracking-wide text-base">InterChat Team</span>
            </div>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            The Talented Team Behind
            <span className="block mt-3 text-primary">InterChat&apos;s Success</span>
          </h2>
          <p className="max-w-3xl mx-auto text-lg text-gray-300">
            Meet the dedicated individuals who contribute their skills and passion to make InterChat
            a reality. From developers to community managers and translators, our team works
            tirelessly to provide you with the best possible cross-server Discord experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 relative z-10">
          {sortedContributors.map((contributor) => (
            <div
              key={contributor.name}
              className="group relative"
              itemScope // Added for Schema
              itemType="https://schema.org/Person" // Added for Schema
            >
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/10 to-primary-alt/10 blur-xl opacity-0 group-hover:opacity-50 transition-opacity duration-700" />

              <div className="bg-gray-800/50 backdrop-blur-xl rounded-2xl p-6 border border-gray-700/50 group-hover:border-gray-600/70 hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 relative">
                <div className="flex items-center text-start">
                  {contributor.avatar ? (
                    <Image
                      src={contributor.avatar}
                      alt={`${contributor.name}'s avatar`} // Improved alt text
                      width={60}
                      height={60}
                      className="rounded-full border-2 border-gray-700/50"
                      itemProp="image" // Added for Schema
                    />
                  ) : (
                    <div className="w-15 h-15 rounded-full flex items-center justify-center bg-gray-700/50 text-primary border-2 border-gray-600/50">
                      <span className="text-lg font-semibold">{contributor.name[0]}</span>
                    </div>
                  )}

                  <div className="ml-4 flex-1">
                    <h4
                      className="font-medium text-white group-hover:text-primary transition-colors duration-300"
                      itemProp="name"
                    >
                      {contributor.name}
                    </h4>
                    <Badge
                      className={cn('mt-1', getRoleBadgeStyles(contributor.roleType))}
                      itemProp="jobTitle" // Added for Schema
                    >
                      {contributor.role}
                    </Badge>
                  </div>

                  <div className="flex space-x-2">
                    {contributor.github && (
                      <Link
                        href={`https://github.com/${contributor.github}?utm_source=interchat.tech&utm_medium=referral`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-full hover:bg-gray-700/50 transition-colors"
                        // Added aria-label
                        aria-label={`View ${contributor.name}'s GitHub profile`}
                      >
                        <Github
                          className="h-5 w-5 text-gray-300 hover:text-white"
                          aria-hidden="true"
                        />
                      </Link>
                    )}
                    {contributor.linkedin && (
                      <Link
                        href={`https://linkedin.com/${contributor.linkedin}?utm_source=interchat.tech&utm_medium=referral`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-full hover:bg-gray-700/50 transition-colors"
                        aria-label={`View ${contributor.name}'s LinkedIn profile`}
                      >
                        <Linkedin
                          className="h-5 w-5 text-gray-300 hover:text-white"
                          aria-hidden="true"
                        />
                      </Link>
                    )}
                    {contributor.website && (
                      <Link
                        href={`${contributor.website}?utm_source=interchat.tech`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-full hover:bg-gray-700/50 transition-colors"
                        aria-label={`Visit ${contributor.name}'s website`}
                      >
                        <Globe
                          className="h-5 w-5 text-gray-300 hover:text-white"
                          aria-hidden="true"
                        />
                      </Link>
                    )}
                    {contributor.discord && (
                      <Link
                        href={`https://discord.com/invite/${contributor.discord}?utm_source=interchat.tech&utm_medium=referral`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-full hover:bg-gray-700/50 transition-colors"
                        // Added aria-label
                        aria-label={`Contact ${contributor.name} on Discord`}
                      >
                        <DiscordIcon
                          className="h-5 w-5 fill-gray-300 hover:fill-white"
                          aria-hidden="true"
                        />
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
