import { Button } from "@/components/ui/button";
import { <PERSON>R<PERSON>, CheckCircle2, Zap } from "lucide-react";
import { motion } from "motion/react";

const steps = [
  {
    number: "01",
    title: "Add InterChat to Discord",
    description:
      "Invite InterChat to your server with a single click. No complex setup required.",
    gradient: "from-emerald-500/20 to-teal-500/30",
  },
  {
    number: "02",
    title: "Pick Your Hub",
    description:
      "Use /setup to select which channel to connect and customize your community's experience.",
    gradient: "from-indigo-500/20 to-violet-500/30",
  },
  {
    number: "03",
    title: "Start Global Conversations",
    description:
      "That's it! Your community can now engage with like-minded servers worldwide.",
    gradient: "from-rose-500/20 to-pink-500/30",
  },
];

export const HowItWorks = () => {
  return (
    <section className="relative overflow-hidden py-32">
      {/* gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-900/20 to-purple-900/30" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-purple-900/30 via-transparent to-transparent" />

      {/* Animated gradient blobs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          initial={{ opacity: 0.5, x: -100 }}
          animate={{
            opacity: [0.5, 0.7, 0.5],
            x: [-20, 20, -20],
            y: [-20, 10, -20],
            scale: [0.9, 1.1, 0.9],
          }}
          transition={{
            repeat: Infinity,
            duration: 15,
            ease: "easeInOut",
          }}
          className="absolute top-1/4 left-1/5 w-96 h-96 bg-primary/20 rounded-full blur-3xl"
        />
        <motion.div
          initial={{ opacity: 0.5, x: 100 }}
          animate={{
            opacity: [0.5, 0.8, 0.5],
            x: [20, -20, 20],
            y: [20, -10, 20],
            scale: [1, 1.2, 1],
          }}
          transition={{
            repeat: Infinity,
            duration: 18,
            ease: "easeInOut",
            delay: 1,
          }}
          className="absolute bottom-1/4 right-1/6 w-96 h-96 bg-primary-alt/25 rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-20 relative z-10">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="inline-block mb-6"
          >
            <div className="flex items-center justify-center bg-white/10 text-primary px-6 py-3 rounded-full border border-white/15 backdrop-blur-lg">
              <Zap className="w-5 h-5 mr-3" />
              <span className="font-medium tracking-wide text-base">
                Quick Setup Process
              </span>
            </div>
          </motion.div>

          <motion.h2
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6"
          >
            Get Started in
            <span className="block mt-3 text-transparent bg-clip-text bg-gradient-to-r from-primary-alt via-primary to-primary-alt animate-gradient-x">
              Three Simple Steps
            </span>
          </motion.h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8 relative z-10">
          {steps.map((step, index) => (
            <motion.div
              key={step.number}
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ duration: 0.7, delay: index * 0.1 }}
              className="group relative"
            >
              <div
                className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${step.gradient} blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-700`}
              />

              <div className="flex flex-col bg-white/5 backdrop-blur-xl rounded-2xl p-8 border border-white/10 group-hover:border-primary/40 transition-all duration-300 relative h-full">
                <div className="mb-6">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-primary-alt to-primary flex items-center justify-center text-white font-bold text-lg mb-4">
                    {step.number}
                  </div>
                  <h3 className="text-2xl font-bold group-hover:text-primary-alt transition-colors duration-300">
                    {step.title}
                  </h3>
                </div>

                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  {step.description}
                </p>

                <div className="mt-auto">
                  <div className="flex items-center text-primary">
                    <CheckCircle2 className="w-5 h-5 mr-2" />
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mt-20 text-center relative z-10"
        >
          <a href="/invite" target="_blank" rel="noreferrer">
            <Button className="cursor-pointer bg-gradient-to-r from-primary-alt to-primary hover:from-primary hover:to-primary-alt text-white font-medium py-6 px-10 rounded-full text-lg shadow-xl shadow-primary/20 hover:shadow-primary/40 transition-all duration-300 transform hover:scale-105">
              Add InterChat to Discord
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </a>
        </motion.div>
      </div>
    </section>
  );
};
