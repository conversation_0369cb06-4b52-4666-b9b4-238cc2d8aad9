"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { UserNav } from "@/components/user-nav";
import type { User } from "next-auth";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "@/components/ui/navigation-menu";
import { ArrowRight, Menu, ExternalLink } from "lucide-react";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>rigger,
  SheetTitle,
  SheetHeader,
} from "@/components/ui/sheet";

const links = [
  {
    text: "Docs",
    url: "/docs",
  },
  {
    text: "Hubs",
    url: "/hubs",
  },
  {
    text: "Pricing",
    url: "/pricing",
  },
  // FIXME: add back when blog is ready
  // {
  //   text: "Blog",
  //   url: "/blog",
  // },
  {
    text: "Guidelines",
    url: "/guidelines",
  },
  {
    text: "Vote",
    url: "/vote",
    external: true,
  },
];

export function Navbar({ user }: { user: User | null }) {
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);
  const isDocsOrDashboardPage =
    pathname?.startsWith("/docs") || pathname.startsWith("/dashboard");

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isDocsOrDashboardPage]);

  return (
    <header
      className={`${
        isDocsOrDashboardPage ? "hidden" : ""
      } fixed top-0 z-50 w-full
      ${
        isScrolled
          ? "backdrop-blur-[6px] bg-[#0a0a0c]/30"
          : "bg-transparent pointer-events-auto"
      }
      transition-all duration-300`}
    >
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-4 py-2">
        {/* Logo and Navigation */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center gap-2 mr-6 group">
            <Image
              alt="InterChat"
              src="/interchat.png"
              height={32}
              width={32}
              className="rounded-full transition-transform duration-300 group-hover:scale-105"
            />
            <span className="font-bold text-lg hidden sm:inline text-white group-hover:text-primary transition-colors duration-300">
              InterChat
            </span>
          </Link>

          {/* Desktop Navigation */}
          <NavigationMenu className="hidden lg:flex">
            <NavigationMenuList className="space-x-1">
              {links.map((link) => (
                <NavigationMenuItem key={link.url}>
                  <NavigationMenuLink
                    href={link.url}
                    className={`group inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium
                      text-gray-400 hover:text-white relative
                      transition-colors duration-300 after:absolute after:bottom-0 after:left-1/2 after:h-[2px] after:w-0
                      after:translate-x-[-50%] after:bg-primary after:transition-all after:duration-300
                      hover:after:w-[80%] ${
                        pathname === link.url ? "text-white after:w-[80%]" : ""
                      }`}
                  >
                    {link.text}
                  </NavigationMenuLink>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        {/* Right side items */}
        <div className="flex items-center gap-2">
          {/* GitHub - Visible on large screens */}
          <Button
            variant="ghost"
            size="icon"
            asChild
            className="hidden lg:flex text-gray-400 hover:text-white rounded-full transition-transform duration-300 hover:scale-110 hover:bg-transparent"
          >
            <Link
              href="https://github.com/interchatapp/InterChat"
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="h-5 w-5" />
              <span className="sr-only">GitHub</span>
            </Link>
          </Button>

          {/* Discord Button - Hidden on small/medium screens */}
          <Button
            asChild
            className="bg-gradient-to-r from-primary to-primary-alt text-white font-medium
            hidden lg:flex shadow-md hover:shadow-primary/20 transition-all duration-300 hover:scale-105"
          >
            <Link href="/invite" className="items-center gap-2">
              Invite
              <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
            </Link>
          </Button>

          {/* User Nav or Login */}
          {user ? (
            <UserNav user={user} />
          ) : (
            <Button
              asChild
              variant="ghost"
              className="hidden lg:flex text-gray-400 hover:text-white hover:bg-white/5 backdrop-blur-sm transition-all duration-300"
            >
              <Link href={`/login?callbackUrl=${encodeURIComponent(pathname)}`}>
                Login
              </Link>
            </Button>
          )}

          {/* Mobile/Tablet Menu Button */}
          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden text-gray-400 hover:text-white hover:bg-white/5 backdrop-blur-sm rounded-full transition-all duration-300"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-[280px] sm:w-[320px] p-0 border-l
              border-gray-800/30 bg-[#0a0a0c]/95 backdrop-blur-md"
            >
              <SheetHeader className="p-6 border-b border-gray-800/30">
                <SheetTitle className="flex items-center gap-2 text-white">
                  <Image
                    alt="InterChat"
                    src="/interchat.png"
                    height={24}
                    width={24}
                    className="rounded-full transition-transform duration-300 hover:scale-105"
                  />
                  <span className="font-medium">InterChat Menu</span>
                </SheetTitle>
              </SheetHeader>

              <div className="flex flex-col h-full">
                <nav className="flex flex-col py-4">
                  {links.map((link) => (
                    <Link
                      key={link.url}
                      href={link.url}
                      className={`flex items-center py-3 px-6
                      text-gray-400 hover:text-white relative
                      transition-all duration-300 text-sm font-medium
                      ${
                        pathname === link.url
                          ? "text-white pl-8 bg-white/5"
                          : ""
                      }
                      hover:bg-white/5 hover:pl-8`}
                    >
                      {link.text}
                    </Link>
                  ))}
                </nav>

                <div className="mt-auto p-6 space-y-4 border-t border-gray-800/30">
                  <Button
                    asChild
                    className="w-full bg-gradient-to-r from-primary to-primary-alt text-white font-medium shadow-md hover:shadow-primary/20 transition-all duration-300"
                  >
                    <Link
                      href="/invite"
                      className="flex items-center justify-center gap-2 group"
                    >
                      Invite
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-0.5" />
                    </Link>
                  </Button>

                  {!user && (
                    <Button
                      asChild
                      variant="ghost"
                      className="w-full text-gray-400 hover:text-white hover:bg-white/5 backdrop-blur-sm transition-all duration-300"
                    >
                      <Link
                        href={`/login?callbackUrl=${encodeURIComponent(
                          pathname,
                        )}`}
                      >
                        Login
                      </Link>
                    </Button>
                  )}

                  <div
                    className="flex items-center justify-between pt-4
                    border-t border-gray-800/30"
                  >
                    <Link
                      href="https://github.com/interchatapp/InterChat"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 rounded-md text-gray-400 hover:text-white transition-colors"
                    >
                      <ExternalLink className="h-5 w-5" />
                    </Link>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
