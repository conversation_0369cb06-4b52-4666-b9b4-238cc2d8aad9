"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ChevronDown, 
  ChevronRight, 
  ExternalLink, 
  RefreshCw, 
  Clock,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  HelpCircle
} from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import type { BeginnerFriendlyError, ErrorSolution } from "@/lib/error-messages";

interface BeginnerFriendlyErrorProps {
  error: BeginnerFriendlyError;
  onRetry?: () => void;
  isRetrying?: boolean;
  className?: string;
  compact?: boolean;
}

interface SolutionCardProps {
  solution: ErrorSolution;
  isExpanded: boolean;
  onToggle: () => void;
  onTryFix?: () => void;
}

function SolutionCard({ solution, isExpanded, onToggle, onTryFix }: SolutionCardProps) {
  const difficultyColors = {
    easy: "bg-green-500/10 text-green-400 border-green-500/20",
    medium: "bg-yellow-500/10 text-yellow-400 border-yellow-500/20",
    advanced: "bg-red-500/10 text-red-400 border-red-500/20"
  };

  return (
    <Card className="border-gray-700/50 bg-gray-800/30">
      <Collapsible open={isExpanded} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-700/20 transition-colors pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                )}
                <div>
                  <CardTitle className="text-sm font-medium text-white">
                    {solution.title}
                  </CardTitle>
                  <CardDescription className="text-xs text-gray-400 mt-1">
                    {solution.description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={cn("text-xs", difficultyColors[solution.difficulty])}>
                  {solution.difficulty}
                </Badge>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  {solution.estimatedTime}
                </div>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="bg-gray-900/50 rounded-lg p-3">
                <h4 className="text-sm font-medium text-white mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  Step-by-step solution:
                </h4>
                <ol className="space-y-2">
                  {solution.steps.map((step, index) => (
                    <li key={index} className="flex gap-3 text-sm text-gray-300">
                      <span className="flex-shrink-0 w-5 h-5 bg-primary/20 text-primary rounded-full flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </span>
                      <span>{step}</span>
                    </li>
                  ))}
                </ol>
              </div>

              <div className="flex gap-2">
                {onTryFix && (
                  <Button
                    onClick={onTryFix}
                    size="sm"
                    className="flex-1"
                  >
                    Try This Fix
                  </Button>
                )}
                {solution.helpLink && (
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Link href={solution.helpLink} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Learn More
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}

export function BeginnerFriendlyErrorDisplay({
  error,
  onRetry,
  isRetrying = false,
  className,
  compact = false
}: BeginnerFriendlyErrorProps) {
  const [expandedSolution, setExpandedSolution] = useState<string | null>(
    error.solutions.length > 0 ? error.solutions[0].id : null
  );
  const [showAllSolutions, setShowAllSolutions] = useState(false);

  const severityStyles = {
    info: "border-blue-500/30 bg-blue-500/5",
    warning: "border-yellow-500/30 bg-yellow-500/5",
    error: "border-red-500/30 bg-red-500/5",
    critical: "border-red-600/50 bg-red-600/10"
  };

  const IconComponent = error.icon;
  const visibleSolutions = showAllSolutions ? error.solutions : error.solutions.slice(0, 2);

  if (compact) {
    return (
      <Alert className={cn("border-gray-700/50", severityStyles[error.severity], className)}>
        <IconComponent className="h-4 w-4" />
        <AlertDescription className="text-sm">
          <span className="font-medium">{error.title}:</span> {error.description}
          {onRetry && (
            <Button
              onClick={onRetry}
              disabled={isRetrying}
              size="sm"
              variant="outline"
              className="ml-2"
            >
              {isRetrying ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                "Try Again"
              )}
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={cn(
      "border-gray-700/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm",
      severityStyles[error.severity],
      className
    )}>
      <CardHeader>
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <IconComponent className="h-8 w-8 text-current" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-xl text-white mb-2">
              {error.title}
            </CardTitle>
            <CardDescription className="text-gray-300 text-base">
              {error.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Quick Actions */}
        {(onRetry || error.quickFix) && (
          <div className="flex gap-3">
            {onRetry && (
              <Button
                onClick={onRetry}
                disabled={isRetrying}
                className="flex-1"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Trying Again...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </>
                )}
              </Button>
            )}
            {error.quickFix && (
              <Button
                onClick={error.quickFix.action}
                variant="outline"
                className="flex-1"
              >
                {error.quickFix.label}
              </Button>
            )}
          </div>
        )}

        {/* Solutions */}
        {error.solutions.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-medium text-white">How to Fix This</h3>
            </div>
            
            <div className="space-y-3">
              {visibleSolutions.map((solution) => (
                <SolutionCard
                  key={solution.id}
                  solution={solution}
                  isExpanded={expandedSolution === solution.id}
                  onToggle={() => setExpandedSolution(
                    expandedSolution === solution.id ? null : solution.id
                  )}
                />
              ))}
            </div>

            {error.solutions.length > 2 && !showAllSolutions && (
              <Button
                onClick={() => setShowAllSolutions(true)}
                variant="ghost"
                size="sm"
                className="w-full text-gray-400 hover:text-white"
              >
                Show {error.solutions.length - 2} more solutions
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            )}
          </div>
        )}

        {/* Prevention Tips */}
        {error.preventionTips && error.preventionTips.length > 0 && (
          <>
            <Separator className="bg-gray-700/50" />
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-400" />
                <h3 className="text-lg font-medium text-white">Prevention Tips</h3>
              </div>
              <ul className="space-y-2">
                {error.preventionTips.map((tip, index) => (
                  <li key={index} className="flex gap-3 text-sm text-gray-300">
                    <span className="text-yellow-400 mt-1">•</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}

        {/* Related Help */}
        {error.relatedHelp && error.relatedHelp.length > 0 && (
          <>
            <Separator className="bg-gray-700/50" />
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-green-400" />
                <h3 className="text-lg font-medium text-white">Need More Help?</h3>
              </div>
              <div className="flex flex-wrap gap-2">
                {error.relatedHelp.map((help, index) => (
                  <Button
                    key={index}
                    asChild
                    variant="outline"
                    size="sm"
                  >
                    <Link href={help.link} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-3 w-3 mr-1" />
                      {help.title}
                    </Link>
                  </Button>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
