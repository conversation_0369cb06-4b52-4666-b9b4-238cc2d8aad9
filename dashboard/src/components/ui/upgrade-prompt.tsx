'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Crown, ExternalLink, Check, Sparkles } from 'lucide-react';
import { PremiumBadge } from './premium-badge';

interface UpgradePromptProps {
  trigger?: React.ReactNode;
  feature?: string;
  children?: React.ReactNode;
}

export function UpgradePrompt({ trigger, feature = 'this feature', children }: UpgradePromptProps) {
  const [open, setOpen] = useState(false);

  const features = [
    'Custom hub names with special characters',
    'Unlimited media sharing in calls',
    'Priority support',
    'Early access to new features',
    'Support InterChat development',
  ];

  const defaultTrigger = (
    <Button
      variant="outline"
      className="bg-gradient-to-r from-purple-600/20 to-purple-700/20 border-purple-500/30 text-purple-400 hover:from-purple-600/30 hover:to-purple-700/30 cursor-pointer"
    >
      <Crown className="w-4 h-4 mr-2" />
      Upgrade to Premium
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white">
            <Crown className="w-5 h-5 text-yellow-500" />
            Upgrade to Premium
            <Sparkles className="w-4 h-4 text-purple-400 animate-pulse" />
          </DialogTitle>
          <DialogDescription className="text-gray-300">
            Unlock {feature} and support InterChat development with a Ko-fi Supporter subscription.
          </DialogDescription>
        </DialogHeader>

        <Card className="border-gray-800 bg-gray-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white flex items-center gap-2">
              <Crown className="w-5 h-5 text-yellow-500" />
              Ko-fi Supporter
              <PremiumBadge variant="default" size="sm" />
            </CardTitle>
            <CardDescription className="text-gray-400">
              $2.99/month • Cancel anytime
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
                <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                {feature}
              </div>
            ))}
          </CardContent>
        </Card>

        {children && (
          <div className="text-sm text-gray-400 bg-gray-800/50 p-3 rounded-lg">{children}</div>
        )}

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            className="border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            Maybe Later
          </Button>
          <Button
            asChild
            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 border-none text-white"
          >
            <a
              href="https://ko-fi.com/interchat"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2"
            >
              <Crown className="w-4 h-4" />
              Become a Supporter
              <ExternalLink className="w-4 h-4" />
            </a>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface InlineUpgradePromptProps {
  feature?: string;
  className?: string;
}

export function InlineUpgradePrompt({
  feature = 'this feature',
  className,
}: InlineUpgradePromptProps) {
  return (
    <Card
      className={`border-yellow-500/30 bg-gradient-to-r from-yellow-900/20 to-amber-900/20 shadow-lg shadow-yellow-500/5 ${className}`}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Crown className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <h4 className="font-medium text-yellow-400">✨ Premium Feature</h4>
            <p className="text-sm text-gray-300">
              {feature} is available to Ko-fi Supporters ($2.99/month).
            </p>
            <UpgradePrompt feature={feature}>
              <p>
                Your support helps us maintain and improve InterChat for everyone. Thank you for
                considering becoming a supporter!
              </p>
            </UpgradePrompt>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
