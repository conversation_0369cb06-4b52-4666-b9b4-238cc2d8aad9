import React from "react";
import { <PERSON><PERSON><PERSON>, GlobeIcon, <PERSON><PERSON>les, Zap } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "./ui/button";

type StepType = {
  number: string;
  title: string;
  description: string | React.ReactNode;
  gradient: string;
  icon: React.ReactNode;
  link: string;
  isExternal: boolean;
};

const steps: StepType[] = [
  {
    number: "01",
    title: "Add InterChat to Discord",
    description:
      "Invite InterChat to your server with a single click. No complex setup required.",
    gradient: "from-gray-800/50 to-gray-900/50",
    icon: <Sparkles className="w-6 h-6 text-primary" />,
    link: "/invite",
    isExternal: true,
  },
  {
    number: "02",
    title: "Pick Your Hub",
    description: (
      <>
        Find a hub from the{" "}
        <Link
          href="/hubs"
          className="underline text-white hover:text-primary-alt transition-colors"
        >
          Hub Discovery
        </Link>{" "}
        and link a channel to a hub to get started.
      </>
    ),
    gradient: "from-gray-800/50 to-gray-900/50",
    icon: <GlobeIcon className="w-6 h-6 text-primary" />,
    link: "/hubs",
    isExternal: false,
  },
  {
    number: "03",
    title: "Start Global Conversations",
    description:
      "That's it! Your community can now engage with like-minded servers worldwide.",
    gradient: "from-gray-800/50 to-gray-900/50",
    icon: <Zap className="w-6 h-6 text-primary-alt" />,
    link: "/dashboard",
    isExternal: false,
  },
];

export const CTASection = () => {
  return (
    <section className="relative overflow-hidden py-16 bg-gradient-to-b from-gray-900 to-gray-950 border-t border-gray-800/50">
      {/* Background pattern - consistent with hub management */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 32 32%27 width=%2732%27 height=%2732%27 fill=%27none%27 stroke=%27rgb(255 255 255 / 0.05)%27%3e%3cpath d=%27M0 .5H31.5V32%27/%3e%3c/svg%3e')] bg-center opacity-30" />
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-8 relative z-10">
          <div className="inline-block mb-8">
            <div className="flex items-center justify-center bg-gray-800/50 text-white px-6 py-3 rounded-full border border-gray-700/50 shadow-lg backdrop-blur-sm">
              <Sparkles className="w-5 h-5 mr-3 text-primary" />
              <span className="font-medium tracking-wide text-base">
                Get Started in Three Simple Steps
              </span>
            </div>
          </div>

          <h2 className="text-3xl md:text-5xl font-bold mb-4 text-white leading-tight tracking-tight">
            Ready to Connect Your{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-alt relative">
              Discord Servers?
            </span>
          </h2>

          <p className="text-base md:text-lg text-gray-300 mb-6 max-w-2xl mx-auto leading-relaxed">
            Join thousands of thriving Discord servers already using InterChat
            to build connections and grow their communities globally.
          </p>
        </div>

        <div className="text-center relative z-10">
          <div className="flex flex-col sm:flex-row gap-5 mb-8 justify-center">
            <a href="/invite" target="_blank" rel="noreferrer">
              <Button className="cursor-pointer bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white transition-all duration-300 font-semibold text-base px-7 py-5 rounded-xl shadow-lg hover:shadow-xl hover:shadow-primary/20 hover:scale-105 transform hover:-translate-y-1">
                <Sparkles className="mr-2 h-5 w-5" />
                Get Started Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </a>
            <Link href="/hubs">
              <Button
                variant="outline"
                className="cursor-pointer bg-gray-800/50 border-2 border-gray-700/50 text-white hover:bg-gray-700/50 hover:border-gray-600/50 transition-all duration-300 font-semibold text-base px-7 py-5 rounded-xl hover:scale-105 transform hover:-translate-y-1 backdrop-blur-sm"
              >
                <GlobeIcon className="mr-2 h-5 w-5" />
                Browse Hubs
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-4 md:gap-6 relative z-10">
          {steps.map((step) => (
            <div
              key={step.number}
              className="group"
            >
              <div
                className={`rounded-2xl bg-gradient-to-br ${step.gradient} p-5 border border-gray-700/50 shadow-xl transition-all duration-300 hover:shadow-2xl hover:shadow-primary/10 hover:-translate-y-1 h-full flex flex-col items-center backdrop-blur-sm`}
              >
                <div className="mb-1 text-lg font-bold text-gray-300 tracking-wider">
                  {step.number}
                </div>
                {step.isExternal ? (
                  <a
                    href={step.link}
                    target="_blank"
                    rel="noreferrer"
                    className="group-hover:no-underline"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-gray-800/50 rounded-full mb-3 border border-gray-700/50 shadow-md hover:shadow-primary/20 transition-all duration-300 hover:scale-105">
                      {step.icon}
                    </div>
                  </a>
                ) : (
                  <Link href={step.link} className="group-hover:no-underline">
                    <div className="flex items-center justify-center w-12 h-12 bg-gray-800/50 rounded-full mb-3 border border-gray-700/50 shadow-md hover:shadow-primary/20 transition-all duration-300 hover:scale-105">
                      {step.icon}
                    </div>
                  </Link>
                )}
                {step.isExternal ? (
                  <a
                    href={step.link}
                    target="_blank"
                    rel="noreferrer"
                    className="group-hover:no-underline"
                  >
                    <h3 className="text-xl font-bold text-white mb-2 hover:text-primary-alt transition-colors duration-200">
                      {step.title}
                    </h3>
                  </a>
                ) : (
                  <Link href={step.link} className="group-hover:no-underline">
                    <h3 className="text-xl font-bold text-white mb-2 hover:text-primary-alt transition-colors duration-200">
                      {step.title}
                    </h3>
                  </Link>
                )}
                <p className="text-gray-300 text-base leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Added social proof */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center justify-center gap-2 bg-gray-800/50 px-6 py-3 rounded-full border border-gray-700/50 backdrop-blur-sm">
            <span className="text-gray-300 text-sm">Trusted by</span>
            <span className="font-semibold text-white">10,000+</span>
            <span className="text-gray-300 text-sm">
              Discord servers
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};
