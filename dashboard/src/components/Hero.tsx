'use client';

import { AnimatedH<PERSON>Background } from '@/components/AnimatedHeroBackground';
import { InterChatDemo } from '@/components/chat-demo/DemoComponent';
import { Button } from '@/components/ui/button';
import { ArrowRight, Globe, Heart, LayoutDashboard, Sparkles, Users } from 'lucide-react';
import { memo, useEffect, useState } from 'react';

export const HeroSection = memo(() => {
  // Use state to ensure consistent client-side rendering
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Only render the full component on the client side
  if (!isMounted) {
    return (
      <section
        className="relative overflow-hidden pt-0 pb-20 md:pb-28 bg-gradient-to-b from-gray-950 to-gray-900 border-b border-gray-700/50"
        id="hero"
      >
        <div className="container mx-auto px-4 relative z-10 pt-24 md:pt-28">
          {/* Empty placeholder with same height to prevent layout shift */}
          <div className="min-h-[600px]" />
        </div>
      </section>
    );
  }

  return (
    <section
      className="relative overflow-hidden pt-0 pb-20 md:pb-28 bg-gradient-to-b from-gray-950 to-gray-900 border-b border-gray-700/50"
      id="hero"
    >
      {/* Animated background */}
      <AnimatedHeroBackground />
      <div className="absolute inset-0 bg-mesh-gradient opacity-20 mix-blend-overlay z-0" />

      <div className="container mx-auto px-4 relative z-10 pt-24 md:pt-28">
        {/* Hero Content */}
        <div className="flex flex-col lg:flex-row items-center gap-16">
          <div className="flex-1">
            <h1 className="text-4xl text-start lg:text-6xl font-bold mb-7 tracking-tight leading-tight bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-alt to-primary">
              Connected Communities, One Message Away
            </h1>

            <p className="text-lg text-start mb-8 text-gray-300 leading-relaxed max-w-2xl">
              Unite your Discord server with a thriving network of like-minded communities. One
              channel. Hundreds of conversations. Endless possibilities.
            </p>

            {/* CTA buttons */}
            <div className="flex flex-col sm:flex-row gap-5 mb-10">
              <Button
                size="lg"
                className="h-14 px-8 rounded-full bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white font-medium cursor-pointer shadow-xl shadow-primary/25 hover:shadow-2xl hover:shadow-primary/35 transition-all duration-300 transform hover:-translate-y-1 border-none"
              >
                <a
                  href="/invite"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center"
                  aria-label="Add InterChat to your Discord server"
                >
                  Add InterChat Now
                  <ArrowRight
                    className="ml-3 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"
                    aria-hidden="true"
                  />
                </a>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="h-14 px-8 rounded-full text-white bg-gray-800 border border-gray-700/70 hover:bg-gray-700 hover:border-primary/50 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              >
                <a
                  href="/dashboard"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center"
                  aria-label="InterChat Dashboard"
                >
                  Dashboard
                  <LayoutDashboard className="ml-3 h-5 w-5" aria-hidden="true" />
                </a>
              </Button>
            </div>

            {/* Quick Navigation to Dashboard Sections */}
            <div className="flex flex-wrap justify-start gap-4 mb-8">
              <a
                href="https://ko-fi.com/interchat"
                className="flex items-center gap-2 text-gray-300 hover:text-white bg-gray-800/50 hover:bg-gray-700/50 px-4 py-2 rounded-lg border border-gray-700/50 hover:border-gray-600/50 shadow-md transition-all duration-300"
              >
                <Heart className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium">Donate</span>
              </a>
              <a
                href="/dashboard"
                className="flex items-center gap-2 text-gray-300 hover:text-white bg-gray-800/50 hover:bg-gray-700/50 px-4 py-2 rounded-lg border border-gray-700/50 hover:border-gray-600/50 shadow-md transition-all duration-300"
              >
                <LayoutDashboard className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium">Dashboard</span>
              </a>
            </div>

            {/* Stats indicators */}
            <div className="flex flex-wrap justify-start gap-6">
              <div className="flex items-center gap-3 text-gray-200 bg-gray-800/50 px-4 py-2 rounded-full border border-gray-700/50 shadow-md backdrop-blur-sm">
                <Sparkles className="w-5 h-5 text-primary" />
                <span>30+ Hubs Chatting Now</span>
              </div>
              <div className="flex items-center gap-3 text-gray-200 bg-gray-800/50 px-4 py-2 rounded-full border border-gray-700/50 shadow-md backdrop-blur-sm">
                <Users className="w-5 h-5 text-primary-alt" />
                <span>10,000+ Discord Servers</span>
              </div>
              <div className="flex items-center gap-3 text-gray-200 bg-gray-800/50 px-4 py-2 rounded-full border border-gray-700/50 shadow-md backdrop-blur-sm">
                <Globe className="w-5 h-5 text-primary" />
                <span>Worldwide Communities</span>
              </div>
            </div>
          </div>

          {/* Demo container */}
          <div className="flex-1 relative transform-gpu" style={{ willChange: 'transform' }}>
            <div className="absolute -inset-1 bg-gradient-to-r from-primary-alt/50 to-primary/50 rounded-xl blur-md opacity-75 transform-gpu animate-pulse-slow" />

            {/* Single decorative element - reduced for performance */}
            <div className="absolute -bottom-6 sm:-bottom-8 -left-6 sm:-left-8 w-14 sm:w-20 h-14 sm:h-20 bg-primary-alt/20 rounded-full blur-lg z-0" />

            <div className="relative bg-[#313338] p-3 sm:p-5 rounded-xl shadow-2xl border border-gray-700/50">
              {/* Add alt text or aria-label if InterChatDemo contains meaningful visual info */}
              <InterChatDemo />
              <div className="absolute -bottom-3 sm:-bottom-4 -right-3 sm:-right-4 bg-gradient-to-r from-primary to-primary-alt px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg text-xs sm:text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center gap-1.5">
                <span className="h-1.5 w-1.5 bg-green-500 rounded-full animate-pulse hidden sm:block" />
                Live Preview
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

HeroSection.displayName = 'HeroSection';
