"use client";

import { useEffect, useState } from "react";
import { GridPattern } from "./magicui/grid-pattern";

export function AnimatedHeroBackground() {
  // Use state to ensure this only renders on the client
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null; // Return nothing on server-side
  }

  return (
    <>
      {/* Single gradient overlay - simplified for performance */}
      <div className="absolute inset-0 top-[-64px] bg-gradient-to-b from-transparent to-purple-900/10" />

      {/* Grid pattern - simplified */}
      <GridPattern
        width={80}
        height={80}
        className="stroke-primary/10 fill-transparent z-0"
        strokeDasharray="6 6"
      />

      {/* No gradient blobs or animated elements for better performance */}
    </>
  );
}
