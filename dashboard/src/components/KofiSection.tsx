'use client';

import {
  claimManualSubscription,
  claimPendingSubscription,
} from '@/app/dashboard/settings/actions';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  AlertTriangle,
  Coffee,
  Crown,
  DollarSign,
  Link,
  Loader2,
  Mail,
  Receipt,
  Unlink,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface PendingClaim {
  id: string;
  tierName: string | null;
  amount: number;
  currency: string;
  fromName: string;
  expiresAt: Date;
  createdAt: Date;
}

interface Donation {
  id: string;
  amount: number;
  currency: string;
  fromName: string;
  kofiTimestamp: Date;
  processed: boolean;
}

interface KofiSectionProps {
  pendingClaims: PendingClaim[];
  recentDonations: Donation[];
}

export function KofiSection({ pendingClaims, recentDonations }: KofiSectionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showManualClaim, setShowManualClaim] = useState(false);
  const [manualEmail, setManualEmail] = useState('');
  const [transactionId, setTransactionId] = useState('');
  const [isManualClaimLoading, setIsManualClaimLoading] = useState(false);
  const { toast } = useToast();

  const handleClaimPending = async (claimId: string) => {
    setIsLoading(true);
    try {
      const result = await claimPendingSubscription(claimId);
      toast({
        title: 'Premium Claimed!',
        description: result?.message,
      });
    } catch (error) {
      toast({
        title: 'Claim Failed',
        description:
          error instanceof Error ? error.message : 'Failed to claim premium subscription.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualClaim = async () => {
    if (!manualEmail.trim() || !transactionId.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please provide both email and transaction ID.',
        variant: 'destructive',
      });
      return;
    }

    setIsManualClaimLoading(true);
    try {
      const result = await claimManualSubscription(
        manualEmail.trim().toLowerCase(),
        transactionId.trim(),
      );
      toast({
        title: 'Premium Claimed!',
        description: result?.message,
      });
      setShowManualClaim(false);
      setManualEmail('');
      setTransactionId('');
    } catch (error) {
      toast({
        title: 'Claim Failed',
        description:
          error instanceof Error ? error.message : 'Failed to claim premium subscription.',
        variant: 'destructive',
      });
    } finally {
      setIsManualClaimLoading(false);
    }
  };

  const getTierColor = (tierName: string | null) => {
    switch (tierName?.toLowerCase()) {
      case 'supporter':
        return 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900 dark:text-amber-200';
      //   case 'silver':
      //     return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-200';
      //   case 'gold':
      //     return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200';
      //   default:
      //     return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-200';
    }
  };

  const getTierEmoji = (tierName: string | null) => {
    switch (tierName?.toLowerCase()) {
      case 'supporter':
        return '👑';
    }
  };

  return (
    <div className="space-y-6">
      {/* Claim Premium Section */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Claim Premium Subscription
          </CardTitle>
          <CardDescription className="text-gray-400">
            Claim your Ko-fi premium subscription benefits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Display pending claims */}
          {pendingClaims.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-green-400">Unclaimed Subscriptions:</h4>
              {pendingClaims.map((claim) => (
                <div
                  key={claim.id}
                  className="border border-gray-700 rounded-lg p-4 bg-gray-800/50"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{getTierEmoji(claim.tierName)}</span>
                      <Badge className={getTierColor(claim.tierName)}>
                        {claim.tierName || 'Premium'}
                      </Badge>
                    </div>
                    <span className="font-medium text-green-400">
                      ${claim.amount} {claim.currency}
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mb-3">From: {claim.fromName}</p>
                  <Button
                    onClick={() => handleClaimPending(claim.id)}
                    disabled={isLoading}
                    size="sm"
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-600/80 hover:to-pink-600/80"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Claiming...
                      </>
                    ) : (
                      <>
                        <Crown className="h-3 w-3 mr-1" />
                        Claim Premium
                      </>
                    )}
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Manual claim dialog */}
          <div className={pendingClaims.length > 0 ? 'pt-4 border-t border-gray-700' : ''}>
            <Dialog open={showManualClaim} onOpenChange={setShowManualClaim}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full border-gray-600 hover:border-gray-500">
                  <Receipt className="h-4 w-4 mr-2" />
                  Manual Claim with Transaction ID
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Manual Premium Claim</DialogTitle>
                  <DialogDescription>
                    Enter your Ko-fi donation email and transaction ID to claim your premium
                    subscription.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="manual-email">Email Address</Label>
                    <Input
                      id="manual-email"
                      type="email"
                      placeholder="Enter the email used for Ko-fi donation"
                      value={manualEmail}
                      onChange={(e) => setManualEmail(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="transaction-id">Ko-fi Transaction ID</Label>
                    <Input
                      id="transaction-id"
                      placeholder="e.g., 00000000-1111-2222-3333-444444444444"
                      value={transactionId}
                      onChange={(e) => setTransactionId(e.target.value)}
                    />
                    <p className="text-xs text-gray-400">
                      You can find this in your Ko-fi email receipt or transaction history.
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    onClick={handleManualClaim}
                    disabled={isManualClaimLoading}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-600/80 hover:to-pink-600/80"
                  >
                    {isManualClaimLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Claiming Premium...
                      </>
                    ) : (
                      <>
                        <Crown className="h-4 w-4 mr-2" />
                        Claim Premium
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Mail className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-200">
                <p className="font-medium mb-1">How it works:</p>
                <ul className="space-y-1 text-blue-200/80">
                  <li>• If you have pending claims, they&apos;ll appear above automatically</li>
                  <li>• Otherwise, use manual claim with your Ko-fi email and transaction ID</li>
                  <li>• Premium benefits are granted immediately upon successful claim</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Donations Section */}
      {recentDonations.length > 0 && (
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 overflow-hidden">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 via-blue-600/5 to-purple-600/5"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-full blur-2xl"></div>

            <CardHeader className="relative pb-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl shadow-lg">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                    Recent Ko-fi Donations
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your recent Ko-fi donations linked to this account
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </div>

          <CardContent className="relative">
            <div className="mt-4 space-y-4">
              {recentDonations.map((donation) => (
                <div
                  key={donation.id}
                  className="group relative overflow-hidden rounded-xl bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 border border-gray-200/50 dark:border-gray-700/50 p-4 hover:shadow-lg transition-all duration-300 hover:scale-[1.01]"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <div className="relative flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-md">
                        <Coffee className="h-5 w-5 text-white" />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <span className="font-bold text-lg text-green-600 dark:text-green-400">
                              ${donation.amount}
                            </span>
                            <span className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                              {donation.currency}
                            </span>
                          </div>

                          {/* Show SUPPORTER badge for donations >= $1.99 */}
                          {donation.amount >= 1.99 && (
                            <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
                              <Crown className="h-3 w-3 mr-1" />
                              SUPPORTER
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                          <span className="font-medium">From: {donation.fromName}</span>
                          <span className="text-gray-400 dark:text-gray-500">
                            {new Date(donation.kofiTimestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Donation Email Management */}
      <DonationEmailManager />
    </div>
  );
}

// DonationEmailManager component
function DonationEmailManager() {
  const [emailStatus, setEmailStatus] = useState<{
    oAuthEmail: string | null;
    donationEmail: string | null;
    latestDonationEmail: string | null;
    canSetToOAuth: boolean;
    canSetToLatestDonation: boolean;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const { toast } = useToast();

  // Load email status on mount
  useEffect(() => {
    const fetchEmailStatus = async () => {
      try {
        const response = await fetch('/api/user/donation-email');
        if (response.ok) {
          const data = await response.json();
          setEmailStatus(data);
        }
      } catch (error) {
        console.error('Failed to fetch email status:', error);
      }
    };

    fetchEmailStatus();
  }, []);

  const handleEmailAction = async (action: 'unlink' | 'set_to_oauth') => {
    // Show warning modal for switching from custom donation email to Discord email
    if (
      action === 'set_to_oauth' &&
      emailStatus?.donationEmail &&
      emailStatus.donationEmail !== emailStatus.oAuthEmail
    ) {
      setShowWarningModal(true);
      return;
    }

    await performEmailAction(action);
  };

  const performEmailAction = async (action: 'unlink' | 'set_to_oauth') => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/user/donation-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: 'Success',
          description: data.message,
        });

        // Refresh email status
        const statusResponse = await fetch('/api/user/donation-email');
        if (statusResponse.ok) {
          const statusData = await statusResponse.json();
          setEmailStatus(statusData);
        }
      } else {
        throw new Error(data.error || 'Failed to update donation email');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update donation email',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!emailStatus) {
    return null; // Loading or error state
  }

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-xl font-semibold flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Donation Email Settings
        </CardTitle>
        <CardDescription className="text-gray-400">
          Manage which email is used for donation matching
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 rounded-lg bg-gray-800/30">
            <div>
              <p className="text-sm font-medium">Discord Email</p>
              <p className="text-xs text-gray-400">{emailStatus.oAuthEmail || 'Not available'}</p>
            </div>
            {emailStatus.canSetToOAuth && emailStatus.donationEmail !== emailStatus.oAuthEmail && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleEmailAction('set_to_oauth')}
                disabled={isLoading}
                className="text-xs"
              >
                <Link className="h-3 w-3 mr-1" />
                Use This
              </Button>
            )}
          </div>

          {emailStatus.latestDonationEmail &&
            emailStatus.donationEmail !== emailStatus.latestDonationEmail && (
              <div className="flex items-center justify-between p-3 rounded-lg bg-amber-900/20 border border-amber-800/30">
                <div>
                  <p className="text-sm font-medium text-amber-200">Previous Donation Email</p>
                  <p className="text-xs text-amber-300">{emailStatus.latestDonationEmail}</p>
                  <p className="text-xs text-amber-400 mt-1">
                    To use a different email, contact support or donate again
                  </p>
                </div>
              </div>
            )}

          <div className="flex items-center justify-between p-3 rounded-lg bg-blue-900/20 border border-blue-800/30">
            <div>
              <p className="text-sm font-medium text-blue-200">Current Donation Email</p>
              <p className="text-xs text-blue-300">
                {emailStatus.donationEmail || 'Not set (using Discord email)'}
              </p>
            </div>
            {emailStatus.donationEmail && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleEmailAction('unlink')}
                disabled={isLoading}
                className="text-xs border-red-600 text-red-400 hover:bg-red-600/10"
              >
                <Unlink className="h-3 w-3 mr-1" />
                Unlink
              </Button>
            )}
          </div>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p>• Your donation email is used to match Ko-fi donations to your account</p>
          <p>
            • You can only change to Discord email - for other emails, contact support or donate
            again
          </p>
          <p>• To use a different email, make a new donation and use the manual claim feature</p>
        </div>
      </CardContent>

      {/* Warning Modal */}
      <Dialog open={showWarningModal} onOpenChange={setShowWarningModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-amber-400">
              <AlertTriangle className="h-5 w-5" />
              Warning: Email Change
            </DialogTitle>
            <DialogDescription className="space-y-2">
              <p>
                You&apos;re about to change your donation email from{' '}
                <span className="font-medium text-blue-300">{emailStatus?.donationEmail}</span> back
                to your Discord email{' '}
                <span className="font-medium text-green-300">{emailStatus?.oAuthEmail}</span>.
              </p>
              <p className="text-amber-300 font-medium">
                ⚠️ After this change, you won&apos;t be able to use your previous donation email
                again unless you:
              </p>
              <ul className="text-sm text-gray-300 list-disc list-inside space-y-1 ml-2">
                <li>
                  Make a new donation with that email and use the manual claim feature with a valid
                  transaction ID.
                </li>
              </ul>
              <p> Contact support in our Discord server if you need assistance.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={() => setShowWarningModal(false)} className="flex-1">
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setShowWarningModal(false);
                await performEmailAction('set_to_oauth');
              }}
              className="flex-1 bg-amber-600 hover:bg-amber-700"
            >
              Continue
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
