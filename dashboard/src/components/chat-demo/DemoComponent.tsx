"use client";

import type React from "react";

import { useState, useEffect, useCallback, memo, useMemo } from "react";
import {
  AtSign,
  Globe,
  PlusCircle,
  Smile,
  Send,
  Paperclip,
} from "lucide-react";
import Image from "next/image";
import { useMobile } from "@/hooks/use-mobile";

// Simplified chat message component
const ChatMessage = memo(({ msg }: { msg: Message }) => (
  <div className="flex items-start gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-[#32353b] group transition-colors duration-200">
    <div className="relative flex-shrink-0">
      <div className="relative">
        <Image
          src={msg.avatar || "/placeholder.svg"}
          alt={msg.user}
          width={32}
          height={32}
          className="w-8 h-8 sm:w-10 sm:h-10 rounded-full cursor-pointer border-2 border-[#4f545c] hover:border-primary transition-colors"
          loading="lazy"
        />
        <div className="absolute bottom-0 right-0 w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full border-2 border-[#313338]"></div>
      </div>
    </div>
    <div className="flex-1 min-w-0">
      <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
        <p className="font-bold text-white cursor-pointer hover:underline text-xs sm:text-sm">
          {msg.user}
        </p>
        <span className="text-[10px] sm:text-xs text-gray-400 flex items-center gap-1">
          <span className="inline-block w-1 h-1 bg-gray-500 rounded-full"></span>
          {msg.server}
        </span>
        {msg.badge && (
          <span className="px-1 sm:px-1.5 py-0.5 text-[10px] sm:text-xs bg-primary/20 text-primary rounded-full font-medium">
            {msg.badge}
          </span>
        )}
        {msg.timestamp && (
          <span className="text-[10px] sm:text-xs text-gray-500 ml-auto">{msg.timestamp}</span>
        )}
      </div>
      <p className="text-xs sm:text-sm text-[#dcddde] mt-0.5 sm:mt-1 leading-relaxed">{msg.text}</p>
    </div>
  </div>
));
ChatMessage.displayName = "ChatMessage";

// Simplified typing indicator
const TypingIndicator = memo(({ user }: { user: string }) => (
  <div className="flex items-center gap-1.5 sm:gap-2 p-2 sm:p-3 text-gray-400 text-[10px] sm:text-xs bg-[#32353b]/30 rounded-lg mx-1 sm:mx-2">
    <div className="flex items-center space-x-0.5 sm:space-x-1">
      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-primary/70" />
      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-primary/70" />
      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-primary/70" />
    </div>
    <span className="font-medium text-primary/80">{user}</span>
    <span>is typing...</span>
  </div>
));
TypingIndicator.displayName = "TypingIndicator";

interface Message {
  id: number;
  server: string;
  user: string;
  text: string;
  avatar: string;
  badge?: string;
  timestamp?: string;
  reactions?: {
    emoji: string;
    count: number;
  }[];
}

// Move static data outside component to prevent re-creation on renders
const INITIAL_MESSAGES: Message[] = [
  {
    id: 1,
    server: "Gaming Community",
    user: "Casey",
    text: "Hey everyone! Anyone here playing the new RPG that just launched? It's getting amazing reviews!",
    avatar: "/pfp1.png",
    badge: "Mod",
    timestamp: "15m ago",
  },
  {
    id: 2,
    server: "Dev Hub",
    user: "Taylor",
    text: "I'm building a Discord bot with InterChat's API. The cross-server features are incredible!",
    avatar: "/pfp2.png",
    reactions: [
      { emoji: "👍", count: 8 },
      { emoji: "🚀", count: 5 },
    ],
    timestamp: "10m ago",
  },
  {
    id: 3,
    server: "Creative Studio",
    user: "Jordan",
    text: "Welcome to InterChat! I love how we can share art and collaborate across different servers now.",
    avatar: "/pfp3.png",
    badge: "Pro",
    timestamp: "5m ago",
  },
  {
    id: 4,
    server: "Music Collective Server",
    user: "Alex",
    text: "This is amazing! Anyone want to join our music production hub later? We're doing a collaborative session.",
    avatar: "/pfp4.mp4",
    timestamp: "2m ago",
  },
  {
    id: 5,
    server: "Science Club",
    user: "Morgan",
    text: "Just connected our server to InterChat and already seeing great conversations happening!",
    avatar: "/pfp1.png",
    timestamp: "Just now",
  },
];

// Simplified header component
const Header = memo(() => (
  <div className="flex items-center justify-between p-3 sm:p-4 border-b border-[#2b2d31] bg-[#2b2d31] relative">
    <div className="flex items-center gap-2 sm:gap-3">
      <div className="relative bg-gradient-to-br from-primary to-primary-alt p-1.5 rounded-full">
        <Globe className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
      </div>
      <div>
        <h2 className="text-sm sm:text-base font-bold text-white flex items-center gap-2">
          InterChat
          <span className="text-[10px] sm:text-xs px-1.5 py-0.5 bg-primary/20 text-primary rounded-full">Hub</span>
        </h2>
        <div className="flex items-center">
          <span className="h-1.5 sm:h-2 w-1.5 sm:w-2 bg-green-500 rounded-full mr-1.5"></span>
          <p className="text-[10px] sm:text-xs text-green-400">5,280 Online</p>
        </div>
      </div>
    </div>
    <div className="flex items-center space-x-1 sm:space-x-2">
      <button
        className="p-1.5 sm:p-2 rounded-md bg-[#4f545c]/50"
        aria-label="Mentions"
      >
        <AtSign className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#b9bbbe]" />
      </button>
      <button
        className="p-1.5 sm:p-2 rounded-md bg-[#4f545c]/50"
        aria-label="Add channel"
      >
        <PlusCircle className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#b9bbbe]" />
      </button>
    </div>
  </div>
));
Header.displayName = "Header";

// Simplified input component
const ChatInput = memo(
  ({
    value,
    onChange,
    onKeyDown,
  }: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onKeyDown: (e: React.KeyboardEvent) => void;
  }) => (
    <div className="p-3 sm:p-4 border-t border-[#2b2d31] bg-[#2b2d31]">
      <div className="flex items-center gap-1.5 sm:gap-2 bg-[#40444b] p-2 sm:p-2.5 rounded-lg">
        <button
          className="text-[#b9bbbe] p-1 sm:p-1.5 rounded-md bg-[#4f545c]/50 hidden sm:block"
          aria-label="Add attachment"
        >
          <PlusCircle className="h-4 w-4 sm:h-5 sm:w-5" />
        </button>
        <button
          className="text-[#b9bbbe] p-1 sm:p-1.5 rounded-md bg-[#4f545c]/50"
          aria-label="Upload file"
        >
          <Paperclip className="h-4 w-4 sm:h-5 sm:w-5" />
        </button>
        <input
          type="text"
          value={value}
          onChange={onChange}
          onKeyDown={onKeyDown}
          placeholder="Message InterChat..."
          className="bg-transparent text-[#dcddde] outline-none flex-1 text-xs sm:text-sm placeholder:text-[#72767d] py-1 sm:py-1.5"
          aria-label="Message input"
        />
        <button
          className="text-[#b9bbbe] p-1 sm:p-1.5 rounded-md bg-[#4f545c]/50"
          aria-label="Add emoji"
        >
          <Smile className="h-4 w-4 sm:h-5 sm:w-5" />
        </button>
        <button
          className={`p-1 sm:p-1.5 rounded-md ${value ? "bg-primary/10 text-primary" : "text-[#b9bbbe] bg-[#4f545c]/50"}`}
          aria-label="Send message"
          disabled={!value}
        >
          <Send className="h-4 w-4 sm:h-5 sm:w-5" />
        </button>
      </div>
      <div className="mt-1.5 sm:mt-2 px-1 sm:px-2">
        <p className="text-[10px] sm:text-xs text-gray-400 italic flex items-center gap-1 sm:gap-1.5">
          <Globe className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary/70" />
          Messages in InterChat are visible to all connected servers
        </p>
      </div>
    </div>
  ),
);
ChatInput.displayName = "ChatInput";

export function InterChatDemo() {
  const [chat, setChat] = useState<Message[]>([]);
  const [index, setIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [userTyping, setUserTyping] = useState("");
  const [inputValue, setInputValue] = useState("");
  const isMobile = useMobile();

  // Memoize messages to prevent unnecessary re-renders
  const messages = useMemo(() => INITIAL_MESSAGES, []);

  // Optimize message adding with useCallback
  const addMessage = useCallback(() => {
    if (index >= messages.length) return;

    setIsTyping(true);
    setUserTyping(messages[index].user);

    const timer = setTimeout(() => {
      setChat((prev) => [...prev, messages[index]]);
      setIsTyping(false);
      setIndex((prev) => prev + 1);
    }, 1200);

    return () => clearTimeout(timer);
  }, [index, messages]);

  // Add messages with a delay - only add first 3 messages for performance
  useEffect(() => {
    if (!isTyping && index < Math.min(3, messages.length)) {
      const timer = setTimeout(addMessage, 800);
      return () => clearTimeout(timer);
    }
  }, [index, isTyping, addMessage, messages.length]);

  // Handle input change
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
    },
    [],
  );

  // Add user message on Enter
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && inputValue.trim()) {
        e.preventDefault();
        setInputValue("");
      }
    },
    [inputValue],
  );

  // Virtualize messages for better performance - show fewer messages
  const visibleMessages = useMemo(() => {
    // Always limit the number of rendered messages to improve performance
    // On mobile show even fewer messages
    if (isMobile) {
      return chat.slice(-3);
    }
    return chat.slice(-5);
  }, [chat, isMobile]);

  return (
    <div className="w-full max-w-lg mx-auto overflow-hidden rounded-xl shadow-2xl border border-[#2b2d31] flex flex-col bg-[#313338] dark:bg-[#2b2d31] relative">

      {/* Header */}
      <Header />

      {/* Chat area - simplified for performance */}
      <div
        className="text-start h-[350px] sm:h-80 overflow-y-auto flex flex-col gap-1.5 p-3 bg-[#313338] dark:bg-[#2b2d31] flex-1"
      >
        {/* Welcome message */}
        <div className="px-3 sm:px-4 py-2 sm:py-3 mb-2 bg-primary/10 border border-primary/20 rounded-lg text-center text-xs sm:text-sm text-white">
          <p className="font-medium">Welcome to the InterChat Hub!</p>
          <p className="text-[10px] sm:text-xs text-gray-300 mt-0.5 sm:mt-1">This is a shared chat between multiple Discord servers</p>
        </div>

        {visibleMessages.map((msg) => (
          <ChatMessage key={msg.id} msg={msg} />
        ))}

        {/* Typing indicator */}
        {isTyping && (
          <TypingIndicator key="typing-indicator" user={userTyping} />
        )}
      </div>

      {/* Input area */}
      <ChatInput
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
      />
    </div>
  );
}
