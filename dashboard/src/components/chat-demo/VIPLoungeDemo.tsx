"use client";

import { Lock } from "lucide-react";
import { memo } from "react";

const VIPLoungeDemo = () => (
  <div className="w-full max-w-lg mx-auto overflow-hidden rounded-lg shadow-2xl border border-[#2b2d31] flex flex-col bg-[#36393f] dark:bg-[#2b2d31] relative">
    {/* Header */}
    <div className="flex items-center justify-between p-3 border-b border-[#2b2d31]">
      <div className="flex items-center gap-2">
        <div className="relative">
          <Lock className="h-5 w-5 text-yellow-500" />
        </div>
        <div>
          <h2 className="text-base font-bold text-white">Cat Lovers Lounge</h2>
          <div className="flex items-center">
            <span className="h-2 w-2 bg-yellow-500 rounded-full mr-1.5"></span>
            <p className="text-xs text-yellow-400">
              Private Hub • Members Only
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Blurred chat area */}
    <div className="relative h-72">
      <div className="absolute inset-0 backdrop-blur-lg bg-[#36393f]/50 z-10" />
      {/* Lock overlay */}
      <div className="absolute inset-0 flex flex-col items-center justify-center z-20 bg-black/40">
        <Lock className="h-12 w-12 text-yellow-500 mb-4" />
        <h3 className="text-xl font-bold text-white mb-2">Invite Only</h3>
        <p className="text-sm text-gray-300 text-center max-w-xs">
          Join our private hub for exclusive content and events.
        </p>
      </div>
    </div>

    {/* Disabled input area */}
    <div className="p-3 border-t border-[#2b2d31]">
      <div className="flex items-center gap-2 bg-[#40444b] p-2 rounded-md opacity-50">
        <input
          type="text"
          disabled
          placeholder="Members only chat..."
          className="bg-transparent text-[#72767d] outline-none flex-1 text-sm cursor-not-allowed"
        />
      </div>
      <div className="mt-2 px-2">
        <p className="text-xs text-gray-400 italic">
          Join us for cat GIFs and purr-fect moments! Ask a moderator for an
          invite.
        </p>
      </div>
    </div>
  </div>
);

export default memo(VIPLoungeDemo);
