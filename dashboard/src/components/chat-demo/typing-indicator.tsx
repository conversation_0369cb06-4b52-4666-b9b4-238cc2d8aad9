"use client";

import { memo } from "react";
import { motion } from "motion/react";

const TypingIndicator = ({ user }: { user: string }) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex items-center gap-1.5 sm:gap-2 p-2 sm:p-3 text-gray-400 text-[10px] sm:text-xs bg-[#32353b]/30 rounded-lg mx-1 sm:mx-2"
  >
    <div className="flex items-center space-x-0.5 sm:space-x-1">
      {[0, 150, 300].map((delay) => (
        <div
          key={delay}
          className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full bg-primary/70"
          style={{
            animation: "bounce 1.4s infinite ease-in-out",
            animationDelay: `${delay}ms`,
            animationFillMode: "both",
          }}
        />
      ))}
    </div>
    <span className="font-medium text-primary/80">{user}</span>
    <span>is typing...</span>
  </motion.div>
);

export default memo(TypingIndicator);
