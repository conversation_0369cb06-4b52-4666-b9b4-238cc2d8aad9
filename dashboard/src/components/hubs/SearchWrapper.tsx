'use client';

import React, { Suspense } from 'react';
import { motion } from 'motion/react';
import dynamic from 'next/dynamic';

// Dynamically import the search component with proper error boundary
const RedesignedAdvancedHubSearch = dynamic(
  () => import('./HubSearch').then((mod) => ({ default: mod.HubSearch })),
  {
    ssr: false,
    loading: () => (
      <div className="relative bg-gradient-to-br from-gray-950 via-gray-950 to-gray-900 border-b border-gray-800/50 pt-20">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="container mx-auto px-4 py-8 relative">
          <div className="text-center mb-8">
            <div className="h-8 bg-gray-800 rounded-lg animate-pulse mb-2 max-w-md mx-auto" />
            <div className="h-4 bg-gray-800 rounded-lg animate-pulse max-w-2xl mx-auto" />
          </div>
          <div className="mb-8">
            <div className="relative max-w-4xl mx-auto">
              <div className="flex gap-4 items-end">
                <div className="flex-1 h-12 bg-gray-800 rounded-lg animate-pulse" />
                <div className="h-12 w-24 bg-gray-800 rounded-lg animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
  },
);

interface EnhancedSearchWrapperProps {
  children: React.ReactNode;
}

/**
 * Enhanced Search Wrapper Component
 * Wraps the existing search page with modern search functionality
 */
export function SearchWrapper({ children }: EnhancedSearchWrapperProps) {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-950 via-gray-950 to-gray-900">
      {/* Redesigned Search Header */}
      <Suspense
        fallback={
          <div className="relative bg-gradient-to-br from-gray-950 via-gray-950 to-gray-900 border-b border-gray-800/50 pt-20">
            <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:32px_32px]" />
            <div className="container mx-auto px-4 py-8 relative">
              <div className="text-center mb-8">
                <div className="h-8 bg-gray-800 rounded-lg animate-pulse mb-2 max-w-md mx-auto" />
                <div className="h-4 bg-gray-800 rounded-lg animate-pulse max-w-2xl mx-auto" />
              </div>
              <div className="mb-8">
                <div className="relative max-w-4xl mx-auto">
                  <div className="flex gap-4 items-end">
                    <div className="flex-1 h-12 bg-gray-800 rounded-lg animate-pulse" />
                    <div className="h-12 w-24 bg-gray-800 rounded-lg animate-pulse" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        }
      >
        <RedesignedAdvancedHubSearch />
      </Suspense>

      {/* Existing Content with Animation and proper spacing */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="pt-8"
      >
        {children}
      </motion.div>
    </div>
  );
}
