"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  SlidersHorizontal,
  TrendingUp,
  Clock,
  Users,
  Star,
  Activity,
  Sparkles,
  Filter,
  X,
  Shield,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { TagSelector } from './TagSelector';
import { SearchBar } from './SearchBar';
import { NSFWAgeVerificationModal } from './NSFWAgeVerificationModal';
import { useNSFWPreference } from '@/hooks/useNSFWPreference';
import { cn } from '@/lib/utils';
import { SortOptions } from '@/app/hubs/constants';

interface SearchFilters {
  search: string;
  tags: string[];
  sortBy: string;
  contentFilter: string;
  verificationStatus: string;
  language: string;
  minMembers: number;
  maxMembers: number;
  activityLevel: string[];
}

/**
 * Completely Redesigned Advanced Hub Search Component
 * Modern, polished design with enhanced visual appeal and better integration
 */
export function HubSearch() {
  const [showFilters, setShowFilters] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [showAgeVerification, setShowAgeVerification] = useState(false);

  // NSFW preference hook
  const {
    showNsfwHubs,
    isUpdating: nsfwUpdating,
    isAuthenticated,
    updateNSFWPreference
  } = useNSFWPreference();

  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    tags: [],
    sortBy: SortOptions.Trending,
    contentFilter: 'all',
    verificationStatus: 'all',
    language: 'any',
    minMembers: 0,
    maxMembers: 10000,
    activityLevel: [],
  });

  // Always call hooks, but guard their usage
  const router = useRouter();
  const searchParams = useSearchParams();

  // Set mounted flag after hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Initialize filters from URL params - only after mounting
  useEffect(() => {
    if (!mounted || !searchParams) return;

    try {
      const search = searchParams.get('search') || '';
      const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
      const sortBy = searchParams.get('sort') || SortOptions.Trending;
      const contentFilter = searchParams.get('contentFilter') || 'all';
      const verificationStatus = searchParams.get('verificationStatus') || 'all';
      const language = searchParams.get('language') || 'any';
      const minMembers = parseInt(searchParams.get('minMembers') || '0');
      const maxMembers = parseInt(searchParams.get('maxMembers') || '10000');
      const activityLevel = searchParams.get('activityLevels')?.split(',').filter(Boolean) || [];

      setFilters({
        search,
        tags,
        sortBy,
        contentFilter,
        verificationStatus,
        language,
        minMembers,
        maxMembers,
        activityLevel,
      });
    } catch (error) {
      console.error('Error initializing filters from URL:', error);
    }
  }, [mounted, searchParams]);

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // Only update URL if mounted and router is available
    if (!mounted || !router) return;

    try {
      // Build URL params
      const params = new URLSearchParams();
      if (updatedFilters.search) params.set('search', updatedFilters.search);
      if (updatedFilters.tags.length > 0) params.set('tags', updatedFilters.tags.join(','));
      if (updatedFilters.sortBy !== SortOptions.Trending) params.set('sort', updatedFilters.sortBy);
      if (updatedFilters.contentFilter !== 'all') params.set('contentFilter', updatedFilters.contentFilter);
      if (updatedFilters.verificationStatus !== 'all') params.set('verificationStatus', updatedFilters.verificationStatus);
      if (updatedFilters.language && updatedFilters.language !== 'any') params.set('language', updatedFilters.language);
      if (updatedFilters.minMembers > 0) params.set('minMembers', updatedFilters.minMembers.toString());
      if (updatedFilters.maxMembers < 10000) params.set('maxMembers', updatedFilters.maxMembers.toString());
      if (updatedFilters.activityLevel.length > 0) params.set('activityLevels', updatedFilters.activityLevel.join(','));

      router.push(`/hubs/search?${params.toString()}`);
    } catch (error) {
      console.error('Error updating URL filters:', error);
    }
  };

  const handleTagsChange = (tags: string[]) => {
    updateFilters({ tags });
  };

  // Handle NSFW toggle with age verification
  const handleNSFWToggle = async (enabled: boolean) => {
    if (!isAuthenticated) {
      // For non-authenticated users, just update the content filter
      updateFilters({ contentFilter: enabled ? 'nsfw' : 'all' });
      return;
    }

    if (enabled && !showNsfwHubs) {
      // Show age verification modal when enabling NSFW
      setShowAgeVerification(true);
    } else {
      // Directly update preference when disabling or already enabled
      await updateNSFWPreference(enabled);
      updateFilters({ contentFilter: enabled ? 'nsfw' : 'all' });
    }
  };

  // Handle age verification confirmation
  const handleAgeVerificationConfirm = async () => {
    const success = await updateNSFWPreference(true);
    if (success) {
      updateFilters({ contentFilter: 'nsfw' });
    }
    setShowAgeVerification(false);
  };

  // Handle age verification cancel
  const handleAgeVerificationCancel = () => {
    setShowAgeVerification(false);
  };

  const clearAllFilters = () => {
    setFilters({
      search: '',
      tags: [],
      sortBy: SortOptions.Trending,
      contentFilter: 'all',
      verificationStatus: 'all',
      language: 'any',
      minMembers: 0,
      maxMembers: 10000,
      activityLevel: [],
    });

    if (mounted && router) {
      try {
        router.push('/hubs/search');
      } catch (error) {
        console.error('Error clearing filters:', error);
      }
    }
  };

  const sortOptions = [
    { value: SortOptions.Trending, label: 'Trending', icon: TrendingUp, color: 'from-orange-500 to-red-500' },
    { value: SortOptions.Activity, label: 'Activity', icon: Activity, color: 'from-purple-500 to-pink-500' },
    { value: SortOptions.Created, label: 'Newest', icon: Clock, color: 'from-blue-500 to-cyan-500' },
    { value: SortOptions.Servers, label: 'Most Members', icon: Users, color: 'from-green-500 to-emerald-500' },
    { value: SortOptions.Rating, label: 'Highest Rated', icon: Star, color: 'from-yellow-500 to-amber-500' },
  ];

  const activityLevels = [
    { value: 'high', label: 'Very Active', color: 'bg-green-500' },
    { value: 'medium', label: 'Active', color: 'bg-yellow-500' },
    { value: 'low', label: 'Quiet', color: 'bg-gray-500' },
  ];

  const hasActiveFilters = filters.tags.length > 0 ||
    filters.contentFilter !== 'all' ||
    filters.verificationStatus !== 'all' ||
    filters.language !== 'any' ||
    filters.minMembers > 0 ||
    filters.maxMembers < 10000 ||
    filters.activityLevel.length > 0;

  const activeFilterCount = filters.tags.length +
    (filters.contentFilter !== 'all' ? 1 : 0) +
    (filters.verificationStatus !== 'all' ? 1 : 0) +
    (filters.language !== 'any' ? 1 : 0) +
    (filters.minMembers > 0 ? 1 : 0) +
    (filters.maxMembers < 10000 ? 1 : 0) +
    filters.activityLevel.length;

  // Show loading state until component is mounted
  if (!mounted) {
    return (
      <div className="relative bg-gradient-to-br from-gray-950 via-gray-950 to-gray-900 border-b border-gray-800/50 pt-20">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:32px_32px]" />
        <div className="container mx-auto px-4 py-8 relative">
          <div className="text-center mb-8">
            <div className="h-8 bg-gray-800 rounded-lg animate-pulse mb-2 max-w-md mx-auto" />
            <div className="h-4 bg-gray-800 rounded-lg animate-pulse max-w-2xl mx-auto" />
          </div>
          <div className="mb-8">
            <div className="relative max-w-4xl mx-auto">
              <div className="flex gap-4 items-end">
                <div className="flex-1 h-12 bg-gray-800 rounded-lg animate-pulse" />
                <div className="h-12 w-24 bg-gray-800 rounded-lg animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative bg-gradient-to-br from-gray-950 via-gray-950 to-gray-900 border-b border-gray-800/50 pt-20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:32px_32px]" />

      <div className="container mx-auto px-4 py-8 relative">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold text-white mb-2 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            Find Your Perfect Hub
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Discover active Discord communities that match your interests. Use filters to find exactly what you&apos;re looking for.
          </p>
        </motion.div>

        {/* Modern Search Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="relative max-w-4xl mx-auto">
            <div className="flex gap-4 items-end">
              {/* Enhanced Search Bar */}
              <div className="flex-1">
                <SearchBar
                  initialValue={filters.search}
                  placeholder="Search hubs by name, description, or topic..."
                  size="default"
                  variant="header"
                  showButton={true}
                  onSearch={(term) => updateFilters({ search: term })}
                  className="w-full"
                />
              </div>

              {/* Modern Filter Button */}
              <Button
                type="button"
                variant="outline"
                size="lg"
                onClick={() => setShowFilters(!showFilters)}
                className={cn(
                  "relative h-12 px-6 border-gray-700/50 bg-gray-800/50 backdrop-blur-sm text-gray-300 hover:bg-gray-700/50 hover:border-gray-600/50 transition-all duration-300",
                  showFilters && "bg-blue-600/20 border-blue-500/50 text-blue-300"
                )}
              >
                <SlidersHorizontal className="w-5 h-5 mr-2" />
                <span className="font-medium">Filters</span>
                {activeFilterCount > 0 && (
                  <Badge className="ml-2 bg-blue-600 text-white border-0 text-xs px-2 py-0.5">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </motion.div>

        {/* NSFW Content Toggle - Prominent placement */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.15 }}
          className="mb-6"
        >
          <div className="flex justify-center">
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-2xl p-4 border border-gray-700/50 shadow-lg">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center">
                    {showNsfwHubs || filters.contentFilter === 'nsfw' ? (
                      <AlertTriangle className="w-4 h-4 text-white" />
                    ) : (
                      <Shield className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-white cursor-pointer">
                      Show NSFW Content
                    </label>
                    <p className="text-xs text-gray-400">
                      {isAuthenticated
                        ? "Enable mature content in search results"
                        : "Sign in to save this preference"
                      }
                    </p>
                  </div>
                </div>
                <Switch
                  checked={showNsfwHubs || filters.contentFilter === 'nsfw'}
                  onCheckedChange={handleNSFWToggle}
                  disabled={nsfwUpdating}
                  className="data-[state=checked]:bg-orange-600"
                />
              </div>
              {(showNsfwHubs || filters.contentFilter === 'nsfw') && (
                <div className="mt-3 p-3 bg-orange-500/10 rounded-lg border border-orange-500/20">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-orange-200">
                      NSFW content may include mature themes and explicit material. Must be 18+ to view.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Enhanced Sort Options */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-8"
        >
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3 p-2 bg-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-700/50">
              <span className="text-gray-400 text-sm font-medium px-3">Sort by:</span>
              <div className="flex gap-2">
                {sortOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant={filters.sortBy === option.value ? "default" : "ghost"}
                    size="sm"
                    onClick={() => updateFilters({ sortBy: option.value })}
                    className={cn(
                      "relative overflow-hidden transition-all duration-300 hover:scale-105",
                      filters.sortBy === option.value
                        ? `bg-gradient-to-r ${option.color} text-white shadow-lg`
                        : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
                    )}
                  >
                    <option.icon className="w-4 h-4 mr-2" />
                    <span className="font-medium">{option.label}</span>
                    {filters.sortBy === option.value && (
                      <motion.div
                        layoutId="activeSort"
                        className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"
                        transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                      />
                    )}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Advanced Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: 'auto', y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.4, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <Card className="bg-gray-800/30 backdrop-blur-sm border-gray-700/50 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                        <Filter className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">Advanced Filters</CardTitle>
                        <p className="text-gray-400 text-sm">Refine your search to find the perfect hub</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {hasActiveFilters && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={clearAllFilters}
                          className="text-gray-400 hover:text-white hover:bg-gray-700/50"
                        >
                          <X className="w-4 h-4 mr-1" />
                          Clear All
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowFilters(false)}
                        className="text-gray-400 hover:text-white hover:bg-gray-700/50"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Tag Selector */}
                  <div>
                    <label className="text-sm font-medium text-gray-300 mb-3 block flex items-center gap-2">
                      <Sparkles className="w-4 h-4 text-blue-400" />
                      Tags & Topics
                    </label>
                    <TagSelector
                      selectedTags={filters.tags}
                      onTagsChange={handleTagsChange}
                      placeholder="Search for tags to filter hubs..."
                      maxTags={5}
                      showPopular={true}
                      allowTagCreation={false}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Content Filter */}
                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-3 block">
                        Content Type
                      </label>
                      <Select value={filters.contentFilter} onValueChange={(value) => updateFilters({ contentFilter: value })}>
                        <SelectTrigger className="bg-gray-700/50 border-gray-600/50 text-white hover:bg-gray-600/50 transition-colors">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Content</SelectItem>
                          <SelectItem value="sfw">Safe for Work</SelectItem>
                          <SelectItem value="nsfw">NSFW</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Verification Status */}
                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-3 block">
                        Verification Status
                      </label>
                      <Select value={filters.verificationStatus} onValueChange={(value) => updateFilters({ verificationStatus: value })}>
                        <SelectTrigger className="bg-gray-700/50 border-gray-600/50 text-white hover:bg-gray-600/50 transition-colors">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Hubs</SelectItem>
                          <SelectItem value="verified">Verified Only</SelectItem>
                          <SelectItem value="partnered">Partnered Only</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Language */}
                    <div>
                      <label className="text-sm font-medium text-gray-300 mb-3 block">
                        Language
                      </label>
                      <Select value={filters.language} onValueChange={(value) => updateFilters({ language: value })}>
                        <SelectTrigger className="bg-gray-700/50 border-gray-600/50 text-white hover:bg-gray-600/50 transition-colors">
                          <SelectValue placeholder="Any Language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="any">Any Language</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="de">German</SelectItem>
                          <SelectItem value="ja">Japanese</SelectItem>
                          <SelectItem value="ko">Korean</SelectItem>
                          <SelectItem value="zh">Chinese</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Member Count Range */}
                  <div>
                    <label className="text-sm font-medium text-gray-300 mb-4 block">
                      Member Count: {filters.minMembers} - {filters.maxMembers === 10000 ? '10000+' : filters.maxMembers}
                    </label>
                    <div className="px-3">
                      <Slider
                        value={[filters.minMembers, filters.maxMembers]}
                        onValueChange={([min, max]) => updateFilters({ minMembers: min, maxMembers: max })}
                        max={10000}
                        step={100}
                        className="w-full"
                      />
                    </div>
                  </div>

                  {/* Activity Level */}
                  <div>
                    <label className="text-sm font-medium text-gray-300 mb-4 block">
                      Activity Level
                    </label>
                    <div className="flex gap-6">
                      {activityLevels.map((level) => (
                        <div key={level.value} className="flex items-center space-x-3">
                          <Checkbox
                            id={level.value}
                            checked={filters.activityLevel.includes(level.value)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                updateFilters({ activityLevel: [...filters.activityLevel, level.value] });
                              } else {
                                updateFilters({ activityLevel: filters.activityLevel.filter(l => l !== level.value) });
                              }
                            }}
                            className="border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                          />
                          <label htmlFor={level.value} className="text-sm text-gray-300 flex items-center cursor-pointer">
                            <div className={`w-3 h-3 rounded-full ${level.color} mr-2`} />
                            {level.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* NSFW Age Verification Modal */}
      <NSFWAgeVerificationModal
        isOpen={showAgeVerification}
        onClose={handleAgeVerificationCancel}
        onConfirm={handleAgeVerificationConfirm}
        isLoading={nsfwUpdating}
      />
    </div>
  );
}
