import { cn } from "@/lib/utils";
import { Shield, AlertTriangle } from "lucide-react";

interface NSFWBadgeProps {
  isNsfw: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

export function NSFWBadge({ 
  isNsfw, 
  className, 
  size = "md", 
  showIcon = true 
}: NSFWBadgeProps) {
  if (!isNsfw) {
    return null;
  }

  const sizeClasses = {
    sm: "px-1.5 py-0.5 text-xs",
    md: "px-2 py-1 text-sm",
    lg: "px-3 py-1.5 text-base",
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4", 
    lg: "w-5 h-5",
  };

  return (
    <div
      className={cn(
        "inline-flex items-center gap-1 rounded-full font-medium",
        "bg-red-500/20 text-red-400 border border-red-500/30",
        "backdrop-blur-sm",
        sizeClasses[size],
        className
      )}
    >
      {showIcon && <AlertTriangle className={iconSizes[size]} />}
      <span>NSFW</span>
    </div>
  );
}

interface SFWBadgeProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

export function SFWBadge({ 
  className, 
  size = "md", 
  showIcon = true 
}: SFWBadgeProps) {
  const sizeClasses = {
    sm: "px-1.5 py-0.5 text-xs",
    md: "px-2 py-1 text-sm",
    lg: "px-3 py-1.5 text-base",
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4", 
    lg: "w-5 h-5",
  };

  return (
    <div
      className={cn(
        "inline-flex items-center gap-1 rounded-full font-medium",
        "bg-green-500/20 text-green-400 border border-green-500/30",
        "backdrop-blur-sm",
        sizeClasses[size],
        className
      )}
    >
      {showIcon && <Shield className={iconSizes[size]} />}
      <span>SFW</span>
    </div>
  );
}
