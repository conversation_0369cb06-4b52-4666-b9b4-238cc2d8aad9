"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Users } from 'lucide-react';
import Link from 'next/link';
import { HubCard } from './HubCard';
import { SimplifiedHub } from '@/hooks/use-infinite-hubs';

interface EnhancedHubGridProps {
  hubs: SimplifiedHub[];
  isLoading?: boolean;
  viewMode?: 'grid' | 'list';
}

/**
 * Enhanced Hub Grid Component
 * Now uses the redesigned hub cards for modern, consistent appearance
 */
export function HubGrid({ hubs, isLoading = false, viewMode = 'grid' }: EnhancedHubGridProps) {

  if (isLoading) {
    return (
      <div className={`grid ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'} gap-6`}>
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="h-[420px] bg-gray-800/50 border border-gray-700/50 rounded-2xl animate-pulse">
            <div className="p-5 space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-14 h-14 bg-gray-600 rounded-xl" />
                <div className="flex-1">
                  <div className="h-5 bg-gray-600 rounded mb-2" />
                  <div className="h-4 bg-gray-700 rounded w-3/4" />
                </div>
              </div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-600 rounded" />
                <div className="h-4 bg-gray-600 rounded w-5/6" />
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-600 rounded w-16" />
                  <div className="h-6 bg-gray-600 rounded w-20" />
                </div>
                <div className="h-10 bg-gray-600 rounded mt-auto" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (hubs.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-24 h-24 mx-auto mb-6 bg-gray-800 rounded-full flex items-center justify-center">
          <Users className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">No hubs found</h3>
        <p className="text-gray-400 mb-6">Try adjusting your search criteria or browse popular hubs.</p>
        <Button asChild>
          <Link href="/hubs">Browse All Hubs</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className={`grid ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'} gap-6`}>
      {hubs.map((hub) => (
        <HubCard
          key={hub.id}
          hub={hub}
          variant="search"
          showBanner={true}
          className={viewMode === 'list' ? 'w-full' : ''}
        />
      ))}
    </div>
  );
}
