"use client";

import React from 'react';
import { motion } from 'motion/react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Users,
  MessageCircle,
  Heart,
  Star,
  Shield,
  Crown,
  Clock,
  TrendingUp,
  ArrowRight,
  Eye,
  Zap
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Jo<PERSON><PERSON><PERSON><PERSON> from '@/app/hubs/components/hub-detail/JoinButton';
import { SimplifiedHub } from '@/hooks/use-infinite-hubs';
import { ActivityLevel, ACTIVITY_LEVEL_INFO } from '@/app/hubs/constants';
import { NSFWBadge } from '@/components/hubs/NSFWBadge';
import { NSFWBlurWrapper } from '@/components/hubs/NSFWBlurWrapper';
import { NSFWRevealProvider } from '@/components/hubs/NSFWRevealContext';

interface HubCardProps {
  hub: SimplifiedHub;
  variant?: 'grid' | 'list' | 'featured' | 'compact' | 'search';
  className?: string;
  showBanner?: boolean;
}

/**
 * Modern Hub Card Component
 * Consistent design with fixed dimensions for both grid and list views
 */
export function HubCard({
  hub,
  variant = 'grid',
  className,
  showBanner = true
}: HubCardProps) {

  // Format numbers for display
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Calculate activity level using consistent algorithm
  const getActivityLevel = () => {
    const connections = hub._count?.connections || 0;
    const messages = hub._count?.messages || 0;
    const upvotes = hub.upvotes?.length || 0;
    const reviews = hub.reviews?.length || 0;

    // Calculate recent activity (last 7 days)
    const now = new Date();
    const recentActivityWindow = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const isRecentlyActive = hub.lastActive && hub.lastActive > recentActivityWindow;

    // Enhanced activity score calculation (matching utils.ts)
    const connectionScore = connections;
    const messageScore = Math.min(messages / 10, 50);
    const upvoteScore = upvotes * 2;
    const reviewScore = reviews * 3;
    const recentActivityBonus = isRecentlyActive ? 20 : 0;

    const totalActivity = connectionScore + messageScore + upvoteScore + reviewScore + recentActivityBonus;

    // Adjusted thresholds since we now filter out hubs with 0 connections
    // These hubs all have at least 1 connection, so we can be more discriminating
    let activityLevel: ActivityLevel;
    if (totalActivity >= 50 || connections >= 20) {
      activityLevel = ActivityLevel.HIGH;
    } else if (totalActivity >= 15 || connections >= 5) {
      activityLevel = ActivityLevel.MEDIUM;
    } else {
      activityLevel = ActivityLevel.LOW;
    }

    const info = ACTIVITY_LEVEL_INFO[activityLevel];

    // Map to display format with appropriate icons
    const iconMap = {
      [ActivityLevel.HIGH]: Zap,
      [ActivityLevel.MEDIUM]: TrendingUp,
      [ActivityLevel.LOW]: Clock
    };

    return {
      level: activityLevel,
      color: info.color,
      icon: iconMap[activityLevel],
      label: info.label
    };
  };

  const activity = getActivityLevel();

  // Calculate average rating
  const averageRating = hub.reviews?.length
    ? hub.reviews.reduce((sum, review) => sum + review.rating, 0) / hub.reviews.length
    : 0;

  // Format relative time
  const getRelativeTime = (dateInput: string | Date): string => {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return `${Math.floor(diffInDays / 7)}w ago`;
  };

  // Grid variant - consistent card design
  if (variant === 'grid') {
    return (
      <NSFWRevealProvider>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.05 }}
          className={cn("group relative", className)}
        >
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 border border-gray-700/50 transition-all duration-300 hover:border-gray-600/70 hover:shadow-2xl hover:shadow-blue-500/10 flex flex-col h-[420px]">
          {/* Glow Effect */}
          <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />

          {/* Banner Section - Fixed Height */}
          {showBanner && (
            <NSFWBlurWrapper
              isNsfw={hub.nsfw}
              className="relative h-32 overflow-hidden flex-shrink-0"
              blurIntensity="medium"
              showRevealButton={true}
              revealButtonPosition="top-right"
              hubId={hub.id}
            >
              {hub.bannerUrl ? (
                <Image
                  src={hub.bannerUrl}
                  alt={`${hub.name} banner`}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-105"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20" />
              )}
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900/80 via-gray-900/20 to-transparent" />

              {/* Activity indicator */}
              <div className="absolute top-3 right-3">
                <div className="flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-black/60 backdrop-blur-md border border-white/20 text-white text-xs font-medium">
                  <activity.icon className="w-3 h-3" />
                  <span>{activity.label}</span>
                </div>
              </div>
            </NSFWBlurWrapper>
          )}

          {/* Main Content */}
          <div className="flex flex-col flex-1 p-5 min-h-0">
            {/* Header with Icon and Title */}
            <div className="flex items-start gap-4 mb-4">
              <div className="relative">
                <NSFWBlurWrapper
                  isNsfw={hub.nsfw}
                  className="relative w-14 h-14 rounded-xl overflow-hidden border-2 border-gray-600/50 group-hover:border-blue-500/50 transition-colors duration-300"
                  blurIntensity="light"
                  showRevealButton={false}
                  hubId={hub.id}
                >
                  {hub.iconUrl ? (
                    <Image
                      src={hub.iconUrl}
                      alt={`${hub.name} icon`}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold text-lg">
                      {hub.name?.charAt(0)?.toUpperCase() || '?'}
                    </div>
                  )}
                </NSFWBlurWrapper>

                {/* Status Badges */}
                <div className="absolute -bottom-1 -right-1 flex gap-1">
                  {hub.verified && (
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center border-2 border-gray-900">
                      <Shield className="w-3 h-3 text-white" />
                    </div>
                  )}
                  {hub.partnered && (
                    <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center border-2 border-gray-900">
                      <Crown className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <NSFWBlurWrapper
                    isNsfw={hub.nsfw}
                    className="flex-1"
                    blurIntensity="light"
                    showRevealButton={false}
                    hubId={hub.id}
                  >
                    <h3 className="font-bold text-lg text-white truncate group-hover:text-blue-300 transition-colors duration-300">
                      {hub.name || 'Unnamed Hub'}
                    </h3>
                  </NSFWBlurWrapper>
                  <NSFWBadge isNsfw={hub.nsfw} size="sm" />
                </div>

                {/* Rating */}
                {averageRating > 0 && (
                  <div className="flex items-center gap-1">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={cn(
                            "w-3 h-3",
                            i < Math.floor(averageRating)
                              ? "text-yellow-400 fill-current"
                              : "text-gray-600"
                          )}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-400">
                      {averageRating.toFixed(1)} ({hub.reviews?.length || 0})
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Short Description */}
            <p className="text-gray-300 text-sm line-clamp-2 leading-relaxed mb-4">
              {hub.shortDescription || hub.description || 'No description available'}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-1.5 mb-4">
              {(hub.tags || []).slice(0, 3).map((tag) => (
                <span
                  key={tag.name}
                  className="px-2.5 py-1 text-xs rounded-full bg-gray-700/50 text-gray-300 border border-gray-600/30 hover:bg-gray-600/50 hover:border-gray-500/50 transition-colors duration-200"
                >
                  {tag.name}
                </span>
              ))}
              {(hub.tags || []).length > 3 && (
                <span className="px-2.5 py-1 text-xs rounded-full bg-gray-700/50 text-gray-400 border border-gray-600/30">
                  +{(hub.tags || []).length - 3}
                </span>
              )}
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-blue-400 mb-1">
                  <Users className="w-4 h-4" />
                  <span className="font-semibold text-sm">{formatNumber(hub._count.connections || 0)}</span>
                </div>
                <span className="text-xs text-gray-500">Servers</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-green-400 mb-1">
                  <MessageCircle className="w-4 h-4" />
                  <span className="font-semibold text-sm">{formatNumber(hub._count?.messages || 0)}</span>
                </div>
                <span className="text-xs text-gray-500">Messages</span>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-red-400 mb-1">
                  <Heart className="w-4 h-4" />
                  <span className="font-semibold text-sm">{formatNumber((hub.upvotes || []).length)}</span>
                </div>
                <span className="text-xs text-gray-500">Likes</span>
              </div>
            </div>

            {/* Last Active */}
            <div className="flex items-center justify-center text-xs text-gray-500 mb-4">
              <Clock className="w-3 h-3 mr-1" />
              <span>Active {getRelativeTime(hub.lastActive)}</span>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-auto">
              <Link href={`/hubs/${hub.id}`} className="flex-1">
                <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium transition-all duration-300 group-hover:scale-[1.02]">
                  <Eye className="w-4 h-4 mr-2" />
                  View
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <div className="flex-1">
                <JoinButton hubName={hub.name || 'Unnamed Hub'} hubId={hub.id} />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
      </NSFWRevealProvider>
    );
  }

  // List variant - horizontal layout
  return (
    <NSFWRevealProvider>
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: 0.05 }}
        className={cn("group relative", className)}
      >
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-gray-900 via-gray-900 to-gray-800 border border-gray-700/50 transition-all duration-300 hover:border-gray-600/70 hover:shadow-xl hover:shadow-blue-500/10 flex h-[140px]">
        {/* Glow Effect */}
        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />

        {/* Banner/Icon Section */}
        <NSFWBlurWrapper
          isNsfw={hub.nsfw}
          className="relative w-48 flex-shrink-0 overflow-hidden"
          blurIntensity="medium"
          showRevealButton={true}
          revealButtonPosition="center"
          hubId={hub.id}
        >
          {hub.bannerUrl ? (
            <Image
              src={hub.bannerUrl}
              alt={`${hub.name} banner`}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
              {hub.iconUrl ? (
                <div className="relative w-16 h-16 rounded-xl overflow-hidden border-2 border-white/20">
                  <Image
                    src={hub.iconUrl}
                    alt={`${hub.name} icon`}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold text-2xl">
                  {hub.name?.charAt(0)?.toUpperCase() || '?'}
                </div>
              )}
            </div>
          )}
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-gray-900/80" />
        </NSFWBlurWrapper>

        {/* Content Section */}
        <div className="flex flex-col flex-1 p-4 min-w-0">
          {/* Header */}
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <NSFWBlurWrapper
                  isNsfw={hub.nsfw}
                  className="flex-1"
                  blurIntensity="light"
                  showRevealButton={false}
                  hubId={hub.id}
                >
                  <h3 className="font-bold text-lg text-white truncate group-hover:text-blue-300 transition-colors duration-300">
                    {hub.name || 'Unnamed Hub'}
                  </h3>
                </NSFWBlurWrapper>
                {hub.verified && (
                  <Shield className="w-4 h-4 text-blue-500 flex-shrink-0" />
                )}
                {hub.partnered && (
                  <Crown className="w-4 h-4 text-purple-500 flex-shrink-0" />
                )}
                <NSFWBadge isNsfw={hub.nsfw} size="sm" />
              </div>
              <p className="text-gray-300 text-sm line-clamp-1 mb-2">
                {hub.shortDescription || hub.description || 'No description available'}
              </p>
            </div>

            {/* Activity indicator */}
            <div className="flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-gray-800/60 backdrop-blur-md border border-gray-600/30 text-white text-xs font-medium ml-3">
              <activity.icon className="w-3 h-3" />
              <span>{activity.label}</span>
            </div>
          </div>

          {/* Tags and Stats */}
          <div className="flex items-center justify-between mt-auto">
            {/* Tags */}
            <div className="flex flex-wrap gap-1.5 flex-1 min-w-0 mr-4">
              {(hub.tags || []).slice(0, 2).map((tag) => (
                <span
                  key={tag.name}
                  className="px-2 py-0.5 text-xs rounded-full bg-gray-700/50 text-gray-300 border border-gray-600/30 truncate"
                >
                  {tag.name}
                </span>
              ))}
              {(hub.tags || []).length > 2 && (
                <span className="px-2 py-0.5 text-xs rounded-full bg-gray-700/50 text-gray-400 border border-gray-600/30">
                  +{(hub.tags || []).length - 2}
                </span>
              )}
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-xs text-gray-400 flex-shrink-0">
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                <span>{formatNumber(hub._count.connections || 0)}</span>
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="w-3 h-3" />
                <span>{formatNumber(hub._count?.messages || 0)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-3 h-3" />
                <span>{formatNumber((hub.upvotes || []).length)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 p-4 justify-center w-32 flex-shrink-0">
          <Link href={`/hubs/${hub.id}`}>
            <Button size="sm" className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium transition-all duration-300">
              <Eye className="w-3 h-3 mr-1" />
              View
            </Button>
          </Link>
          <JoinButton hubName={hub.name || 'Unnamed Hub'} hubId={hub.id} />
        </div>
      </div>
    </motion.div>
    </NSFWRevealProvider>
  );
}
