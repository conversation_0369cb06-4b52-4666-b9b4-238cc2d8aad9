"use client";

import { motion } from 'motion/react';
import { Activity, Globe, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';

interface StatsData {
  activeServers: number;
  publicHubs: number;
  weeklyMessages: number;
}

interface StatsBarProps {
  stats?: StatsData;
}

/**
 * 2️⃣ Stats Bar (below hero)
 * Quick stats with icons and animated counter-up effect
 */
export function StatsBar({ stats }: StatsBarProps) {
  const [animatedStats, setAnimatedStats] = useState({
    activeServers: 0,
    publicHubs: 0,
    weeklyMessages: 0,
  });

  const defaultStats = {
    activeServers: 1200,
    publicHubs: 60,
    weeklyMessages: 300000,
  };

  const finalStats = stats || defaultStats;

  // Animated counter effect
  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60; // 60 FPS
    const stepDuration = duration / steps;

    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);

      setAnimatedStats({
        activeServers: Math.floor(finalStats.activeServers * easeOutQuart),
        publicHubs: Math.floor(finalStats.publicHubs * easeOutQuart),
        weeklyMessages: Math.floor(finalStats.weeklyMessages * easeOutQuart),
      });

      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedStats(finalStats);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, [finalStats]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const statsItems = [
    {
      icon: TrendingUp,
      value: animatedStats.activeServers,
      label: 'Active Servers',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
    },
    {
      icon: Globe,
      value: animatedStats.publicHubs,
      label: 'Public Hubs',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
    },
    {
      icon: Activity,
      value: animatedStats.weeklyMessages,
      label: 'Messages Sent This Week',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
    },
  ];

  return (
    <div className="bg-gray-900/50 border-y border-gray-800/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-8">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {statsItems.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
            >
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-xl ${stat.bgColor} mr-3`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="text-left">
                  <div className="text-3xl font-bold text-white">
                    🔥 {formatNumber(stat.value)}
                  </div>
                  <div className="text-sm text-gray-400">{stat.label}</div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
}
