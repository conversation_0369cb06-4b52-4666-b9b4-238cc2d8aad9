"use client";

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Eye, EyeOff, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { NSFWAgeVerificationModal } from './NSFWAgeVerificationModal';
import { useNSFWPreference } from '@/hooks/useNSFWPreference';
import { useNSFWRevealOptional } from './NSFWRevealContext';

interface NSFWBlurWrapperProps {
  isNsfw: boolean;
  children: React.ReactNode;
  className?: string;
  blurIntensity?: 'light' | 'medium' | 'heavy';
  showRevealButton?: boolean;
  revealButtonPosition?: 'center' | 'top-right' | 'bottom-right';
  hubId?: string; // Hub ID for coordinated reveal state
}

/**
 * Wrapper component that applies blur effects to NSFW content
 * Provides click-to-reveal functionality with age verification
 */
export function NSFWBlurWrapper({
  isNsfw,
  children,
  className,
  blurIntensity = 'medium',
  showRevealButton = true,
  revealButtonPosition = 'center',
  hubId,
}: NSFWBlurWrapperProps) {
  const [showAgeVerification, setShowAgeVerification] = useState(false);

  const {
    showNsfwHubs,
    isAuthenticated,
    updateNSFWPreference,
    isUpdating
  } = useNSFWPreference();

  // Use shared reveal context for coordinated state management
  const { isHubRevealed, revealHub, hideHub } = useNSFWRevealOptional();

  // Determine if this hub is revealed (either globally or temporarily)
  const isRevealed = hubId ? isHubRevealed(hubId) : false;

  // If not NSFW or user has NSFW enabled globally, show content normally
  if (!isNsfw || showNsfwHubs) {
    return <div className={className}>{children}</div>;
  }

  // If content is revealed temporarily, show without blur
  if (isRevealed) {
    return (
      <div className={cn("relative", className)}>
        {children}
        {showRevealButton && hubId && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => hideHub(hubId)}
            className={cn(
              "absolute z-10 bg-gray-900/80 hover:bg-gray-800/80 text-white border border-gray-600/50",
              revealButtonPosition === 'center' && "top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
              revealButtonPosition === 'top-right' && "top-2 right-2",
              revealButtonPosition === 'bottom-right' && "bottom-2 right-2"
            )}
          >
            <EyeOff className="w-4 h-4 mr-1" />
            Hide
          </Button>
        )}
      </div>
    );
  }

  const blurClasses = {
    light: 'blur-sm',
    medium: 'blur-md',
    heavy: 'blur-lg',
  };

  const handleReveal = () => {
    if (!hubId) return; // Can't reveal without hub ID

    if (!isAuthenticated) {
      // For non-authenticated users, just reveal temporarily
      revealHub(hubId);
      return;
    }

    // For authenticated users, show age verification to enable NSFW globally
    setShowAgeVerification(true);
  };

  const handleAgeVerificationConfirm = async () => {
    const success = await updateNSFWPreference(true);
    if (success && hubId) {
      revealHub(hubId);
    }
    setShowAgeVerification(false);
  };

  const handleAgeVerificationCancel = () => {
    setShowAgeVerification(false);
  };

  return (
    <>
      <div className={cn("relative", className)}>
        {/* Blurred content */}
        <div className={cn("transition-all duration-300", blurClasses[blurIntensity])}>
          {children}
        </div>

        {/* Overlay with reveal button */}
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900/20">
          {showRevealButton && (
            <div className={cn(
              "flex flex-col items-center gap-2",
              revealButtonPosition === 'center' && "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
              revealButtonPosition === 'top-right' && "absolute top-2 right-2",
              revealButtonPosition === 'bottom-right' && "absolute bottom-2 right-2"
            )}>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReveal}
                disabled={isUpdating}
                className="bg-gray-900/90 hover:bg-gray-800/90 text-white border-orange-500/50 hover:border-orange-400/50"
              >
                <Eye className="w-4 h-4 mr-1" />
                {isAuthenticated ? 'Enable NSFW' : 'Reveal'}
              </Button>

              {revealButtonPosition === 'center' && (
                <div className="flex items-center gap-1 text-xs text-orange-200 bg-gray-900/80 px-2 py-1 rounded">
                  <AlertTriangle className="w-3 h-3" />
                  <span>NSFW Content</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Age Verification Modal */}
      <NSFWAgeVerificationModal
        isOpen={showAgeVerification}
        onClose={handleAgeVerificationCancel}
        onConfirm={handleAgeVerificationConfirm}
        isLoading={isUpdating}
      />
    </>
  );
}
