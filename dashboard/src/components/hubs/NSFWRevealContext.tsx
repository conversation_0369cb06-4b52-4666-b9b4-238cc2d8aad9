"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface NSFWRevealContextType {
  revealedHubs: Set<string>;
  revealHub: (hubId: string) => void;
  hideHub: (hubId: string) => void;
  isHubRevealed: (hubId: string) => boolean;
}

const NSFWRevealContext = createContext<NSFWRevealContextType | undefined>(undefined);

interface NSFWRevealProviderProps {
  children: ReactNode;
}

/**
 * Context provider for managing NSFW reveal state across multiple components
 * Allows coordinated reveal/hide functionality for all NSFW elements of the same hub
 */
export function NSFWRevealProvider({ children }: NSFWRevealProviderProps) {
  const [revealedHubs, setRevealedHubs] = useState<Set<string>>(new Set());

  const revealHub = (hubId: string) => {
    setRevealedHubs(prev => new Set([...prev, hubId]));
  };

  const hideHub = (hubId: string) => {
    setRevealedHubs(prev => {
      const newSet = new Set(prev);
      newSet.delete(hubId);
      return newSet;
    });
  };

  const isHubRevealed = (hubId: string) => {
    return revealedHubs.has(hubId);
  };

  return (
    <NSFWRevealContext.Provider value={{
      revealedHubs,
      revealHub,
      hideHub,
      isHubRevealed,
    }}>
      {children}
    </NSFWRevealContext.Provider>
  );
}

/**
 * Hook to access NSFW reveal context
 */
export function useNSFWReveal() {
  const context = useContext(NSFWRevealContext);
  if (context === undefined) {
    throw new Error('useNSFWReveal must be used within a NSFWRevealProvider');
  }
  return context;
}

/**
 * Hook to access NSFW reveal context with fallback for components outside provider
 */
export function useNSFWRevealOptional() {
  const context = useContext(NSFWRevealContext);
  
  // Fallback state for components not wrapped in provider
  const [localRevealedHubs, setLocalRevealedHubs] = useState<Set<string>>(new Set());
  
  if (context === undefined) {
    return {
      revealedHubs: localRevealedHubs,
      revealHub: (hubId: string) => {
        setLocalRevealedHubs(prev => new Set([...prev, hubId]));
      },
      hideHub: (hubId: string) => {
        setLocalRevealedHubs(prev => {
          const newSet = new Set(prev);
          newSet.delete(hubId);
          return newSet;
        });
      },
      isHubRevealed: (hubId: string) => {
        return localRevealedHubs.has(hubId);
      },
    };
  }
  
  return context;
}
