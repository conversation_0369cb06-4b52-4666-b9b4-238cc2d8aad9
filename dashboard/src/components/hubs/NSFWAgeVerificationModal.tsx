"use client";

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Shield, ExternalLink } from 'lucide-react';
import { motion } from 'motion/react';

interface NSFWAgeVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

/**
 * Age Verification Modal for NSFW Content
 * Displays when users attempt to enable NSFW content filtering
 */
export function NSFWAgeVerificationModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}: NSFWAgeVerificationModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gray-900 border-gray-800">
        <DialogHeader className="text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="mx-auto w-16 h-16 bg-orange-500/20 rounded-full flex items-center justify-center mb-4"
          >
            <AlertTriangle className="w-8 h-8 text-orange-400" />
          </motion.div>

          <DialogTitle className="text-xl font-bold text-white text-center">
            Age Verification Required
          </DialogTitle>

          <DialogDescription className="text-gray-400 text-base leading-relaxed text-center">
            You are about to enable NSFW (Not Safe for Work) content in your hub discovery.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Age Confirmation */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-300">
                <p className="font-medium text-white mb-1">Age Requirement</p>
                <p>You must be 18 years or older to view NSFW content. By continuing, you confirm that you meet this age requirement.</p>
              </div>
            </div>
          </div>

          {/* Discord ToS Compliance */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
            <div className="flex items-start gap-3">
              <ExternalLink className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-300">
                <p className="font-medium text-white mb-1">Discord Terms of Service</p>
                <p>
                  NSFW content must comply with{' '}
                  <a
                    href="https://discord.com/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    Discord&apos;s Terms of Service
                  </a>{' '}
                  and{' '}
                  <a
                    href="https://discord.com/guidelines"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    Community Guidelines
                  </a>
                  . Illegal content is strictly prohibited.
                </p>
              </div>
            </div>
          </div>

          {/* Warning Notice */}
          <div className="bg-orange-500/10 rounded-lg p-4 border border-orange-500/20">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-orange-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-orange-200">
                <p className="font-medium text-orange-100 mb-1">Content Warning</p>
                <p>NSFW hubs may contain mature themes, explicit language, and adult content. Proceed with caution.</p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-3 sm:gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading}
            className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Confirming...
              </div>
            ) : (
              'I Confirm (18+)'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
