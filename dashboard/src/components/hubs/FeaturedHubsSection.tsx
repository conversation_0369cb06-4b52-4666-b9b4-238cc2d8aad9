"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Star,
  ChevronLeft,
  ChevronRight,
  Users,
  Activity,
  Shield,
  Crown,
  Sparkles,
  Loader2
} from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import type { FeaturedHubsResponse } from '@/lib/platform-stats';

interface FeaturedHub {
  id: string;
  name: string;
  shortDescription: string;
  iconUrl: string;
  bannerUrl?: string;
  tags: Array<{ name: string }>;
  serverCount: number;
  dailyActivity: number;
  verified: boolean;
  partnered: boolean;
  featured: boolean;
}

interface FeaturedHubsSectionProps {
  initialData?: FeaturedHubsResponse;
}

/**
 * 6️⃣ Featured Hubs / Staff Picks
 * Rotating carousel of recommended hubs with staff picks - now with real data
 */
export function FeaturedHubsSection({ initialData }: FeaturedHubsSectionProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Use initial data from SSR instead of client-side fetch
  const hubs = initialData?.data || [];
  const isLoading = false; // No loading state needed with SSR
  const error = initialData?.error || null;
  const isFallback = initialData?.fallback || false;
  const itemsPerPage = 2;
  const totalPages = Math.ceil(hubs.length / itemsPerPage);

  // Auto-rotation effect
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % totalPages);
    }, 5000); // 5 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, totalPages]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % totalPages);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + totalPages) % totalPages);
    setIsAutoPlaying(false);
  };

  const getCurrentHubs = () => {
    const startIndex = currentIndex * itemsPerPage;
    return hubs.slice(startIndex, startIndex + itemsPerPage);
  };

  const FeaturedCard = ({ hub }: { hub: FeaturedHub }) => (
    <Card className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 hover:scale-[1.02] overflow-hidden h-full">
      {/* Banner */}
      <div className="relative h-32 bg-gradient-to-br from-blue-600/20 to-purple-600/20 overflow-hidden">
        {hub.bannerUrl ? (
          <Image
            src={hub.bannerUrl}
            alt={`${hub.name} banner`}
            fill
            className="object-cover"
          />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <Sparkles className="w-12 h-12 text-blue-400/50" />
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

        {/* Badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          {hub.verified && (
            <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
              <Shield className="w-3 h-3 mr-1" />
              Verified
            </Badge>
          )}
          {hub.partnered && (
            <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30">
              <Crown className="w-3 h-3 mr-1" />
              Partner
            </Badge>
          )}
        </div>

        {/* Hub Icon */}
        <div className="absolute bottom-3 left-3">
          <div className="w-12 h-12 rounded-xl bg-gray-900/80 backdrop-blur-sm border border-gray-600/50 flex items-center justify-center overflow-hidden">
            <Image
              src={hub.iconUrl}
              alt={`${hub.name} icon`}
              width={40}
              height={40}
              className="rounded-lg"
            />
          </div>
        </div>
      </div>

      <CardContent className="p-6">
        {/* Hub Name */}
        <h3 className="text-xl font-bold text-white mb-3 line-clamp-1">
          {hub.name}
        </h3>

        {/* Description */}
        <p className="text-gray-400 text-sm mb-4 line-clamp-3 leading-relaxed">
          {hub.shortDescription}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {hub.tags.slice(0, 3).map((tag, i) => (
            <Badge key={i} variant="secondary" className="text-xs bg-gray-700/50 text-gray-300">
              #{tag.name}
            </Badge>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-blue-400" />
            <span className="text-gray-300">{hub.serverCount} servers</span>
          </div>
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-green-400" />
            <span className="text-gray-300">{(hub.dailyActivity / 1000).toFixed(1)}K/day</span>
          </div>
        </div>

        {/* Action Button */}
        <Button
          asChild
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none"
        >
          <Link href={`/hubs/${hub.id}`}>
            Explore Hub
          </Link>
        </Button>
      </CardContent>
    </Card>
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-gradient-to-b from-gray-900 to-gray-950 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-400" />
            <p className="text-gray-400">Loading featured hubs...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !isFallback) {
    return (
      <div className="bg-gradient-to-b from-gray-900 to-gray-950 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Star className="w-8 h-8 mx-auto mb-4 text-gray-600" />
            <p className="text-gray-400">Unable to load featured hubs</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-b from-gray-900 to-gray-950 py-16">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center gap-3">
            <Star className="w-8 h-8 text-yellow-400 fill-current" />
            Featured Hubs & Staff Picks
            {isFallback && (
              <Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30 text-xs">
                Demo Data
              </Badge>
            )}
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Handpicked communities that showcase the best of what InterChat has to offer
          </p>
        </motion.div>

        {/* Carousel Container */}
        <div className="relative max-w-6xl mx-auto">
          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={prevSlide}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-gray-800/80 border-gray-600 hover:bg-gray-700/80 backdrop-blur-sm"
            disabled={totalPages <= 1}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={nextSlide}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-gray-800/80 border-gray-600 hover:bg-gray-700/80 backdrop-blur-sm"
            disabled={totalPages <= 1}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>

          {/* Carousel Content */}
          <div className="overflow-hidden mx-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-6"
              >
                {getCurrentHubs().map((hub) => (
                  <FeaturedCard key={hub.id} hub={hub} />
                ))}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Pagination Dots */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-8">
              {Array.from({ length: totalPages }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setCurrentIndex(index);
                    setIsAutoPlaying(false);
                  }}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-blue-500 scale-125'
                      : 'bg-gray-600 hover:bg-gray-500'
                  }`}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
