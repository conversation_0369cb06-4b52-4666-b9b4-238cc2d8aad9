"use client";

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { usePopularTags } from '@/hooks/use-tags';
import {
    Activity,
    Heart,
    Sparkles,
    Star,
    TrendingUp,
    Users
} from 'lucide-react';
import { motion } from 'motion/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SearchBar } from './SearchBar';

/**
 * Modern Hub Discovery Hero Section
 * Redesigned with new layout structure as specified
 */
export function HubDiscovery() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { tags: popularTags, isLoading: tagsLoading } = usePopularTags(12);

  // Get initial search term from URL params
  const initialSearchTerm = searchParams.get('search') || '';

  const handleTagClick = (tagName: string) => {
    router.push(`/hubs/search?tags=${encodeURIComponent(tagName)}`);
  };


  const quickDiscoveryOptions = [
    {
      icon: TrendingUp,
      label: 'Trending Now',
      description: 'Most popular hubs this week',
      action: () => router.push('/hubs/search?sort=trending'),
      color: 'from-orange-500 to-red-500',
    },
    {
      icon: Activity,
      label: 'Most Active',
      description: 'Hubs with highest activity today',
      action: () => router.push('/hubs/search?sort=activity'),
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: Star,
      label: 'Top Rated',
      description: 'Highest rated communities',
      action: () => router.push('/hubs/search?sort=rating'),
      color: 'from-yellow-500 to-amber-500',
    },
    {
      icon: Sparkles,
      label: 'New & Fresh',
      description: 'Recently created hubs',
      action: () => router.push('/hubs/search?sort=newest'),
      color: 'from-purple-500 to-pink-500',
    },
  ];

  return (
    <div className="relative min-h-[70vh] bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950 overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl" />
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Main Header */}
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.h1
              className="text-5xl md:text-7xl font-bold mb-8 bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-200 to-purple-200 drop-shadow-2xl"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Discover Amazing
              <br />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">
                Cross-Server Communities
              </span>
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Connect your Discord server to thriving cross-server hubs. Find active communities
              for gaming, art, technology, and more.
            </motion.p>

            <motion.div
              className="flex items-center justify-center gap-2 text-gray-400 mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              <Sparkles className="w-5 h-5 text-yellow-400" />
              <span className="text-lg">Join thousands of servers already connected</span>
              <Sparkles className="w-5 h-5 text-yellow-400" />
            </motion.div>
          </motion.div>

          {/* Enhanced Search Section */}
          <motion.div
            className="mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className="max-w-2xl mx-auto">
              <motion.div
                className="text-center mb-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.7 }}
              >
                <p className="text-gray-400 text-lg">
                  🔍 Start your journey by searching for your interests
                </p>
              </motion.div>
              <SearchBar
                initialValue={initialSearchTerm}
                placeholder="Search for gaming, art, anime, technology hubs..."
                size="large"
                variant="hero"
                showButton={true}
                autoFocus={false}
              />
            </div>
          </motion.div>

          {/* Quick Discovery Options */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <h2 className="text-2xl font-bold text-white text-center mb-8">Quick Discovery</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickDiscoveryOptions.map((option, index) => (
                <motion.div
                  key={option.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
                >
                  <Card
                    className="bg-gray-800/30 backdrop-blur-sm border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 cursor-pointer group hover:scale-105"
                    onClick={option.action}
                  >
                    <CardContent className="p-6 text-center">
                      <div className={`w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-r ${option.color} p-3 group-hover:scale-110 transition-transform duration-300`}>
                        <option.icon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="font-semibold text-white mb-2">{option.label}</h3>
                      <p className="text-sm text-gray-400">{option.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Popular Tags */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <h2 className="text-2xl font-bold text-white text-center mb-8">Popular Topics</h2>
            <div className="flex flex-wrap justify-center gap-3">
              {tagsLoading ? (
                // Loading skeleton
                Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="h-8 w-20 bg-gray-700/50 rounded-full animate-pulse" />
                ))
              ) : (
                popularTags.map((tag, index) => (
                  <motion.div
                    key={tag.name}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 1.1 + index * 0.05 }}
                  >
                    <Badge
                      variant="secondary"
                      className="px-4 py-2 text-sm bg-gray-800/50 hover:bg-gray-700/50 border-gray-600/50 hover:border-gray-500/50 text-gray-200 hover:text-white cursor-pointer transition-all duration-300 hover:scale-105"
                      onClick={() => handleTagClick(tag.name)}
                    >
                      {tag.name}
                      {tag.usageCount && (
                        <span className="ml-2 text-xs text-gray-400">
                          {tag.usageCount}
                        </span>
                      )}
                    </Badge>
                  </motion.div>
                ))
              )}
            </div>
          </motion.div>

          {/* Stats Section */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <Sparkles className="w-8 h-8 text-yellow-400 mr-2" />
                  <span className="text-3xl font-bold text-white">50+</span>
                </div>
                <p className="text-gray-400">Active Hubs</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <Users className="w-8 h-8 text-blue-400 mr-2" />
                  <span className="text-3xl font-bold text-white">1K+</span>
                </div>
                <p className="text-gray-400">Connected Servers</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <Heart className="w-8 h-8 text-red-400 mr-2" />
                  <span className="text-3xl font-bold text-white">10K+</span>
                </div>
                <p className="text-gray-400">Messages Daily</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
