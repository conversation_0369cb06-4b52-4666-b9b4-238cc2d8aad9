---
title: Configuration Guide
description: how to configure your self-hosted InterChat instance through environment variables and other settings.
---

import { Callout } from 'fumadocs-ui/components/callout'

## Environment Variables

InterChat uses environment variables for configuration. Create a `.env` file in the root directory of your installation with the following variables:

### Required Variables

```py
# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token
CLIENT_ID=your_discord_application_id

# Database Configuration
DATABASE_URL=mongodb+srv://username:<EMAIL>/interchat?retryWrites=true&w=majority

# Redis Configuration
REDIS_URL=redis://localhost:6379

VOTE_WEBHOOK_URL=your_vote_webhook_url

NODE_ENV=development # or production
PORT=3000
DEBUG=true

REDIS_URI=redis://localhost:6379

GRAFANA_DASHBOARD_URL=your_grafana_dashboard_url
GRAFANA_SERVICE_ACCOUNT_TOKEN=your_grafana_service_account_token
GRAFANA_CLOUD_URL=your_grafana_cloud_url
GRAFANA_CLOUD_USERNAME=your_username
GRAFANA_CLOUD_API_KEY="your_api_key"

TENOR_KEY=your_tenor_api_key

TOPGG_API_KEY=your_topgg_api_key
TOPGG_WEBHOOK_SECRET=your_topgg_webhook_secret

# Ko-fi Donation Configuration
KOFI_VERIFICATION_TOKEN=your_kofi_verification_token
DONATION_WEBHOOK_URL=your_donation_announcement_webhook_url

```

## Discord Bot Configuration

### Creating a Discord Application

1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name
3. Navigate to the "Bot" tab
4. Click "Add Bot"
5. Under the "Token" section, click "Copy" to get your bot token (used for `DISCORD_TOKEN`)
6. Note your Application ID from the "General Information" tab (used for `CLIENT_ID`)

### Required Bot Permissions

Your bot needs the following permissions:

- Read Messages/View Channels
- Send Messages
- Manage Messages
- Embed Links
- Attach Files
- Read Message History
- Use External Emojis
- View Audit Log
- Add Reactions
- Manage Webhooks

### Required Gateway Intents

In the Discord Developer Portal, under the "Bot" tab, enable:

- Server Members Intent
- Message Content Intent

<Callout type="warning">
  Without these intents, InterChat will not function at all.
</Callout>

## Database Configuration

InterChat uses MongoDB Atlas for its database. The `DATABASE_URL` should follow this format:

```
mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
```

### Database Setup

After setting up your MongoDB Atlas cluster:

```bash
# Generate Prisma client
bunx prisma generate
```

## Redis Configuration

Redis is used for caching and real-time features. The `REDIS_URL` should follow this format:

```
redis://username:password@hostname:port
```

For local Redis instances without authentication, use:

```
redis://localhost:6379
```

## Advanced Configuration

### Metrics and Monitoring

InterChat supports Prometheus metrics:

To use with Grafana Cloud:

```py
GRAFANA_CLOUD_URL=your_grafana_cloud_url
GRAFANA_CLOUD_USERNAME=your_username
GRAFANA_CLOUD_API_KEY=your_api_key
```

### Error Tracking

For production environments, it's recommended to use Sentry for error tracking:

```py
SENTRY_DSN=your_sentry_dsn
SENTRY_ENVIRONMENT=production
```

## Custom Emojis

InterChat uses custom emojis for various features. To set up custom emojis:

1. Run `npm run sync:emojis` to sync the emojis with your bot

<Callout type="info">
  The synced emojis can be viewed in the Discord Developer Portal under the "Emojis" tab.
</Callout>

## Localization

InterChat supports multiple languages. Localization files are stored in the `locales/` directory.

To add a new language:

Visit our [Crowdin project](https://crowdin.com/project/interchat) to contribute translations. (Recommended)

Or manually, if you're familiar with YAML:

1. Copy `locales/en.yml` to `locales/your_language_code.yml`
2. Translate the strings in the new file
3. Run `npm run locale-types` to update the type definitions

## Command Synchronization

After making changes to commands or updating the bot, sync the commands with Discord:

```bash
npm run sync:commands --private --public
```

This ensures that slash commands are properly registered with Discord.

## Security Considerations

When self-hosting InterChat, consider these security best practices:

1. Run the bot with minimal permissions
2. Enable firewall rules to restrict access to your server
3. Regularly update dependencies with `npm install`
4. Back up your database regularly

<Callout type="warning">
  Never share your `.env` file or bot token. Anyone with your bot token can control your bot.
</Callout>
