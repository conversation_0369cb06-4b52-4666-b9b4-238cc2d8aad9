---
title: Essential Commands
description: Master the most important InterChat commands with practical examples and real-world use cases.
icon: Terminal
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Essential Commands

This guide covers the most important InterChat commands you'll use daily, organized by common tasks and use cases.

## Quick Reference

### Most Used Commands

| Command | Purpose | Example |
|---------|---------|---------|
| `/connect` | Connect channel to hub | `/connect hub:Gaming Network` |
| `/disconnect` | Remove connection | `/disconnect` |
| `/hub create` | Create new hub | `/hub create` |
| **Hub Directory** | Find public hubs | Visit [interchat.tech/hubs](https://interchat.tech/hubs) |
| `/connection list` | View all connections | `/connection list` |
| `/call` | Start call with random server | `/call` |
| `/achievements` | View your achievements | `/achievements` |
| `/help` | Show all commands | `/help` |

### Emergency Commands

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `/connection pause` | Stop message flow | Server maintenance, local issues |
| `/blacklist user` | Remove problematic user | Rule violations, harassment |
| `/disconnect` | Leave hub immediately | Hub becomes problematic |
| `/deletemsg` | Delete a message | Inappropriate content, accidental send |
| `/editmsg` | Edit a message | Typos, clarification |

## Getting Started Commands

### Finding and Joining Hubs

<Tabs items={['Browse Public Hubs', 'Join with Invite', 'Search Specific Topics']}>
  <Tab>
    **Discover Communities**

    Visit [interchat.tech/hubs](https://interchat.tech/hubs) directly in your browser.

    **What you can do:**
    - Browse public hubs by category
    - Search for specific topics and interests
    - View ratings, reviews, and member counts
    - Filter by activity level and community size
    - Join hubs directly from the website

    **Best for:**
    - First-time users exploring options
    - Finding active, established communities
    - Discovering new topics of interest
  </Tab>
  <Tab>
    **Join Private Communities**

    ```
    /hub join invite:abc123def
    ```

    **What it does:**
    - Joins a private hub using invitation code
    - Adds your server to the hub's member list
    - Allows you to connect channels to the hub

    **Best for:**
    - Joining exclusive or specialized communities
    - Accepting invitations from friends
    - Participating in invite-only events

    **Getting Invites:**
    - Ask hub owners directly
    - Check community forums or social media
    - Network with other server administrators
  </Tab>
  <Tab>
    **Find Specific Communities**

    Use the hub browser's search function to find:
    - **Game-specific hubs:** "Minecraft", "Valorant", "D&D"
    - **Topic-based hubs:** "Programming", "Art", "Music"
    - **Regional hubs:** "EU Gaming", "NA Students"
    - **Activity-based hubs:** "Study Group", "Creative Writing"

    **Pro Tips:**
    - Try different search terms
    - Check hub descriptions carefully
    - Look at member activity levels
    - Read reviews from other users
  </Tab>
</Tabs>

### Connecting Your First Channel

**Basic Connection:**
```
/connect hub:YourHubName
```

**What happens:**
1. InterChat creates a webhook in your channel
2. Your channel joins the hub's network
3. Messages start flowing between connected servers
4. You receive a welcome message (if configured)

**Before Connecting:**
- Choose an appropriate channel
- Inform your community about the connection
- Review the hub's rules
- Ensure you have proper permissions

## Hub Management Commands

### Creating and Configuring Hubs

**Create New Hub:**
```
/hub create
```

**Essential Configuration Commands:**

<Tabs items={['Basic Setup', 'Rules & Welcome', 'Moderation', 'Advanced Settings']}>
  <Tab>
    **Set Hub Rules:**
    ```
    /hub config rules hub:YourHubName
    ```

    **Configure Basic Settings:**
    ```
    /hub config settings hub:YourHubName
    ```

    **Key Settings to Configure:**
    - ✅ Spam Filter - Prevents spam messages
    - ✅ Block NSFW - Filters inappropriate content
    - ✅ Reactions - Allows emoji reactions
    - ⚠️ Block Invites - Consider your community needs
    - ⚠️ Hide Links - May limit useful sharing
  </Tab>
  <Tab>
    **Set Welcome Message:**
    ```
    /hub config welcome hub:YourHubName
    ```

    **Example Welcome Message:**
    ```
    🎉 Welcome to {hubName}!

    We're excited to have {serverName} join our community!

    📋 **Quick Start:**
    • Read our rules: `/hub config rules`
    • Introduce your server to the community
    • Join our weekly events and discussions

    💬 **Need Help?** Tag a moderator anytime!
    ```

    **Available Variables:**
    - `{hubName}` - Your hub's name
    - `{serverName}` - Joining server's name
    - `{user}` - User who connected the channel
    - `{memberCount}` - Server member count
    - `{totalConnections}` - Total connected servers
  </Tab>
  <Tab>
    **Add Moderators:**
    ```
    /hub moderator add hub:YourHubName user:@TrustedUser position:moderator
    ```

    **Set Up Logging:**
    ```
    /hub config logging hub:YourHubName
    ```

    **Essential Log Types:**
    - **Reports** - User-submitted content reports
    - **Mod Logs** - Record of moderation actions
    - **Join/Leaves** - Server connection activity

    **Configure Content Filtering:**
    ```
    /hub config anti-swear hub:YourHubName
    ```
  </Tab>
  <Tab>
    **Make Hub Public:**
    ```
    /hub visibility hub:YourHubName visibility:public
    ```

    **Requirements for Public Hubs:**
    - Hub must be 24+ hours old
    - Must have 2+ moderators
    - Must have report logging configured

    **Create Invitations:**
    ```
    /hub invite create hub:YourHubName expiry:7d
    ```

    **Transfer Ownership:**
    ```
    /hub transfer hub:YourHubName user:@NewOwner
    ```
  </Tab>
</Tabs>

## Connection Management Commands

### Monitoring Your Connections

**View All Connections:**
```
/connection list
```

**Shows:**
- Connected channels and their hubs
- Connection status (active/paused)
- Last activity timestamps
- Any connection issues

### Managing Connection Behavior

**Pause Connection Temporarily:**
```
/connection pause channel:#your-channel
```

**Resume Paused Connection:**
```
/connection unpause channel:#your-channel
```

**Edit Connection Settings:**
```
/connection edit channel:#your-channel
```

**Available Edits:**
- Change connected channel
- Toggle compact mode
- Refresh webhook settings
- Update connection preferences

<Callout type="info">
  **When to Pause Connections:**
  - During server events that might spam the hub
  - When dealing with local server drama
  - For temporary maintenance or updates
  - During sensitive discussions that should stay private
</Callout>

## Moderation Commands

### Handling Rule Violations

**Issue Warning:**
```
/warn user:@Username hub:YourHubName reason:"Please keep discussions on-topic"
```

**Blacklist User (Temporary):**
```
/blacklist user hub:YourHubName user:@ProblemUser reason:"Repeated spam" duration:7d
```

**Blacklist User (Permanent):**
```
/blacklist user hub:YourHubName user:@ProblemUser reason:"Harassment"
```

**Blacklist Server:**
```
/blacklist server hub:YourHubName server:123456789012345678 reason:"Multiple violations"
```

### Reviewing Moderation Actions

**View All Infractions:**
```
/hub infractions hub:YourHubName
```

**View User-Specific Infractions:**
```
/hub infractions hub:YourHubName user:123456789012345678
```

**List Active Blacklists:**
```
/blacklist list hub:YourHubName
```

**Remove Blacklists:**
```
/unblacklist user hub:YourHubName user:@User
/unblacklist server hub:YourHubName server:123456789012345678
```

## Communication Commands

### Hub-Wide Communication

**Send Announcement:**
```
/hub announce hub:YourHubName
```

**What it does:**
- Sends message to all connected channels
- Appears with special formatting
- Identifies as official hub communication
- Has 1-minute cooldown between announcements

**Best Practices for Announcements:**
- Use for important updates only
- Keep messages concise and clear
- Include relevant details and next steps
- Avoid overuse to prevent announcement fatigue

### Context Menu Commands

**Right-click any message for:**

**Report Message** (Everyone)
- Right-click → Apps → InterChat → Report Message
- Select reason for report
- Sends to hub moderators

**Mod Panel** (Moderators only)
- Right-click → Apps → InterChat → Mod Panel
- Quick access to moderation actions
- Delete message, warn user, blacklist, view infractions

## Additional Features

### Calls and Community Engagement

**Start a Call:**
```
/call
```

**What it does:**
- Connects you with a random server for chat
- Creates temporary connections between both servers
- Allows cross-server communication
- Includes rating and reporting features

**View Your Achievements:**
```
/achievements
/achievements user:@SomeUser
```

**Check Community Leaderboards:**
```
/leaderboard messages      # Top message senders
/leaderboard calls         # Most active call participants
/leaderboard votes         # Top InterChat voters
/leaderboard achievements  # Users with most achievements
```

<Callout type="info">
  **Note:** While these features are fun for engagement, **hubs remain the primary way** to build lasting community connections. Learn more in our [Calls & Achievements Guide](/docs/guides/calls-achievements).
</Callout>

## Advanced Commands

### Hub Information and Analytics

**View Hub Details:**
```
/hub servers hub:YourHubName
```

**Shows:**
- All connected servers
- Connection timestamps
- Activity levels
- Server information

**View Hub Moderators:**
```
/hub moderator list hub:YourHubName
```

### Invitation Management

**Create Hub Invitations:**
```
/hub invite create hub:YourHubName expiry:24h
```

**List Active Invitations:**
```
/hub invite list hub:YourHubName
```

**Revoke Invitation:**
```
/hub invite revoke hub:YourHubName code:abc123def
```

## Command Tips and Tricks

### Efficiency Tips

**Use Tab Completion:**
- Type `/hub` and press Tab to see all hub commands
- Start typing hub names and press Tab for autocomplete
- Use arrow keys to navigate command history

**Bookmark Frequently Used Commands:**
- Save common commands in a text file
- Create Discord message templates
- Use Discord's slash command favorites

**Learn Keyboard Shortcuts:**
- `Ctrl+K` (Windows) or `Cmd+K` (Mac) to open quick switcher
- `Up Arrow` to edit your last message
- `Ctrl+Shift+I` to open developer tools for debugging

### Troubleshooting Commands

**When Things Go Wrong:**

1. **Check Connection Status:**
   ```
   /connection list
   ```

2. **Refresh Connection:**
   ```
   /connection edit channel:#your-channel
   ```

3. **Test Basic Functionality:**
   ```
   /help
   ```

4. **Get Support:**
   - Join [support server](https://discord.gg/cgYgC6YZyX)
   - Check [troubleshooting guide](/docs/guides/troubleshooting)
   - Review [FAQ](/docs/faq)

## Command Permissions

### Understanding Who Can Use What

**Everyone:**
- `/help`, `/hub join`, hub directory website
- Report message (context menu)
- Basic information commands

**Server Administrators:**
- `/connect`, `/disconnect`, `/connection` commands
- Hub creation and basic management
- Server-specific moderation

**Hub Moderators:**
- `/warn`, `/blacklist`, `/unblacklist`
- Mod panel (context menu)
- View infractions and reports

**Hub Managers:**
- All moderator permissions
- Add/remove moderators
- Configure most hub settings

**Hub Owners:**
- All permissions
- Transfer ownership
- Delete hub
- Add/remove managers

<Callout type="info">
  **Permission Tip:** Server administrators always have full control over their own server's connections, regardless of their hub role.
</Callout>

---

**Want to dive deeper?** Explore our [complete command reference](/docs/reference/commands) for every available command and option!
