---
title: Moderation Essentials
description: Keep your InterChat community safe and welcoming with comprehensive moderation tools and best practices.
icon: Shield
---

import { Steps } from 'fumadocs-ui/components/steps'
import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Moderation Essentials

Effective moderation is crucial for maintaining a healthy, welcoming community. This guide covers InterChat's powerful moderation tools and proven strategies for keeping your hub safe.

## What You'll Learn
- Understanding InterChat's moderation hierarchy
- How to handle reports and inappropriate content
- Using blacklists and warnings effectively
- Building and managing a moderation team
- Best practices for consistent enforcement

## Understanding Moderation Roles

### Permission Hierarchy

<Tabs items={['Hub Owner', 'Hub Manager', 'Hub Moderator', 'Server Admin']}>
  <Tab>
    **Full Control Over Hub**

    **Permissions:**
    - All moderation actions
    - Add/remove managers and moderators
    - Configure all hub settings
    - Delete or transfer hub ownership
    - Access all logs and reports

    **Responsibilities:**
    - Set overall hub direction and policies
    - Recruit and train moderation team
    - Handle appeals and complex issues
    - Ensure hub compliance with InterChat ToS

    **Best Practices:**
    - Stay actively involved in the community
    - Delegate appropriately to managers
    - Be available for escalated issues
    - Regularly review hub health and growth
  </Tab>
  <Tab>
    **Trusted Leadership Role**

    **Permissions:**
    - All moderation actions
    - Add/remove moderators (not other managers)
    - Configure most hub settings
    - Access all logs and reports
    - Manage hub rules and welcome messages

    **Responsibilities:**
    - Day-to-day hub management
    - Train and support moderators
    - Handle complex moderation cases
    - Coordinate with other managers

    **Best Practices:**
    - Maintain consistent enforcement standards
    - Communicate regularly with the owner
    - Document important decisions
    - Support moderator development
  </Tab>
  <Tab>
    **Frontline Community Safety**

    **Permissions:**
    - Blacklist users and servers
    - Issue warnings to users
    - Delete inappropriate messages
    - View infractions and reports
    - Use moderation panel tools

    **Responsibilities:**
    - Monitor hub activity regularly
    - Respond to reports promptly
    - Enforce hub rules consistently
    - Escalate complex issues to managers

    **Best Practices:**
    - Be visible and approachable
    - Document moderation actions
    - Stay calm under pressure
    - Seek guidance when uncertain
  </Tab>
  <Tab>
    **Server-Level Authority**

    **Permissions:**
    - Full control over their server's connections
    - Can disconnect from hubs at any time
    - Cannot override hub-wide moderation actions
    - Can moderate their own server's members

    **Responsibilities:**
    - Ensure their server follows hub rules
    - Coordinate with hub moderators
    - Handle server-specific issues
    - Communicate hub policies to their members

    **Best Practices:**
    - Work collaboratively with hub staff
    - Address issues before they escalate
    - Keep hub moderators informed of problems
    - Maintain good standing in the hub community
  </Tab>
</Tabs>

## Handling Reports

### Understanding the Report System

When users report content, it creates a direct line to your moderation team:

1. **User Reports Content** - Right-click message → Apps → InterChat → Report Message
2. **Report Sent to Log Channel** - Appears in your configured reports channel
3. **Moderator Reviews** - Team member investigates the report
4. **Action Taken** - Appropriate response based on violation severity

### Setting Up Report Logging

<Steps>
  ### Configure Report Channel

  ```
  /hub config logging hub:YourHubName
  ```

  Select "Configure Report Logs" and choose a private channel for reports.

  ### Set Up Role Mentions (Optional)

  Configure a role to be pinged when reports arrive:
  - Create a `@Hub Moderators` role
  - Add it to the logging configuration
  - Ensure moderators have this role

  ### Test the System

  Have a trusted member submit a test report to verify everything works correctly.
</Steps>

### Responding to Reports

**When a Report Arrives:**

1. **Review Quickly** - Aim to respond within 1 hour during active hours
2. **Investigate Context** - Look at surrounding messages for full picture
3. **Take Appropriate Action** - Use the moderation panel or commands
4. **Document Decision** - Note reasoning for future reference

**Available Actions:**

<Tabs items={['Take Action', 'Mark Resolved', 'Ignore Report']}>
  <Tab>
    **Opens Moderation Panel with Options:**

    - **Delete Message** - Remove inappropriate content
    - **Warn User** - Issue formal warning
    - **Blacklist User** - Remove user from hub
    - **Blacklist Server** - Remove entire server
    - **View Infractions** - Check user's history

    **When to Use:**
    - Clear rule violations
    - Inappropriate content
    - Spam or harassment
    - Repeated minor offenses
  </Tab>
  <Tab>
    **Indicates Report Has Been Handled**

    **When to Use:**
    - Issue was resolved through other means
    - User was contacted privately
    - Content was already removed
    - Situation de-escalated naturally

    **Best Practice:**
    - Add a note explaining the resolution
    - Keep records for pattern tracking
  </Tab>
  <Tab>
    **Dismisses Reports That Don't Require Action**

    **When to Use:**
    - False reports or misunderstandings
    - Content that doesn't violate rules
    - Personal disputes that should be handled privately
    - Duplicate reports of the same issue

    **Important:**
    - Still document the decision
    - Consider if rules need clarification
  </Tab>
</Tabs>

## Using Blacklists Effectively

### User Blacklisting

**When to Blacklist Users:**
- Repeated rule violations after warnings
- Serious violations (harassment, hate speech, etc.)
- Spam or malicious behavior
- Refusal to follow moderation instructions

**How to Blacklist:**
```
/blacklist user hub:YourHubName user:@ProblemUser reason:"Repeated spam after warnings" duration:7d
```

**Duration Guidelines:**
- **First offense:** 1-3 days
- **Repeat offenses:** 1-2 weeks
- **Serious violations:** 1 month or permanent
- **Extreme cases:** Permanent immediately

### Server Blacklisting

**When to Blacklist Servers:**
- Consistent rule violations from multiple members
- Server admin refuses to address problems
- Coordinated harassment or raiding
- Server culture incompatible with hub values

**How to Blacklist:**
```
/blacklist server hub:YourHubName server:123456789012345678 reason:"Multiple members violating rules, admin unresponsive" duration:30d
```

<Callout type="warning">
  **Server Blacklisting Impact:** This disconnects the entire server and prevents them from rejoining. Use carefully and document thoroughly.
</Callout>

### Managing Blacklists

**Review Active Blacklists:**
```
/blacklist list hub:YourHubName
```

**Remove Blacklists Early:**
```
/unblacklist user hub:YourHubName user:@User
/unblacklist server hub:YourHubName server:123456789012345678
```

**Best Practices:**
- Review temporary blacklists before they expire
- Consider reducing sentences for good behavior
- Document all blacklist decisions
- Be consistent with similar violations

## Warning System

### When to Issue Warnings

**Perfect for:**
- First-time minor violations
- Borderline behavior that needs correction
- Educational opportunities
- Building a paper trail for repeat offenders

**How to Warn:**
```
/warn user:@Username hub:YourHubName reason:"Please keep discussions on-topic per hub rules"
```

### Warning Best Practices

**Effective Warning Messages:**
- Be specific about the violation
- Reference relevant rules
- Explain expected behavior
- Maintain a helpful tone

**Examples:**

**Good Warning:**
> "Please keep discussions focused on game development as outlined in our hub rules. Off-topic conversations can be moved to your server's general chat. Thanks for understanding!"

**Poor Warning:**
> "Stop being off-topic."

### Escalation Path

**Typical Progression:**
1. **Friendly Reminder** - Informal correction
2. **Formal Warning** - Official warning with documentation
3. **Temporary Blacklist** - Short removal from hub
4. **Extended Blacklist** - Longer removal
5. **Permanent Blacklist** - Permanent removal

## Content Filtering

### Automated Protection

Set up automated content filtering to catch issues before they become problems:

```
/hub config anti-swear hub:YourHubName
```

**Filter Actions:**
- **Block Message** - Prevent message from appearing
- **Blacklist User** - Automatically remove user (10 minutes)
- **Send Alert** - Notify moderators of violation

### Creating Effective Filters

**Pattern Types:**
- `word` - Exact match only
- `word*` - Matches words starting with "word"
- `*word` - Matches words ending with "word"
- `*word*` - Matches any word containing "word"

**Filter Strategy:**
1. Start with obvious violations
2. Monitor for new patterns
3. Adjust based on false positives
4. Regular review and updates

<Callout type="info">
  **Pro Tip:** Use the "Send Alert" action for borderline content that needs human review rather than automatic blocking.
</Callout>

## Building Your Moderation Team

### Recruiting Moderators

**Look for:**
- Active, respected community members
- Good judgment and communication skills
- Available during different time zones
- Experience with Discord moderation (helpful but not required)

**Recruitment Process:**
1. **Identify Candidates** - Watch for natural leaders
2. **Private Approach** - Discuss role privately first
3. **Trial Period** - Start with limited permissions
4. **Full Promotion** - Grant full moderator status after proving themselves

### Training New Moderators

**Essential Training Topics:**
- Hub rules and their interpretation
- Available moderation tools and commands
- Escalation procedures
- Documentation requirements
- Communication standards

**Training Resources:**
- Written moderation guidelines
- Shadowing experienced moderators
- Practice scenarios and role-playing
- Regular team meetings and updates

### Team Coordination

**Communication Channels:**
- Private moderator Discord server/channel
- Regular team meetings
- Shared documentation (Google Docs, Notion, etc.)
- Clear escalation procedures

**Documentation Standards:**
- Log all significant actions
- Note reasoning for decisions
- Track patterns and trends
- Share insights with the team

## Moderation Best Practices

### Consistency is Key

**Establish Clear Standards:**
- Written guidelines for common violations
- Consistent punishment scales
- Regular team discussions about edge cases
- Documentation of precedent decisions

**Regular Review:**
- Weekly team check-ins
- Monthly policy reviews
- Quarterly training updates
- Annual comprehensive evaluation

### Communication Excellence

**With Rule Violators:**
- Stay calm and professional
- Explain violations clearly
- Offer guidance for improvement
- Document all interactions

**With the Community:**
- Be visible and approachable
- Respond to questions promptly
- Explain decisions when appropriate
- Maintain transparency about policies

**With Your Team:**
- Share important information quickly
- Ask for help when needed
- Provide feedback and support
- Celebrate successes together

### Avoiding Burnout

**For Yourself:**
- Take regular breaks from moderation
- Don't take violations personally
- Seek support from team members
- Maintain perspective on the bigger picture

**For Your Team:**
- Rotate difficult assignments
- Provide emotional support
- Recognize good work publicly
- Ensure adequate team size

## Next Steps

<Callout type="success">
  **You're building a safer community!** Continue improving with these advanced topics:
</Callout>

- **[Content Filtering Setup](/docs/guides/content-filtering)** - Automate content moderation
- **[Activity Logging](/docs/guides/logging)** - Monitor hub health comprehensively
- **[Building Mod Teams](/docs/guides/moderation-team)** - Scale your moderation efforts
- **[Appeal Handling](/docs/guides/appeals)** - Manage blacklist appeals fairly

---

**Questions about moderation?** Join our [support community](https://discord.gg/cgYgC6YZyX) to connect with experienced moderators and get advice!
