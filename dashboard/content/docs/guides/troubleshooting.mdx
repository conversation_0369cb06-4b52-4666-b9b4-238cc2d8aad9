---
title: Troubleshooting Guide
description: Solve common InterChat issues quickly with step-by-step solutions, diagnostic tools, and expert tips.
icon: Wrench
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'
import { Steps } from 'fumadocs-ui/components/steps'

# Troubleshooting Guide

When things go wrong with InterChat, this guide helps you diagnose and fix issues quickly. We've organized solutions by symptoms to get you back up and running fast.

## Quick Diagnostic Checklist

Before diving into specific issues, run through this quick checklist:

<Steps>
  ### Check Bot Status

  1. Is InterChat online in your server?
  2. Can you run `/help` successfully?
  3. Are there any Discord outages? Check [Discord Status](https://discordstatus.com)

  ### Verify Permissions

  1. Does InterChat have "Manage Webhooks" permission?
  2. Can the bot see and send messages in your channel?
  3. Are there any channel-specific permission overrides?

  ### Test Basic Functionality

  1. Run `/connection list` to check connection status
  2. Try sending a test message in your connected channel
  3. Check if other servers in your hub are having similar issues
</Steps>

## Connection Issues

### Messages Not Sending to Other Servers

**Symptoms:**
- Your messages don't appear in other servers
- No error messages displayed
- Connection shows as active

<Tabs items={['Permission Problems', 'Webhook Issues', 'Hub Problems']}>
  <Tab>
    **Check Bot Permissions:**

    1. **Server Settings → Roles → InterChat**
       - ✅ Manage Webhooks
       - ✅ Send Messages
       - ✅ Read Messages
       - ✅ Embed Links

    2. **Channel-Specific Permissions:**
       - Right-click your channel → Edit Channel → Permissions
       - Check InterChat role has necessary permissions
       - Look for red X's that might override server permissions

    **Fix Steps:**
    ```
    1. Go to Server Settings → Roles
    2. Find InterChat role
    3. Enable required permissions
    4. Check channel overrides
    5. Test with a message
    ```

    **If permissions were changed:**
    ```
    /connection edit channel:#your-channel
    ```
    This refreshes the webhook with current permissions.
  </Tab>
  <Tab>
    **Webhook Troubleshooting:**

    **Common Webhook Problems:**
    - Webhook was manually deleted
    - Channel was deleted and recreated

    **Diagnostic Steps:**
    1. Check channel settings for webhooks
    2. Look for "InterChat" webhook
    3. Verify webhook is not disabled

    **Fix Steps:**
    ```
    /connection edit channel:#your-channel
    ```

    **If that doesn't work:**
    ```
    /disconnect
    /connect hub:YourHubName
    ```

    <Callout type="warning">
      **Note:** Disconnecting and reconnecting will create a new webhook but won't affect your hub membership.
    </Callout>
  </Tab>
  <Tab>
    **Hub-Related Issues:**

    **Check Hub Status:**
    1. Are other servers in the hub active?
    2. Has your server been blacklisted?
    3. Is the hub experiencing technical issues?

    **Diagnostic Commands:**
    ```
    /hub servers hub:YourHubName
    /blacklist list hub:YourHubName
    ```

    **Contact Hub Moderators:**
    - Ask in the hub if others are having issues
    - Contact hub owner or moderators directly
    - Check hub announcements for known issues

    **If Hub is Down:**
    - Wait for hub owner to resolve issues
    - Consider temporarily connecting to a backup hub
    - Monitor hub status through other connected servers
  </Tab>
</Tabs>

### Messages Not Receiving from Other Servers

**Symptoms:**
- You can send messages but don't see messages from other servers
- Other servers report your messages are visible
- Connection appears active

**Common Causes and Solutions:**

<Tabs items={['Connection Paused', 'Channel Issues', 'Filter Problems']}>
  <Tab>
    **Check Connection Status:**
    ```
    /connection list
    ```

    **If Connection is Paused:**
    ```
    /connection unpause channel:#your-channel
    ```

    **Why Connections Get Paused:**
    - Manual pausing for maintenance
    - Automatic pausing due to errors
    - Server administrator action
    - Bot restart or update

    **Prevention:**
    - Avoid manual pausing unless necessary
    - Monitor connection status regularly
    - Set up logging to track connection changes
  </Tab>
  <Tab>
    **Channel Configuration Issues:**

    **Check Channel Permissions:**
    - Can InterChat send messages in the channel?
    - Are there any message filters or bots interfering?
    - Is the channel set to slow mode or restricted?

    **Channel History:**
    - Was the channel recently created?
    - Did you change channel permissions recently?
    - Are there any Discord server boosts affecting features?

    **Fix Steps:**
    1. Verify bot can send messages manually
    2. Check for conflicting bots or filters
    3. Review recent permission changes
    4. Test with `/connection edit` to refresh
  </Tab>
  <Tab>
    **Content Filtering Issues:**

    **Hub Content Filters:**
    - Messages might be blocked by hub anti-swear filters
    - NSFW content detection might be active
    - Spam filters might be overly aggressive

    **Check Filter Settings:**
    ```
    /hub config settings hub:YourHubName
    /hub config anti-swear hub:YourHubName
    ```

    **Test with Simple Messages:**
    - Try sending "test" or "hello"
    - Avoid links, images, or complex formatting
    - Check if specific words are being filtered

    **Contact Hub Moderators:**
    - Report potential filter issues
    - Ask about recent filter changes
    - Request filter adjustment if needed
  </Tab>
</Tabs>

## Command Issues

### Commands Not Working

**Symptoms:**
- Slash commands don't appear or respond
- Error messages when trying to use commands
- Commands work in some channels but not others

**Troubleshooting Steps:**

<Steps>
  ### Check Command Availability

  1. Type `/` in your channel
  2. Look for InterChat commands in the list
  3. If no commands appear, the bot may not be properly added

  ### Verify Bot Integration

  1. Check if InterChat appears in your server's member list
  2. Verify the bot has "Use Slash Commands" permission
  3. Try re-inviting the bot if commands are missing

  ### Test in Different Channels

  1. Try commands in different channels
  2. Check if the issue is channel-specific
  3. Verify channel permissions for the bot

  ### Clear Discord Cache

  1. Restart your Discord client
  2. Clear Discord cache (Ctrl+Shift+I → Application → Storage → Clear)
  3. Try commands again after restart
</Steps>

### Permission Errors

**Common Permission Error Messages:**

<Tabs items={['Missing Permissions', 'Insufficient Role', 'Channel Restrictions']}>
  <Tab>
    **"You don't have permission to use this command"**

    **For Hub Commands:**
    - Check if you have the required hub role (Owner/Manager/Moderator)
    - Verify you're using the correct hub name
    - Confirm you're a member of the hub

    **For Server Commands:**
    - Ensure you have "Manage Server" permission
    - Check if you're an administrator or have the required role
    - Verify the bot recognizes your permissions

    **Fix Steps:**
    1. Check your roles in Server Settings
    2. Ask a server administrator to verify your permissions
    3. For hub commands, contact the hub owner
  </Tab>
  <Tab>
    **"You need [specific role] to use this command"**

    **Hub Role Requirements:**
    - Hub Owner: Full control over hub
    - Hub Manager: Most hub management commands
    - Hub Moderator: Basic moderation commands

    **Getting Hub Roles:**
    ```
    Contact hub owner or manager to request role:
    /hub moderator add hub:HubName user:@YourName position:moderator
    ```

    **Server Role Requirements:**
    - Server Administrator: Connection management
    - Manage Server: Most server-related commands
    - Manage Channels: Channel-specific commands
  </Tab>
  <Tab>
    **"Cannot use this command in this channel"**

    **Channel-Specific Restrictions:**
    - Some commands only work in connected channels
    - Others require specific channel types
    - Bot may lack permissions in the current channel

    **Solutions:**
    1. Try the command in a different channel
    2. Use the command in a connected channel if applicable
    3. Check channel permissions for the bot
    4. Ask an administrator to adjust channel settings
  </Tab>
</Tabs>

## Hub Management Issues

### Hub Creation Problems

**"Hub name already exists"**
- Hub names must be unique across all InterChat
- Try variations: "Gaming Hub 2", "Gaming Community", "Gamers United"
- Check for typos in your desired name
- Consider more specific names: "Minecraft Builders Hub"

**"Failed to create hub"**
- Check if you're on cooldown (10 minutes between attempts)
- Verify you have a stable internet connection
- Try again after a few minutes
- Contact support if the issue persists

### Moderation Issues

**Can't Add Moderators:**

<Steps>
  ### Check Your Permissions

  Only Hub Owners and Managers can add moderators.

  ### Verify User Information

  1. Make sure you're using the correct username or ID
  2. The user must be in a server connected to your hub
  3. Check spelling and capitalization

  ### Try Different Formats

  ```
  /hub moderator add hub:YourHub user:@Username position:moderator
  /hub moderator add hub:YourHub user:123456789012345678 position:moderator
  ```
</Steps>

**Moderation Commands Not Working:**
- Verify the user is actually in the hub
- Check if the user is already blacklisted
- Ensure you have the correct hub name
- Confirm the user ID is correct

## Performance Issues

### Slow Message Delivery

**Symptoms:**
- Messages take several seconds to appear
- Intermittent delays in cross-server communication
- Some messages arrive out of order

**Causes and Solutions:**

<Tabs items={['Discord API Limits', 'High Traffic', 'Network Issues']}>
  <Tab>
    **Discord Rate Limiting:**

    **What Happens:**
    - Discord limits how fast bots can send messages
    - High-traffic hubs may hit these limits
    - Results in queued messages and delays

    **Solutions:**
    - This is normal for very active hubs
    - Messages will eventually be delivered
    - Consider splitting very large hubs
    - Peak times may have longer delays

    **When to Worry:**
    - Delays longer than 30 seconds
    - Messages never arriving
    - Consistent issues across all times
  </Tab>
  <Tab>
    **Hub Traffic Management:**

    **High-Traffic Symptoms:**
    - Delays during peak hours
    - Faster delivery during quiet times
    - Some servers affected more than others

    **Management Strategies:**
    - Encourage quality over quantity in messages
    - Consider hub size limits (50-100 servers max)
    - Implement content guidelines to reduce spam
    - Monitor peak usage times

    **Hub Owner Actions:**
    - Enable spam filtering
    - Set clear communication guidelines
    - Consider creating specialized sub-hubs
  </Tab>
  <Tab>
    **Network and Server Issues:**

    **Check External Factors:**
    - Discord server status
    - Your internet connection stability
    - Server hosting issues (for self-hosted instances)

    **Diagnostic Steps:**
    1. Test with other Discord bots
    2. Check Discord status page
    3. Try from different devices/networks
    4. Monitor for patterns in delays

    **When to Contact Support:**
    - Consistent delays across all conditions
    - Complete message delivery failure
    - Issues affecting multiple hubs
  </Tab>
</Tabs>

## Getting Help

### Self-Service Resources

**Documentation:**
- [Essential Commands Guide](/docs/guides/commands)
- [Connection Management](/docs/guides/connections)
- [Moderation Tools](/docs/guides/moderation)
- [FAQ](/docs/faq)

**Diagnostic Tools:**
- `/help` - List all available commands
- `/connection list` - Check connection status
- `/hub servers` - View hub information
- Discord Status Page - Check for outages

### Community Support

**InterChat Support Server:**
- Join: [discord.gg/cgYgC6YZyX](https://discord.gg/cgYgC6YZyX)
- Get help from experienced users
- Report bugs and issues
- Suggest new features

**What to Include in Support Requests:**
1. **Clear description** of the problem
2. **Steps to reproduce** the issue
3. **Error messages** (exact text or screenshots)
4. **When it started** happening
5. **What you've already tried**

### Escalation Process

<Steps>
  ### Try Self-Service First

  - Check this troubleshooting guide
  - Review relevant documentation
  - Test basic functionality

  ### Community Support

  - Ask in the support server
  - Search for similar issues
  - Provide detailed information

  ### Direct Support

  - For critical issues affecting many users
  - Security concerns or abuse reports
  - Bug reports with clear reproduction steps

  ### Emergency Issues

  - Hub-wide outages
  - Security vulnerabilities
  - Abuse or harassment situations
</Steps>

## Prevention Tips

### Regular Maintenance

**Weekly Tasks:**
- Check connection status with `/connection list`
- Review hub activity and engagement
- Monitor moderation logs for issues
- Test basic functionality

**Monthly Tasks:**
- Review and update hub rules
- Audit moderator team and permissions
- Check for new InterChat features
- Gather community feedback

**Best Practices:**
- Keep bot permissions up to date
- Monitor Discord for policy changes
- Stay informed about InterChat updates
- Maintain good relationships with hub communities

---

**Still having issues?** Join our [support community](https://discord.gg/cgYgC6YZyX) for personalized help from our team and experienced users!
