---
title: Activity Logging & Monitoring
description: Set up comprehensive logging to monitor your hub's health, track moderation actions, and gain insights into community activity.
icon: FileText
---

import { Steps } from 'fumadocs-ui/components/steps'
import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Activity Logging & Monitoring

Comprehensive logging is essential for understanding your hub's health, tracking moderation effectiveness, and maintaining community safety. This guide covers setting up all log types and using them effectively.

## What You'll Learn
- How to configure all five log types
- Best practices for log channel organization
- Using logs for community insights and moderation
- Privacy considerations and data management
- Automated monitoring and alert strategies

## Understanding Log Types

InterChat provides six distinct log types, each serving specific monitoring needs:

<Tabs items={['Moderation Logs', 'Join/Leave Logs', 'Report Logs', 'Appeal Logs', 'Network Alerts', 'Message Moderation']}>
  <Tab>
    **Mod Logs** (`modLogs`)

    **What's Logged:**
    - User warnings and blacklists
    - Server blacklists and removals
    - Moderator role changes
    - Filter violations and actions
    - General moderation actions

    **Why Important:**
    - Track moderation team effectiveness
    - Ensure consistent enforcement
    - Document decisions for appeals
    - Identify problem patterns
    - Maintain accountability

    **Best Practices:**
    - Use a private channel accessible only to moderators
    - Review logs regularly for patterns
    - Document reasoning for major actions
    - Use for moderator training and feedback
  </Tab>
  <Tab>
    **Join/Leave Logs** (`joinLeaves`)

    **What's Logged:**
    - Servers joining the hub
    - Servers leaving or being disconnected
    - Connection status changes
    - Server information and timestamps

    **Why Important:**
    - Monitor hub growth and retention
    - Identify servers that frequently leave
    - Track connection stability
    - Understand community dynamics
    - Plan outreach and retention efforts

    **Best Practices:**
    - Use a channel visible to hub staff
    - Welcome new servers personally
    - Follow up on departures to understand reasons
    - Track growth trends over time
  </Tab>
  <Tab>
    **Report Logs** (`reports`)

    **What's Logged:**
    - User-submitted content reports
    - Report categories and reasons
    - Reported message content and context
    - Reporter information (anonymous to community)
    - Moderator responses and actions taken

    **Why Important:**
    - Essential for community safety
    - Enables rapid response to violations
    - Tracks community self-moderation
    - Identifies problem users and patterns
    - Required for public hub status

    **Best Practices:**
    - Respond to reports within 1 hour during active hours
    - Use a private channel for moderator team only
    - Set up role mentions for immediate notification
    - Document all report resolutions
  </Tab>
  <Tab>
    **Appeal Logs** (`appeals`)

    **What's Logged:**
    - Blacklist appeal submissions
    - Appeal status changes (pending, accepted, rejected)
    - Moderator decisions and reasoning
    - Appeal processing timestamps

    **Why Important:**
    - Ensure fair appeal processes
    - Track appeal success rates
    - Identify potential moderation issues
    - Maintain transparency in decisions
    - Build community trust

    **Best Practices:**
    - Process appeals within 24-48 hours
    - Provide clear reasoning for decisions
    - Use for moderator training on edge cases
    - Track patterns in successful appeals
  </Tab>
  <Tab>
    **Network Alerts** (`networkAlerts`)

    **What's Logged:**
    - System-wide notifications
    - Hub policy violations
    - Technical issues and outages
    - Security alerts and warnings
    - Important announcements from InterChat team

    **Why Important:**
    - Stay informed about system status
    - Receive critical security updates
    - Understand policy changes
    - Coordinate with other hub owners
    - Plan for maintenance and updates

    **Best Practices:**
    - Monitor regularly for important updates
    - Share relevant information with your team
    - Plan around announced maintenance
    - Report technical issues promptly
  </Tab>
  <Tab>
    **Message Moderation Logs** (`messageModeration`)

    **What's Logged:**
    - Messages deleted by moderators
    - Messages edited by moderators
    - Original and modified message content
    - Moderation timestamp and moderator
    - Server and channel information
    - Reason for action (if provided)

    **Why Important:**
    - Track all message-related moderation actions
    - Maintain accountability for content changes
    - Provide comprehensive audit trail
    - Monitor content policy enforcement
    - Document evidence for appeals and violations

    **Best Practices:**
    - Use a dedicated channel for message moderation
    - Review patterns for consistency
    - Document reasoning for major actions
    - Coordinate with general mod logs for context
  </Tab>
</Tabs>

## Setting Up Logging

<Steps>
  ### Access Logging Configuration

  ```
  /hub config logging hub:YourHubName
  ```

  This opens the logging configuration interface where you can set up all log types.

  ### Create Dedicated Log Channels

  **Recommended Channel Structure:**
  ```
  📁 Hub Management
    ├── #hub-reports (Private - Reports)
    ├── #hub-modlogs (Private - Mod Logs)
    ├── #hub-appeals (Private - Appeals)
    ├── #hub-activity (Staff - Join/Leaves)
    ├── #hub-alerts (Staff - Network Alerts)
    └── #hub-messages (Private - Message Moderation)
  ```

  **Channel Permissions:**
  - **Private channels:** Only moderators and above
  - **Staff channels:** Moderators, managers, and trusted staff
  - **Public channels:** Generally not recommended for logs

  ### Configure Each Log Type

  For each log type:
  1. Select the appropriate channel
  2. Configure role mentions (optional but recommended)
  3. Test the configuration
  4. Document the setup for your team

  ### Set Up Role Mentions

  **For Critical Logs (Reports):**
  - Create a `@Hub Moderators` role
  - Add all active moderators
  - Configure mentions for immediate notification

  **For Important Logs (Mod Logs, Appeals):**
  - Use manager-level roles for oversight
  - Consider time zone coverage
  - Balance notification frequency with alert fatigue
</Steps>

## Log Channel Organization

### Channel Naming Conventions

**Clear, Descriptive Names:**
- `#hub-reports` - User-submitted reports
- `#hub-modlogs` - Moderation actions
- `#hub-appeals` - Blacklist appeals
- `#hub-activity` - Server joins/leaves
- `#hub-alerts` - System notifications
- `#hub-messages` - Message moderation (deletions/edits)

**Alternative Naming Schemes:**
- By priority: `#critical-logs`, `#important-logs`, `#info-logs`
- By audience: `#mod-only`, `#staff-logs`, `#admin-alerts`
- By function: `#safety-logs`, `#growth-logs`, `#system-logs`

### Permission Management

<Tabs items={['Moderator Access', 'Manager Access', 'Owner Access']}>
  <Tab>
    **Hub Moderators Should Access:**
    - ✅ Report logs (essential for safety)
    - ✅ Mod logs (track team actions)
    - ✅ Appeal logs (handle appeals)
    - ⚠️ Join/leave logs (optional, for context)
    - ❌ Network alerts (usually not needed)

    **Permissions to Grant:**
    - View Channel
    - Read Message History
    - Send Messages (for responses)
    - Add Reactions (for acknowledgment)
  </Tab>
  <Tab>
    **Hub Managers Should Access:**
    - ✅ All log types
    - ✅ Full message history
    - ✅ Channel management permissions
    - ✅ Role mention configuration

    **Additional Responsibilities:**
    - Monitor log channel health
    - Ensure proper moderator access
    - Review logging effectiveness
    - Coordinate with hub owner on issues
  </Tab>
  <Tab>
    **Hub Owners Should Access:**
    - ✅ All log types with full permissions
    - ✅ Configuration and setup control
    - ✅ Analytics and trend analysis
    - ✅ Strategic decision making based on logs

    **Strategic Use:**
    - Long-term trend analysis
    - Policy and rule adjustments
    - Moderator team performance review
    - Hub growth and retention planning
  </Tab>
</Tabs>

## Using Logs Effectively

### Daily Monitoring Routine

**Morning Check (5 minutes):**
1. Review overnight reports - respond to urgent issues
2. Check for new servers joining/leaving
3. Scan mod logs for unusual activity
4. Review any network alerts

**Evening Review (10 minutes):**
1. Comprehensive report log review
2. Assess daily moderation effectiveness
3. Follow up on pending appeals
4. Plan next day priorities

### Weekly Analysis

**Growth and Retention:**
- Track server join/leave patterns
- Identify successful retention strategies
- Understand departure reasons
- Plan outreach and improvement efforts

**Moderation Effectiveness:**
- Review mod log patterns and trends
- Assess response times to reports
- Identify training needs for moderators
- Celebrate successful interventions

**Community Health:**
- Analyze report frequency and types
- Track repeat offenders and patterns
- Assess filter effectiveness
- Plan policy adjustments

### Monthly Strategic Review

**Data-Driven Decisions:**

<Tabs items={['Growth Analysis', 'Safety Metrics', 'Team Performance']}>
  <Tab>
    **Server Growth Tracking:**
    - New servers per month
    - Retention rate (servers staying 30+ days)
    - Peak activity periods
    - Geographic and demographic trends

    **Key Questions:**
    - What attracts servers to join?
    - Why do servers leave?
    - Which servers become most active?
    - How can we improve retention?

    **Action Items:**
    - Adjust recruitment strategies
    - Improve onboarding processes
    - Address common departure reasons
    - Celebrate growth milestones
  </Tab>
  <Tab>
    **Safety and Moderation Metrics:**
    - Reports per month and resolution time
    - Filter effectiveness and false positives
    - Appeal rates and success rates
    - Repeat violation patterns

    **Key Questions:**
    - Are we catching violations effectively?
    - Is our response time acceptable?
    - Are our policies clear and fair?
    - What new threats are emerging?

    **Action Items:**
    - Update content filters
    - Clarify rules and policies
    - Improve moderator training
    - Enhance safety measures
  </Tab>
  <Tab>
    **Moderation Team Analysis:**
    - Actions per moderator
    - Response time consistency
    - Decision quality and appeals
    - Team coordination effectiveness

    **Key Questions:**
    - Is workload distributed fairly?
    - Are decisions consistent across the team?
    - Do moderators need additional training?
    - How can we improve team coordination?

    **Action Items:**
    - Rebalance moderator responsibilities
    - Provide targeted training
    - Improve team communication
    - Recognize outstanding performance
  </Tab>
</Tabs>

## Privacy and Data Management

### Privacy Considerations

**What's Logged:**
- User IDs and usernames (not personal information)
- Message content for reported messages only
- Server information and connection data
- Moderation actions and timestamps

**What's NOT Logged:**
- Private conversations or DMs
- Messages not reported or flagged
- Personal information beyond Discord data
- Content from non-connected channels

**Data Retention:**
- Logs are stored according to Discord's data policies
- Hub owners can request log deletion
- Appeals and serious violations may be retained longer
- Regular cleanup of old, non-essential logs

### GDPR and Privacy Compliance

**User Rights:**
- Users can request information about their data
- Users can request deletion of their data
- Users can appeal moderation decisions
- Users can leave hubs at any time

**Hub Owner Responsibilities:**
- Use logs only for legitimate moderation purposes
- Protect log channel access appropriately
- Don't share logs outside your moderation team
- Respect user privacy and Discord ToS

## Advanced Monitoring Strategies

### Automated Monitoring

**Discord Bot Integration:**
- Set up bots to monitor log channels
- Create automated summaries and reports
- Alert on unusual patterns or spikes
- Generate weekly/monthly analytics

**External Tools:**
- Export log data for analysis
- Create dashboards and visualizations
- Set up alerting systems
- Integrate with other community tools

### Pattern Recognition

**Identifying Trends:**
- Seasonal activity patterns
- Time-of-day violation spikes
- Recurring problem users or servers
- Successful intervention strategies

**Predictive Insights:**
- Early warning signs of community issues
- Growth opportunity identification
- Resource planning and allocation
- Proactive policy adjustments

## Troubleshooting Log Issues

### Common Problems

**Logs Not Appearing:**
1. Check channel permissions for InterChat
2. Verify logging configuration is saved
3. Test with a known action (like a warning)
4. Contact support if issues persist

**Missing Role Mentions:**
1. Verify role exists and has mentionable permission
2. Check role hierarchy and permissions
3. Test mention configuration
4. Update role assignments if needed

**Channel Access Issues:**
1. Review channel permission overrides
2. Check role assignments for team members
3. Verify bot permissions in log channels
4. Update permissions as team changes

## Best Practices Summary

### Setup Best Practices

**Channel Organization:**
- Use clear, consistent naming
- Set appropriate permissions
- Document setup for your team
- Regular permission audits

**Configuration:**
- Enable all relevant log types
- Set up appropriate role mentions
- Test configurations thoroughly
- Document settings and changes

### Monitoring Best Practices

**Daily Operations:**
- Consistent monitoring schedule
- Rapid response to critical logs
- Clear escalation procedures
- Team coordination and communication

**Strategic Use:**
- Regular trend analysis
- Data-driven decision making
- Policy and procedure updates
- Continuous improvement focus

## Next Steps

<Callout type="success">
  **Your hub monitoring is comprehensive!** Continue optimizing with these advanced topics:
</Callout>

- **[Analytics and Insights](/docs/guides/analytics)** - Deep dive into community data
- **[Crisis Management](/docs/guides/crisis-response)** - Handle serious incidents
- **[Team Coordination](/docs/guides/team-management)** - Optimize moderator workflows
- **[Automation Tools](/docs/guides/automation)** - Streamline monitoring processes

---

**Need help with logging setup?** Join our [support community](https://discord.gg/cgYgC6YZyX) to get assistance from experienced hub administrators!
