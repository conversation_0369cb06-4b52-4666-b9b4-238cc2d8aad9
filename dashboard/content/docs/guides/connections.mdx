---
title: Mastering Connections
description: Learn everything about connecting channels to hubs, managing connections, and troubleshooting common issues.
icon: Link
---

import { Steps } from 'fumadocs-ui/components/steps'
import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Mastering Connections

Connections are the bridges that link your Discord channels to InterChat hubs. This guide covers everything from basic setup to advanced management and troubleshooting.

## What You'll Learn
- How connections work behind the scenes
- Best practices for choosing and setting up channels
- Advanced connection management techniques
- Troubleshooting common connection issues

## Understanding Connections

### How Connections Work

When you connect a channel to a hub:

1. **Webhook Creation** - InterChat creates a webhook in your channel
2. **Message Relay** - Messages sent in your channel are captured and sent to all other connected channels
3. **Cross-Server Display** - Messages from other servers appear in your channel with original usernames and avatars
4. **Real-Time Sync** - Everything happens instantly across all connected servers

<Callout type="info">
  **Technical Note:** InterChat uses Discord's webhook system to preserve message authenticity. This is why messages appear to come from the original authors rather than the bot.
</Callout>

### Connection Limitations

**One Hub Per Channel**
- Each channel can only connect to one hub at a time
- You can have multiple channels connected to different hubs

**One Connection Per Hub Per Server**
- Your server can only have one channel connected to each specific hub
- This prevents message loops and confusion

## Choosing the Right Channel

### Channel Selection Best Practices

<Tabs items={['Dedicated Channels', 'Existing Channels', 'Channel Types']}>
  <Tab>
    **Recommended Approach: Create Dedicated Channels**

    **Benefits:**
    - Clear purpose and expectations
    - Easier to moderate and manage
    - No interference with existing server culture
    - Better organization

    **Naming Suggestions:**
    - `#interchat`
    - `#hub-chat`
    - `#cross-server`
    - `#[hub-name]-chat`

    **Setup Tips:**
    - Place in a visible but not overwhelming location
    - Add a channel description explaining the purpose
    - Set appropriate permissions for your community
  </Tab>
  <Tab>
    **Using Existing Channels**

    **Good Candidates:**
    - Active but not overwhelming channels
    - Channels that match the hub's topic
    - Channels with engaged, respectful communities

    **Avoid These Channels:**
    - `#general` (usually too busy)
    - `#announcements` (one-way communication)
    - Private or restricted channels
    - Channels with frequent off-topic discussion

    **Preparation Steps:**
    - Announce the connection to your community
    - Explain what will change
    - Set expectations for cross-server interaction
  </Tab>
  <Tab>
    **Channel Type Considerations**

    **Text Channels** ✅
    - Standard choice for most hubs
    - Full feature support
    - Easy to moderate

    **Forum Channels** ❌
    - Not supported for connections
    - Use regular text channels instead

    **Thread Channels** ✅
    - Full feature support
    - Slightly harder to moderate

    **Voice Channels** ❌
    - Text connections only
    - Voice chat remains server-specific
  </Tab>
</Tabs>

## Setting Up Your First Connection

<Steps>
  ### Prepare Your Channel

  1. **Choose or create** your connection channel
  2. **Set permissions** appropriately for your community
  3. **Add a channel topic** explaining the connection (optional but helpful)

  Example channel topic:
  ```
  🌐 Connected to [Hub Name] - Chat with [X] other servers! Be respectful and follow hub rules.
  ```

  ### Find a Hub to Join

  **Option 1: Browse Public Hubs**

  Visit [interchat.tech/hubs](https://interchat.tech/hubs) to browse the hub directory where you can find public hubs by category.

  **Option 2: Join with Invite Code**
  ```
  /hub join invite:your-invite-code
  ```
  Use this if you have an invitation to a private hub.

  ### Connect Your Channel

  In your chosen channel, run:
  ```
  /connect hub:HubName
  ```

  Replace `HubName` with the exact name of the hub you want to join.

  ### Verify the Connection

  You should see:
  - A confirmation message from InterChat
  - A welcome message from the hub (if configured)
  - Your channel listed in `/connection list`
</Steps>

## Managing Your Connections

### Viewing All Connections

See all your server's connections at once:

```
/connection list
```

This shows:
- Which channels are connected
- Which hubs they're connected to
- Connection status (active/paused)
- Last activity timestamps

### Pausing and Resuming Connections

**Temporary Pause** (useful for maintenance or events):
```
/connection pause channel:#your-channel
```

**Resume Connection:**
```
/connection unpause channel:#your-channel
```

<Callout type="info">
  **When to Pause Connections:**
  - During server events that might spam the hub
  - When dealing with local server issues
  - For temporary maintenance or updates
  - During sensitive discussions that should stay local
</Callout>

### Editing Connection Settings

Modify connection behavior:
```
/connection edit channel:#your-channel
```

**Available Options:**
- **Change Connected Channel** - Move the connection to a different channel
- **Toggle Compact Mode** - Simplified message display
- **Update Webhook Settings** - Refresh the webhook if needed

### Disconnecting from a Hub

**Permanent Removal:**
```
/disconnect
```

<Callout type="warning">
  **Warning:** Disconnecting is permanent. You'll need to create a new connection to rejoin the hub.
</Callout>

## Advanced Connection Management

### Multiple Hub Strategy

**Organizing Multiple Connections:**

```
Server Layout Example:
📁 InterChat Hubs
  ├── #gaming-hub
  ├── #creative-hub
  └── #learning-hub
📁 Regular Channels
  ├── #general
  ├── #announcements
  └── #voice-chat
```

**Benefits:**
- Clear organization
- Easy to manage
- Members know what to expect
- Reduced confusion

### Connection Monitoring

**Regular Health Checks:**
1. Run `/connection list` weekly
2. Test message delivery occasionally
3. Monitor for webhook issues
4. Check for permission problems

**Signs of Connection Issues:**
- Messages not appearing in other servers
- Webhook errors in server logs
- Missing messages from other servers
- Connection showing as paused when it shouldn't be

## Troubleshooting Common Issues

### Messages Not Sending

<Tabs items={['Permission Issues', 'Webhook Problems', 'Hub Issues']}>
  <Tab>
    **Check Bot Permissions:**
    - Manage Webhooks ✅
    - Send Messages ✅
    - Read Messages ✅
    - Embed Links ✅

    **Check Channel Permissions:**
    - Bot can see the channel ✅
    - Bot can send messages ✅
    - Webhook permissions are intact ✅

    **Fix Steps:**
    1. Verify bot permissions in server settings
    2. Check channel-specific permission overrides
    3. Re-invite the bot if permissions were removed
  </Tab>
  <Tab>
    **Webhook Troubleshooting:**

    **Symptoms:**
    - Messages send but don't appear elsewhere
    - Error messages about webhooks
    - Connection appears active but doesn't work

    **Solutions:**
    1. Edit the connection to refresh webhook:
       ```
       /connection edit channel:#your-channel
       ```
    2. If that fails, disconnect and reconnect:
       ```
       /disconnect
       /connect hub:HubName
       ```
    3. Check if webhooks were manually deleted in channel settings
  </Tab>
  <Tab>
    **Hub-Related Issues:**

    **Check Hub Status:**
    - Is the hub still active?
    - Are you blacklisted from the hub?
    - Is the hub experiencing technical issues?

    **Diagnostic Steps:**
    1. Try connecting a different channel
    2. Ask other hub members if they're having issues
    3. Check hub announcements for known problems
    4. Contact hub moderators for assistance
  </Tab>
</Tabs>

### Messages Not Receiving

**Possible Causes:**
- Connection is paused
- Hub content filters are blocking messages
- Network connectivity issues
- Hub is experiencing problems

**Troubleshooting Steps:**
1. Check connection status: `/connection list`
2. Unpause if needed: `/connection unpause`
3. Test with a simple message
4. Check with hub moderators

### Performance Issues

**Slow Message Delivery:**
- Usually resolves automatically
- May indicate high Discord API load
- Check Discord status page for outages

**Missing Messages:**
- Rare but can happen during Discord outages
- Messages are not stored/replayed
- Normal service resumes when Discord recovers

## Best Practices for Success

### Community Preparation

**Before Connecting:**
1. **Announce the plan** to your community
2. **Explain the benefits** of cross-server chat
3. **Set expectations** for behavior
4. **Designate moderators** familiar with InterChat

**After Connecting:**
1. **Introduce your server** to the hub
2. **Encourage participation** from your members
3. **Monitor interactions** closely at first
4. **Gather feedback** from your community

### Ongoing Management

**Regular Tasks:**
- Monitor connection health weekly
- Review hub activity and engagement
- Update channel descriptions as needed
- Coordinate with hub moderators on issues

**Community Engagement:**
- Encourage cross-server friendships
- Participate in hub events and activities
- Share interesting content from your server
- Be a positive presence in the hub community

## Next Steps

<Callout type="success">
  **You're now a connection expert!** Here are some advanced topics to explore:
</Callout>

- **[Set up moderation tools](/docs/guides/moderation)** - Keep your connections safe
- **[Configure content filtering](/docs/guides/content-filtering)** - Automate content moderation
- **[Monitor with logging](/docs/guides/logging)** - Track connection activity
- **[Create your own hub](/docs/guides/creating-hubs)** - Build your own community network

---

**Having connection issues?** Join our [support community](https://discord.gg/cgYgC6YZyX) for real-time troubleshooting help!
