---
title: Hub Commands
description: Commands for creating and managing InterChat hubs
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Hub Commands

These commands allow you to create and manage InterChat hubs.

## Basic Hub Commands

### `/hub create`

Creates a new hub.

**Usage:** `/hub create`

**Process:**
1. Displays a modal where you enter:
   - Hub name
   - Description
   - Optional: Icon URL

**Permissions:** Everyone
**Cooldown:** 10 minutes

---

### `/hub edit`

Edits an existing hub's details, including description, icon, banner, and toggling locked/NSFW status.

**Usage:** `/hub edit hub:HubName`

**Options:**
- `hub` - The name of the hub to edit

**Process:**
1. Displays an interactive menu with the hub's current settings.
2. Provides direct buttons to toggle `Locked` and `NSFW` status.
3. Offers a select menu to edit `Description`, `Icon URL`, and `Banner URL` via modals.

**Permissions:** Hub Owner, Hub Manager

---

### `/hub delete`

Deletes a hub permanently.

**Usage:** `/hub delete hub:HubName`

**Options:**
- `hub` - The name of the hub to delete

**Permissions:** Hub Owner

<Callout type="warning">
  This action is permanent and cannot be undone. All connections will be removed.
</Callout>

---

### `/hub visibility`

Changes a hub's visibility between public and private.

**Usage:** `/hub visibility hub:HubName visibility:public|private`

**Options:**
- `hub` - The name of the hub
- `visibility` - Either "public" or "private"

**Permissions:** Hub Owner, Hub Manager

---

### `/hub servers`

Lists all servers connected to a hub.

**Usage:** `/hub servers hub:HubName`

**Options:**
- `hub` - The name of the hub

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

---

### `/hub transfer`

Transfers ownership of a hub to another user.

**Usage:** `/hub transfer hub:HubName user:@User`

**Options:**
- `hub` - The name of the hub
- `user` - The user to transfer ownership to

**Permissions:** Hub Owner

---

### `/hub announce`

Sends an announcement to all channels in a hub.

**Usage:** `/hub announce hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays a modal where you enter your announcement
2. Sends the announcement to all connected channels

**Permissions:** Hub Owner, Hub Manager, Hub Moderator
**Cooldown:** 1 minute

## Hub Configuration Commands

### `/hub config rules`

Sets or updates the rules for a hub.

**Usage:** `/hub config rules hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays a modal where you enter the hub rules
2. Rules will be shown to users when they join the hub

**Permissions:** Hub Owner, Hub Manager

---

### `/hub config logging`

Configures logging channels for a hub.

**Usage:** `/hub config logging hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays a menu to configure different log types:
   - Moderation logs (`modLogs`): Record all moderation actions
   - Join/leave logs (`joinLeaves`): Track servers joining or leaving the hub
   - Report logs (`reports`): Receive notifications when content is reported
   - Appeal logs (`appeals`): Track blacklist appeal requests
   - Network alert logs (`networkAlerts`): Receive important system notifications

**Permissions:** Hub Owner, Hub Manager

---

### `/hub config welcome`

Sets a welcome message for new servers joining the hub.

**Usage:** `/hub config welcome hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays a modal where you enter the welcome message
2. This message will be sent when a new server joins the hub

**Permissions:** Hub Owner, Hub Manager

---

### `/hub config settings`

Configures general settings for a hub.

**Usage:** `/hub config settings hub:HubName`

**Options:**
- `hub` - The name of the hub

**Settings:**
- Spam filter
- NSFW content filter
- Message reactions
- Anonymous mode

**Permissions:** Hub Owner, Hub Manager

---

### `/hub config anti-swear`

Configures word filters for a hub.

**Usage:** `/hub config anti-swear hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays options to add, remove, or list blocked words/patterns
2. Supports regex patterns for advanced filtering

**Permissions:** Hub Owner, Hub Manager

---

### `/hub config set-appeal-cooldown`

Sets the cooldown period for blacklist appeals.

**Usage:** `/hub config set-appeal-cooldown hub:HubName cooldown:duration`

**Options:**
- `hub` - The name of the hub
- `cooldown` - Duration (e.g., "7d" for 7 days)

**Permissions:** Hub Owner, Hub Manager

## Hub Moderator Commands

### `/hub moderator add`

Adds a moderator to a hub.

**Usage:** `/hub moderator add hub:HubName user:@User position:moderator|manager`

**Options:**
- `hub` - The name of the hub
- `user` - The user to add as moderator
- `position` - Either "moderator" or "manager"

**Permissions:** Hub Owner, Hub Manager

---

### `/hub moderator remove`

Removes a moderator from a hub.

**Usage:** `/hub moderator remove hub:HubName user:@User`

**Options:**
- `hub` - The name of the hub
- `user` - The moderator to remove

**Permissions:** Hub Owner, Hub Manager

---

### `/hub moderator edit`

Changes a moderator's position.

**Usage:** `/hub moderator edit hub:HubName user:@User position:moderator|manager`

**Options:**
- `hub` - The name of the hub
- `user` - The moderator to edit
- `position` - New position: "moderator" or "manager"

**Permissions:** Hub Owner, Hub Manager

---

### `/hub moderator list`

Lists all moderators in a hub.

**Usage:** `/hub moderator list hub:HubName`

**Options:**
- `hub` - The name of the hub

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

## Hub Invite Commands

### `/hub invite create`

Creates an invite link for a hub.

**Usage:** `/hub invite create hub:HubName uses:number expiry:duration`

**Options:**
- `hub` - The name of the hub
- `uses` - Maximum number of uses (optional)
- `expiry` - How long the invite is valid (optional)

**Permissions:** Hub Owner, Hub Manager

---

### `/hub invite revoke`

Revokes an existing hub invite.

**Usage:** `/hub invite revoke hub:HubName code:inviteCode`

**Options:**
- `hub` - The name of the hub
- `code` - The invite code to revoke

**Permissions:** Hub Owner, Hub Manager

---

### `/hub invite list`

Lists all active invites for a hub.

**Usage:** `/hub invite list hub:HubName`

**Options:**
- `hub` - The name of the hub

**Permissions:** Hub Owner, Hub Manager

## Hub Infractions Command

### `/hub infractions`

Views infractions in a hub.

**Usage:** `/hub infractions hub:HubName user:UserID`

**Options:**
- `hub` - The name of the hub
- `user` - get all infractions for a specific user (optional)

**Process:**
1. Displays a list of all infractions in the hub
2. Includes blacklists and warnings

**Permissions:** Hub Owner, Hub Manager, Hub Moderator
