---
title: Hubs
description: Create and manage community hubs with InterChat
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

Hubs are the central feature of InterChat, allowing you to create communities that span across multiple Discord servers.

<Cards>

  <Card title="Hub Visibility" href="/docs/features/hub-visibility">
    Control whether your hub is public or private
  </Card>
  <Card title="Hub Invites" href="/docs/features/hub-invites">
    Create and manage invite codes for your private hub
  </Card>
  <Card title="Settings" href="/docs/features/settings">
    Customize every aspect of your hub's configuration
  </Card>
  <Card title="Logging" href="/docs/features/logging">
    Detailed logs of all activities in your hub
  </Card>
</Cards>


## What is a Hub?

A hub is a virtual space that connects multiple Discord channels from different servers. When a message is sent in any connected channel, it's relayed to all other channels in the hub, creating a cross-server chat experience.

<Callout type="info">
  Think of a hub as a central meeting point where multiple communities can gather and interact.
</Callout>

## Hub Types

<Tabs items={['Public Hubs', 'Private Hubs']}>
  <Tab>
    Public hubs are discoverable through the hub browser on our website. Anyone can find and join these hubs, making them ideal for:

    - Community building
    - Topic-focused discussions
    - Finding like-minded communities

    To make your hub public, use the `/hub visibility` command and select "public".
  </Tab>
  <Tab>
    Private hubs are invitation-only. They won't appear in the hub browser, and servers can only join with an invite code.

    Private hubs are perfect for:

    - Partnerships between specific servers
    - Private communities
    - Collaborative projects

    All hubs are private by default. To keep your hub private, simply don't change its visibility.
  </Tab>
</Tabs>

## Hub Discovery

InterChat provides a hub browser on our website to help users discover and join public hubs.

### Finding Hubs

To browse available public hubs:

1. Visit [interchat.tech/hubs](https://interchat.tech/hubs) directly
2. Search for hubs by name, description, or tags
3. Filter hubs by category, size, or activity

### Hub Ratings and Reviews

The hub browser includes a rating and review system to help users find quality hubs:

#### Upvoting Hubs

Users can upvote hubs they enjoy, which helps increase their visibility in the hub browser. To upvote a hub:

1. Visit the hub's page on the website
2. Click the upvote button
3. Confirm your vote

You can upvote multiple hubs, but only once per hub.

#### Hub Reviews

Users can also leave detailed reviews for hubs they've joined:

1. Visit the hub's page on the website
2. Click "Write a Review"
3. Rate the hub (1-5 stars)
4. Write your review
5. Submit

Reviews help other users understand what to expect from a hub before joining.

<Callout type="info">
  Hub owners cannot delete reviews, but they can report inappropriate reviews for moderation.
</Callout>

## Creating a Hub

To create a hub:

1. Use the `/hub create` command
2. Fill in the required information:
   - Hub name (must be unique)
   - Description
   - Optional: Hub icon URL

Once created, you'll become the hub owner with full control over its settings.

## Managing a Hub

As a hub owner or manager, you have access to various management commands:

### Basic Management

- `/hub edit` - Edit hub details (name, description, icon)
- `/hub delete` - Delete the hub
- `/hub transfer` - Transfer ownership to another user
- `/hub visibility` - Change hub visibility (public/private)

### Moderation Management

- `/hub moderator add` - Add a moderator to your hub
- `/hub moderator remove` - Remove a moderator
- `/hub moderator list` - View all moderators
- `/hub servers` - View all servers connected to your hub

### Configuration

- `/hub config rules` - Set hub rules
- `/hub config logging` - Configure logging
- `/hub config welcome` - Set welcome messages
- `/hub config settings` - Adjust hub settings
- `/hub config anti-swear` - Configure word filters

<Callout type="warning">
  Deleting a hub is permanent and cannot be undone. All connections will be removed.
</Callout>

## Hub Invites

For private hubs, you'll need to create invite codes:

- `/hub invite create` - Create a new invite code
- `/hub invite revoke` - Revoke an existing invite
- `/hub invite list` - View all active invites

Invite codes can be shared with server administrators who want to join your hub.

## Hub Roles

Hubs have three levels of permissions:

1. **Owner** - Full control over the hub
2. **Manager** - Can manage most hub settings and moderators
3. **Moderator** - Can moderate messages and users

You can assign roles using the `/hub moderator add` command with the appropriate position parameter.
