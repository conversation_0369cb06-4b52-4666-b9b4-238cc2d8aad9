---
title: Welcome Messages
description: Welcome messages allow hub owners and managers to create custom greetings that are automatically sent when a new server joins their hub.
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Steps } from 'fumadocs-ui/components/steps'

## Understanding Welcome Messages

A well-crafted welcome message:

- Introduces new members to your hub's purpose and community
- Provides essential information about rules and expectations
- Creates a positive first impression
- Helps new servers feel included and valued

<Callout type="info">
  Welcome messages are sent automatically to new servers when they first connect a channel to your hub.
</Callout>

## Setting Up Welcome Messages

<Steps>
  ### Access welcome message configuration

  Use the command `/hub config welcome hub:YourHubName` to access the welcome message settings.

  ### Create your welcome message

  A modal will appear where you can write your custom welcome message.

  Your message can include:
  - Markdown formatting
  - Discord emoji
  - Up to 2000 characters

  ### Save your welcome message

  After writing your message, click "Submit" to save it.

  <Callout type="info">
    Preview your welcome message to ensure it appears as intended before saving.
  </Callout>
</Steps>

## Welcome Message Variables

You can use special variables in your welcome message that will be automatically replaced with the appropriate information:

- `{hubName}` - Your hub's name
- `{server<PERSON><PERSON>}` - The name of the server joining your hub
- `{user}` - The name of the user who connected the channel
- `{memberCount}` - The number of members in the server
- `{totalConnections}` - The total number of servers connected to your hub

Example:
```
Welcome to {hubName}! We have {totalConnections} servers connected.

- {user} connected this server with {memberCount} members.
- Please review our rules and have fun!

```

## Effective Welcome Messages

### Elements of a Good Welcome Message

An effective welcome message typically includes:

1. **A warm greeting**: Make new servers feel welcome
2. **Hub introduction**: Briefly explain what your hub is about
3. **Key rules**: Highlight the most important rules
4. **Resources**: Link to full rules or helpful resources
5. **Next steps**: Suggest how to get started participating

### Example Welcome Message

```
# Welcome to {hubName}! 🎉

We're thrilled to have {serverName} join our community!

## About Us
This hub is dedicated to [brief description of hub purpose].

## Quick Rules Reminder
• Be respectful to all members
• Keep content appropriate
• [Other key rules]

## Getting Started
Feel free to introduce yourselves and join the ongoing conversations.
For the full rules, type `/hub config rules` in your server.

If you need help, contact a moderator or use the `/help` command.

Happy chatting! 💬
```

## Updating Welcome Messages

You can update your welcome message at any time using the same command:

```
/hub config welcome hub:YourHubName
```

Changes to the welcome message will only affect new servers that join after the update. It won't be resent to servers that have already received a welcome message.

## Permissions

The following roles can configure welcome messages:

- Hub Owner
- Hub Manager

<Callout type="warning">
  Welcome messages are sent using the hub's identity, not your personal user account. They appear as official hub communications.
</Callout>
