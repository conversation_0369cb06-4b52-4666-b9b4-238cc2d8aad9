rules:
  header: 'Regras do InterChat'
  botRulesNote: 'Essas regras existem para criar uma experiência segura e agradável para todos. Leia e siga-as cuidadosamente:'
  rules: |
    1. **Proibido Discurso de Ódio ou Assédio**
    -# > **Inclui:** Usar insultos ou discurso de ódio para atacar outros, [e mais]({guidelines_link}).
    2. **Proibido Conteúdo Ilegal**
    -# > **Inclui:** Compartilhar links para conteúdo ilegal, Encorajar violência, [e mais]({guidelines_link}).
    3. **Proibido NSFW Severo ou Gore**
    -# > **Inclui:** Postar gore ou gore extremo no InterChat, Postar conteúdo sexual em hubs não-NSFW, [e mais]({guidelines_link}).
    4. **Proibido Spam ou Flooding**
    -# > **Inclui:** Spam em massa ou floods de bots, [e mais]({guidelines_link})
    5. **Proibida Personificação ou Fraude**
    -# > **Inclui:** Se passar por staff do InterChat ou moderadores de hub, Executar golpes de criptomoeda ou NFT, [e mais]({guidelines_link}).
    6. **Proibida Exploração ou Abuso**
    -# > **Inclui:** Comportamento predatório ou grooming com menores, Compartilhar, Solicitar, Fazer chantagem ou ameaçar, Encorajar autolesão, [e mais]({guidelines_link}).
    7. **Proibido Compartilhar Software Malicioso**
    -# > **Inclui:** Compartilhar malware, vírus, links de 'nitro grátis', scripts prejudiciais [e mais]({guidelines_link}).

    Você também concorda em seguir os [Termos de Serviço do Discord](https://discord.com/terms) e [Diretrizes da Comunidade](https://discord.com/guidelines). Confira a [lista completa de regras]({guidelines_link}).
  welcome: |
    {emoji} Olá, {user}! Bem-vindo ao InterChat! 🎉

    Estamos muito empolgados em tê-lo em nossa incrível comunidade de servidores conectados pelo Discord! Antes de começar a conversar com pessoas do mundo todo, vamos dar uma olhada rápida em nossas diretrizes amigáveis da comunidade.

    Essas regras simples ajudam a manter o InterChat um espaço acolhedor, caloroso e seguro para todos fazerem novos amigos e compartilharem ótimas conversas! ✨
  alreadyAccepted: "{emoji} Bem-vindo de volta, {user}! Você está pronto para explorar e conversar com comunidades incríveis. Divirta-se conectando-se com novos amigos! 🌟"
  continue: Continuar
  accept: Aceitar
  decline: Recusar
  agreementNote: Ao aceitar essas regras, você concorda em segui-las enquanto usar o InterChat. Quebrar essas regras pode resultar em restrições ou banimentos.
  hubAgreementNote: |
    Ao aceitar essas regras, você concorda em segui-las enquanto conversa neste hub. Quebrar essas regras pode resultar em remoção do hub.

    ⚠️ **Você não pode enviar mensagens neste hub até aceitar essas regras.**
  accepted: |
    {emoji} Fantástico! Bem-vindo à família InterChat! 🎉✨

    Agora você faz parte de uma comunidade incrível que conecta milhares de servidores e milhões de pessoas em todo o mundo! Estamos emocionados em tê-lo a bordo desta jornada incrível.

    ### 🚀 Pronto para Começar Sua Aventura?
    - **Descubra Comunidades:** Use o [diretório de hubs]({hubs_link}) para explorar comunidades vibrantes e ativas esperando por você
    - **Entre nas Conversas:** Participe de discussões que despertam seu interesse, ou crie seu próprio hub através do nosso [painel]({dashboard_link})
    - **Experimente Conexões Rápidas:** Use nosso comando `/call` para conexões instantâneas servidor-a-servidor - perfeito para fazer novos amigos!
    - **Obtenha Ajuda Personalizada:** Use `/setup` para um tour guiado se estiver se sentindo perdido

    ### 💝 Estamos Aqui Para Você!
    Se sentindo confuso ou precisa de ajuda? Não se preocupe - todos já passamos por isso! Junte-se à nossa [comunidade de suporte]({support_invite}) amigável onde pessoas reais estão empolgadas para ajudá-lo a começar.

    **Ama o que estamos fazendo?** Considere [nos apoiar]({donateLink}) para ajudar o InterChat a crescer e trazer ainda mais recursos incríveis para conectar comunidades em todo o mundo! 🌍
  declined: |
    {emoji} Por favor, dedique um momento para ler e aceitar as diretrizes da comunidade.

    Essas regras não são opcionais—elas estão aqui para manter o InterChat seguro e agradável para todos. Envie outra mensagem ou use qualquer comando do InterChat para começar.

    ⚠️ Importante: Você não poderá conversar com outros servidores até aceitar as regras. Tome seu tempo, mas este passo é obrigatório.
  hubAccepted: |
    {emoji} Você aceitou as regras do hub.
    Agora você pode começar a conversar neste hub!
  hubDeclined: |
    {emoji} Você recusou as regras de {hubName}.
    -# ⚠️ **Você não poderá enviar mensagens neste hub até aceitar suas regras.**
    -# Para tentar novamente, envie outra mensagem neste hub.
  noHubRules: Este hub ainda não definiu regras específicas. No entanto, as [regras gerais do InterChat]({rules_link}) ainda se aplicam.
  hubRules: Regras do Hub
  viewbotRules: 'Ver Regras do Bot'
vote:
  description: |
    Ajude mais comunidades a descobrir o InterChat! Seu voto no top.gg:
    - Ajuda outros a encontrar comunidades ativas
    - Desbloqueia recursos especiais para você
    - Apoia nosso desenvolvimento independente
  footer: 'Votos são renovados a cada 12 horas • Obrigado por apoiar o InterChat!'
  button:
    label: 'Votar no top.gg'
  perks:
    moreComingSoon: 'Mais vantagens em breve! Sugira algumas no [servidor de suporte]({support_invite}).'
  fields:
    currentStreak: 'Sequência Atual:'
    lastVote: 'Último Voto'
    voterPerks: 'Vantagens do Votante'
    voteNow: '[Vote Agora]({vote_url})!'
    perks:
      messageLength: 'Comprimento de mensagem aumentado (2000 caracteres)'
      stickers: 'Enviar stickers em hubs'
      createHubs: 'Criar até 4 hubs'
      welcomeMessages: 'Mensagens de boas-vindas personalizadas'
      voterRole: 'Cargo de votante no servidor de suporte'
      voterBadge: 'Distintivo exclusivo de votante no /profile'
  embed:
    title: 'Vote no InterChat'
network:
  accountTooNew: '{emoji} Olá {user}! Sua conta do Discord ainda é bem nova, então precisamos esperar um pouco antes de você poder enviar mensagens através do InterChat. Isso ajuda a manter nossa comunidade segura! Tente novamente em um pouco.'
  deleteSuccess: '{emoji} A mensagem de {user} foi excluída de __**{deleted}/{total}**__ servidores.'
  editInProgress: '{emoji} Sua solicitação foi enfileirada. As mensagens serão editadas em breve...'
  editInProgressError: '{emoji} Esta mensagem já está sendo editada por outro usuário.'
  emptyContent: '{emoji} O conteúdo da mensagem não pode estar vazio.'
  newMessageContent: 'Novo Conteúdo da Mensagem'
  editMessagePrompt: '{emoji} Por favor, use o modal para editar sua mensagem.'
  editSuccess: '{emoji} A mensagem de {user} foi editada em __**{edited}/{total}**__ servidores.'
  onboarding:
    welcome:
      title: '🎉 Welcome to InterChat!'
      description: |
        Welcome to InterChat! Let's get you set up with a personalized experience that matches your interests and helps you find the perfect communities to join.

        This quick setup will help us:
        - Learn about your interests and preferences
        - Find the best hubs for you to join
        - Connect you with like-minded people
        - Get you started with your first community

        Ready to begin your InterChat journey?
    embed:
      title: '🎉 Bem-vindo ao {hubName}!'
      description: |
        Congratulations! You've discovered an amazing, active community hub! 🌟

        Before you dive in and start making new friends, let's take a quick peek at our simple guidelines. They're designed to help everyone have the most fun and feel comfortable sharing their thoughts and experiences.

        Let's get started!
      footer: Rede InterChat | Conectando Comunidades Mundialmente 🌍
    inProgress: '{emoji} {channel} já está em processo de configuração para se juntar a um hub. Aguarde a conclusão da configuração ou cancele-a se você foi quem a iniciou.'
blacklist:
  description: 'Silenciar/Banir um usuário ou servidor do seu hub.'
  success: '{emoji} **{name}** foi colocado na lista negra com sucesso!'
  removed: '{emoji} **{name}** foi removido da lista negra!'
  modal:
    reason:
      label: Causa
      placeholder: Motivo da lista negra
    duration:
      label: Duração
      placeholder: 'Duração da lista negra. Ex.: 1d, 1s, 1m, 1a. Deixe em branco para permanente.'
  user:
    description: 'Silenciar/Banir um usuário do seu hub.'
    options:
      user:
        description: 'O ID do usuário para adicionar à lista negra (obtenha o id usando o comando /messageinfo)'
      hub:
        description: 'Hub para adicionar à lista negra'
    selectDuration: 'Selecione a duração da lista negra para {username}:'
    cannotBlacklistMod: '{emoji} Você não pode colocar um moderador na lista negra. Por favor, remova a função de moderador dele primeiro.'
    alreadyBlacklisted: '{emoji} Este usuário já está na lista negra.'
    easterEggs:
      blacklistBot: Você não pode me colocar na lista negra, wtf.
  server:
    description: 'Silenciar/Banir um servidor do seu hub.'
    options:
      server:
        description: 'O servidor para adicionar à lista negra'
      hub:
        description: 'Hub para adicionar à lista negra'
    selectDuration: 'Selecione a duração da lista negra para {serverName}:'
    alreadyBlacklisted: '{emoji}{emoji} Este servidor já está na lista negra.'
    unknownError: Falha ao colocar **{server}** na lista negra. Entre em contato com os desenvolvedores para obter mais informações.
  list:
    description: 'Listar todos os usuários e servidores na lista negra do seu hub.'
    user: |
      **UserID:** {id} 
      **Moderador:** {moderator} 
      **Motivo:** {reason} 
      **Expira:** {expires}
    server: |
      **ServerId:** {id}
      **Moderador:** {moderator}
      **Motivo:** {reason}
      **Expira:** {expires}
msgInfo:
  buttons:
    message: Informações da mensagem
    server: Informações do servidor
    user: Informações do usuário
    report: Reportar
  report:
    notEnabled: '{emoji} Os relatórios não estão habilitados para este hub.'
    success: '{emoji} Relatório enviado com sucesso. Obrigado!'
invite: |
  Obrigado por escolher convidar o InterChat! Se você tiver alguma dúvida ou precisar de ajuda, estamos sempre aqui para ajudar no servidor de suporte!

  **[{invite_emoji} `Link de convite`]( {invite} ) [{support_emoji}
connection:
  joinRequestsDisabled: '{emoji} As solicitações de adesão estão desabilitadas para este hub.'
  notFound: '{emoji} Conexão inválida. Verifique o ID do canal ou selecione entre as opções exibidas.'
  channelNotFound: '{emoji} Não é possível encontrar o canal conectado. Para falar novamente, escolha um novo canal.'
  alreadyConnected: '{emoji} O canal {channel} já está conectado a um hub.'
  switchChannel: '{emoji} O canal {channel} já está conectado a um hub:'
  switchCalled: '{emoji} Troca de canal chamada, use o comando novamente para visualizar a nova conexão.'
  switchSuccess: '{emoji} Canal trocado. Agora você está conectado de **{channel}**.'
  inviteRemoved: '{emoji} Convite do servidor removido para este hub.'
  setInviteError: '{emoji} Não foi possível criar convite. Conceda-me a permissão `Criar Convite` para o canal conectado.'
  inviteAdded: '{emoji} Invite Added. Others can now join this server by using `Apps > Message Info/Report` command and `/joinrequest` command.'
  emColorInvalid: '{emoji} Cor inválida. Certifique-se de ter inserido um código de cor hexadecimal válido.'
  emColorChange: '{emoji} Incorporar cor com sucesso {action}'
  embed:
    title: Detalhes da conexão
    fields:
      hub: Hub
      channel: Canal
      invite: Convite
      connected: Conectado
      emColor: Incorporar cor
      compact: Modo compacto
    footer: Use o menu suspenso abaixo para gerenciar sua conexão.
  selects:
    placeholder: '🛠️ Selecione uma opção para editar esta conexão'
  unpaused:
    desc: |
      ### {tick_emoji} Conexão não pausada

      Conexão não pausada para {channel}! Mensagens do hub começarão a chegar ao canal e você poderá enviar mensagens para o hub novamente.
    tips: |
      **💡 Tip:** Use {pause_cmd} to pause the connection or {edit_cmd} to set embed colors, toggle profanity filter, and more.
  paused:
    desc: |
      ### {clock_emoji} Conexão pausada Conexão pausada para {channel}! Mensagens do hub não chegarão mais ao canal e suas mensagens não serão transmitidas a eles.
    tips: |
      **💡 Dica:** Use {unpause_cmd} para retomar a conexão ou {leave_cmd} para parar permanentemente de receber mensagens.
hub:
  notFound: "{emoji} Hmm, não conseguimos encontrar esse hub. Verifique novamente o nome que você inseriu, ou tente navegar pelos hubs disponíveis com o [diretório de hubs]({hubs_link}) para descobrir algumas comunidades incríveis! 🔍"
  notFound_mod: '{emoji} Não foi possível encontrar o hub. Certifique-se de ter inserido o nome correto do hub e de que você é o proprietário ou moderador do hub.'
  notManager: '{emoji} Você deve ser um gerente de hub para executar esta ação.'
  notModerator: '{emoji} Você deve ser um gerente de hub para executar esta ação.'
  notPrivate: '{emoji} Este hub não é privado.'
  notOwner: '{emoji} Somente o proprietário deste hub pode executar esta ação.'
  alreadyJoined: '{emoji} Você já entrou no **{hub}** do {channel}!'
  invalidChannel: '{emoji} Canal inválido. Somente canais de texto e thread são suportados!'
  invalidImgurUrl: '{emoji} URL de imagem inválida para ícone ou banner. Certifique -se de que você inseriu um URL de imagem imgur válido que não é uma galeria ou álbum.'
  join:
    success: |
      🎉 **Fantástico! Bem-vindo ao {hub}!** 🎉

      Você conectou com sucesso {channel} a esta comunidade incrível! Agora você pode conversar com membros de servidores de todo o mundo diretamente deste canal. Que empolgante! ✨

      **🚀 Pronto para explorar?**
      - Use `/connection` para personalizar sua conexão e torná-la única
      - Use `/disconnect` se precisar deixar este hub
      - Use `/connection edit` para trocar para um canal diferente a qualquer momento

      **💝 Dica profissional:** Diga olá e se apresente - todos adoram conhecer novos amigos! Divirta-se conectando-se com a comunidade! 🌟
    nsfwChannelSfwHub: '{emoji} NSFW channels cannot connect to SFW hubs. {channel} is marked as NSFW, but **{hub}** is a safe-for-work hub. Please use a non-NSFW channel or find an NSFW hub instead.'
    sfwChannelNsfwHub: '{emoji} SFW channels cannot connect to NSFW hubs. {channel} is not marked as NSFW, but **{hub}** is an adult content hub. Please use an NSFW channel or find a SFW hub instead.'
  servers:
    total: 'Servidores conectados atualmente: {from}-{to} / **{total}**'
    noConnections: '{emoji} Nenhum servidor se juntou a este hub ainda. Use `/connect` para se juntar a este hub.'
    notConnected: "{emoji} Esse servidor não faz parte do **{hub}**."
    connectionInfo: |
      ServerID: {serverId}
      Canal: #{channelName} `({channelId})`
      Ingressou em: {joinedAt}
      Convidar: {invite}
      Conectado: {connected}
  blockwords:
    deleted: '{emoji} Regra anti-palavrões excluída com sucesso!'
    notFound: '{emoji} Regra anti-palavrões não encontrada.'
    maxRules: '{emoji} Você atingiu o número máximo de regras anti-palavrões (2) para este hub. Exclua uma regra antes de adicionar outra.'
    configure: 'Configurar ações para a regra: {rule}'
    actionsUpdated: '{emoji} Atualizadas as ações a serem tomadas pela regra. **Novas ações:** {actions}'
    selectRuleToEdit: Selecione uma regra para editar suas palavras/ações
    listDescription: |
      ### {emoji} Regras anti-palavrões
      Este hub tem {totalRules}/2 regras anti-palavrões configuradas.
    listFooter: Selecione uma regra usando o menu para visualizar todos os detalhes.
    ruleDescription: |
      ### {emoji} Regra de edição: {ruleName}
      {words}
    ruleFooter: 'Clique no botão abaixo para editar as palavras ou o nome da regra!'
    actionSelectPlaceholder: 'Selecione as ações que esta regra deve executar.'
    embedFields:
      noActions: '{emoji} **Nenhum!** Configure usando o menu abaixo.'
      actionsName: 'Ações configuradas:'
      actionsValue: '{actions}'
    modal:
      addRule: Adicionar regra anti-palavrões
      editingRule: Editando a regra anti-palavrões
      ruleNameLabel: Nome da regra
      wordsLabel: 'Palavras'
      wordsPlaceholder: 'Palavras separadas por vírgula (* = curinga). Ex: palavra1, *palavra2*, *palavra3, palavra4*'
    validating: '{emoji} Validando regra anti-palavrões...'
    noRules: |
      ### {emoji} Vamos configurar algumas regras anti-palavrão!
      Use o botão `Adicionar Regra` para criar uma.
  create:
    modal:
      title: Criar Hub
      name:
        label: Nome do Hub
        placeholder: Insira um nome para seu hub.
      description:
        label: Descrição
        placeholder: Insira uma descrição para seu hub.
      icon:
        label: URL do ícone
        placeholder: Insira uma URL de imagem do Imgur.
      banner:
        label: URL do banner
        placeholder: Insira uma URL de imagem do Imgur.
    maxHubs: '{emoji} [Vote no InterChat]({voteUrl}) para criar mais hubs! Você atingiu o número máximo de hubs ({maxHubs}) que pode criar.'
    invalidName: '{emoji} Nome de hub inválido. Não deve conter `discord`, `clyde` ou \`\`\` . Escolha outro nome.'
    nameTaken: '{emoji} Este nome de hub já foi usado. Por favor, escolha outro nome.'
    success: |
      ### Your __private__ hub, **{name}**, has been successfully created.
      To join, create an invite using `/hub invite create` and share the generated code. Then join using `/connect`.

      - **Edit hub:** `/hub edit`
      - **Generate invite:** `/hub invite create`
      - **Make public:** `/hub visibility`
      - **Join hub:** `/connect`
      - **Edit hub:** `/hub edit`
      - **Set report channel:** `/hub logging set_channe;`
      - **Add moderators:** `/hub moderator add`

      If you have any questions or need help, feel free to ask in the [support server]({support_invite}). Consider [donating]({donateLink}) to support the development costs.
  delete:
    confirm: Tem certeza de que deseja excluir **{hub}**? Esta ação é irreversível. Todos os servidores conectados, convites e dados de mensagens serão removidos deste hub.
    ownerOnly: '{emoji} Somente o proprietário deste hub pode excluí-lo.'
    success: '{emoji} O hub **{hub}** foi excluído.'
    cancelled: '{emoji} A exclusão do Hub foi cancelada.'
  invite:
    create:
      success: |
        ### Convite criado!

        Seu convite foi criado com sucesso. Outros agora podem participar deste hub usando o comando `/connect`.

        - **Participar usando:** `/connect invite:{inviteCode}`
        - **Visualizar convites:** `/`hub lista de convites`
        - **Expiração:** {expiração}
        - **Usos**: ∞.
    revoke:
      invalidCode: '{emoji} Código de convite inválido. Certifique-se de ter inserido um código de convite válido.'
      success: '{emoji} Convite {inviteCode} revogado.'
    list:
      title: '**Códigos de convite:**'
      noInvites: '**Códigos de convite:**'
      notPrivate: '{emoji} Somente hubs privados podem ter convites. Use `/hub edit` para tornar este hub privado.'
  joined:
    noJoinedHubs: '{emoji} This server has not joined any hubs yet. Use [hub directory]({hubs_link}) to view a list of hubs.'
    joinedHubs: Este servidor faz parte de **{total}** hub(s). Use `/disconnect` para sair de um hub.
  leave:
    noHub: '{emoji} Esse canal é inválido ou não se juntou a nenhum hub.'
    confirm: Tem certeza de que deseja sair de **{hub}** de {channel}? Nenhuma outra mensagem será enviada para este servidor deste hub.
    confirmFooter: Confirme usando o botão abaixo em até 10 segundos.
    success: '{emoji} Saiu do hub de {channel}. Nenhuma outra mensagem será enviada para este servidor deste hub. Você pode entrar novamente usando `/connect`.'
  moderator:
    noModerators: '{emoji} Este hub ainda não tem moderadores. Use `/hub moderator add` para adicionar um.'
    add:
      success: '{emoji} **{user}** foi adicionado como moderador da posição **{position}**.'
      alreadyModerator: '{emoji} **{user}** já é um moderador.'
    remove:
      success: '{emoji} **{user}** foi removido como moderador.'
      notModerator: '{emoji} **{user}** não é um moderador.'
      notOwner: '{emoji} Somente o proprietário deste hub pode remover um gerente.'
    update:
      success: "{emoji} A posição de **{user}** foi atualizada para **{position}**."
      notModerator: '{emoji} **{user}** não é um moderador.'
      notAllowed: "{emoji} Somente os gerentes do hub podem atualizar a posição de um moderador."
      notOwner: "{emoji} Somente o proprietário deste hub pode atualizar a posição de um gerente."
  manage:
    dashboardTip: "**🛠️ NEW Dashboard:** Improved interface and more features! Try it out at [your hub's dashboard page]({url})."
    enterImgurUrl: Insira uma URL de imagem Imgur válida que não seja uma galeria ou álbum.
    icon:
      changed: Ícone do Hub alterado com sucesso.
      modal:
        title: Ícone de edição
        label: URL do ícone
      selects:
        label: Ícone de edição
        description: Alterar o ícone deste hub.
    description:
      changed: '{emoji} Somente os gerentes do hub podem atualizar a posição de um moderador.'
      modal:
        title: Editar Descrição
        label: Descrição
        placeholder: Insira uma descrição para este hub.
      selects:
        label: Alterar descrição
        description: Alterar a descrição deste hub.
    banner:
      changed: O banner do hub foi alterado com sucesso.
      removed: O banner do Hub foi removido com sucesso.
      modal:
        title: Editar Banner
        label: URL do banner
      selects:
        label: Editar Banner
        description: Alterar o banner deste hub.
    visibility:
      success: '{emoji} A visibilidade do Hub foi alterada com sucesso para **{visibility}**.'
      selects:
        label: Alterar Visibilidade
        description: Torne este hub público ou privado.
    toggleLock:
      selects:
        label: 'Bloquear/Desbloquear Hub'
        description: 'Bloquear ou desbloquear os chats do hub'
      confirmation: 'Os chats do Hub agora são {status}.'
      announcementTitle: 'Os chats do Hub agora são {status}.'
      announcementDescription:
        locked: 'Somente moderadores podem enviar mensagens.'
        unlocked: 'Todos podem enviar mensagens.'
    toggleNsfw:
      modal:
        title: 'Toggle NSFW Status'
        label: 'NSFW Status'
        description: 'Mark this hub as containing adult content.'
      selects:
        label: 'Toggle NSFW Status'
        description: 'Mark hub as NSFW or SFW content'
      confirmation: 'Hub content rating is now {status}.'
      announcementTitle: 'Hub content rating changed to {status}'
      announcementDescription:
        nsfw: 'This hub now contains adult content. Only NSFW Discord channels can connect to this hub.'
        sfw: 'This hub is now safe for work. All channels can connect to this hub.'
    setNsfw:
      success: '{emoji} **{hub}** has been successfully marked as **{status}**.'
      announcement: "{emoji} **Hub Content Rating Changed**\n\nThis hub is now marked as **{status}**.\n\n{description}"
    nsfwAlreadySet: '{emoji} **{hub}** is already marked as **{status}**.'
    embed:
      visibility: 'Visibilidade'
      connections: 'Conexões'
      chatsLocked: 'Bate-papos bloqueados'
      blacklists: 'Listas negras'
      total: 'Total'
      users: 'Usuários'
      servers: 'Servidores'
      hubStats: 'Estatísticas do Hub'
      moderators: 'Moderadores'
      owner: 'Dono'
    logs:
      title: Configuração de Logs
      reset: '{emoji} A configuração de logs para logs `{type}` foi redefinida com sucesso.'
      roleSuccess: '{emoji} Os logs do tipo `{type}` agora mencionarão {role}!'
      roleRemoved: '{emoji} Logs do tipo `{type}` não mencionarão mais uma função.'
      channelSuccess: '{emoji} Logs of type `{type}` will be sent to  {channel} from now!'
      channelSelect: '#️⃣ Selecione um canal para enviar os logs'
      roleSelect: 'Selecione a função a ser mencionada quando um log for acionado.'
      reportChannelFirst: '{emoji} Por favor, defina um canal de log primeiro.'
      config:
        title: Configurar logs `{type}`
        description: |
          {arrow} Selecione um canal de log e/ou função para ser pingado no menu suspenso abaixo.
          {arrow} Você também pode desabilitar o log usando o botão abaixo.
        fields:
          channel: Canal
          role: Menção de função
      reports:
        label: Relatórios
        description: Receba relatórios dos usuários.
      modLogs:
        label: Registros de Mods
        description: Ações de moderação de log. (por exemplo, lista negra, exclusões de mensagens, etc.)
      joinLeaves:
        label: Entrar/Sair
        description: Registre quando um servidor entra ou sai deste hub.
      appeals:
        label: Apelações
        description: Receba apelações de usuários/servidores na lista negra.
      networkAlerts:
        label: Alertas de rede
        description: Receba alertas sobre mensagens bloqueadas automaticamente.
      messageModeration:
        label: Message Moderation
        description: Log message deletions and edits by moderators.
      messageDeletions:
        label: Message Deletions
        description: Log when messages are deleted from the hub.
      messageEdits:
        label: Message Edits
        description: Log when messages are edited in the hub.
  transfer:
    invalidUser: '.{emoji} O usuário especificado não foi encontrado.'
    selfTransfer: '{emoji} Você não pode transferir a propriedade para si mesmo.'
    botUser: '{emoji} Você não pode transferir a propriedade para um bot.'
    confirm: 'Tem certeza de que deseja transferir a propriedade de **{hub}** para {newOwner}? Você será rebaixado para a função de gerente.'
    cancelled: '{emoji} A transferência do Hub foi cancelada.'
    error: '{emoji} Ocorreu um erro ao transferir a propriedade do hub.'
    success: '{emoji} Propriedade de **{hub}** transferida com sucesso para {newOwner}. Você foi adicionado como gerente.'
    timeout: '{emoji} Hub transfer has timed out.'
  rules:
    noRules: "{emoji} This hub has no rules configured yet. Let's add some!"
    list: "### {emoji} Regras do Hub\n{regras"
    maxRulesReached: '{emoji} Número máximo de regras ({max}) atingido.'
    ruleExists: 'e{moji} Esta regra já existe.'
    selectedRule: 'Regra selecionada {número}'
    modal:
      add:
        title: Adicionar Regra de Hub
        label: Texto da regra
        placeholder: Insira o texto da regra (máx. 1000 caracteres
      edit:
        title: Editar Regra do Hub
        label: Texto da regra
        placeholder: Insira o novo texto da regra (máx. 1000 caracteres)
    select:
      placeholder: Selecione uma regra para editar ou remover
      option:
        label: Regra {number}
    buttons:
      add: Adicionar regra
      edit: Editar regra
      delete: Excluir regra
      back: Voltar
    success:
      add: '{emoji} Regra adicionada com sucesso!'
      edit: '{emoji} Regra atualizada com sucesso!'
      delete: '{emoji} Regra excluída com sucesso!'
    view:
      title: 'Regra {número}'
      select: Selecione uma ação para esta regra
  welcome:
    set: '{emoji} Mensagem de boas-vindas atualizada com sucesso{emoji} Mensagem de boas-vindas atualizada com sucesso!'
    removed: '{emoji} Mensagem de boas-vindas removida.'
    voterOnly: '{emoji} Mensagens de boas-vindas personalizadas são um privilégio exclusivo para eleitores! Vote para desbloquear este recurso.'
    placeholder: |
      Bem-vindo {user} de {serverName} ao {hubName}! 🎉
      Membros: {memberCount}, Hub: {totalConnections}!
report:
  modal:
    title: Detalhes do relatório
    other:
      label: Detalhes do relatório
      placeholder: Uma descrição detalhada do relatório.
    bug:
      input1:
        label: Detalhes do bug
        placeholder: 'Por exemplo: Falhas frequentes de interação para o comando /help...'
      input2:
        label: Descrição detalhada (opcional)
        placeholder: 'Passos que você deu. Por exemplo: 1. Execute /help 2. Aguarde 5 segundos...'
  reasons:
    spam: Spam ou mensagens excessivas
    advertising: Publicidade indesejada ou autopromoção
    nsfw: Conteúdo NSFW ou inapropriado
    harassment: Assédio ou intimidação
    hate_speech: Discurso de ódio ou discriminação
    scam: Golpe, fraude ou tentativa de phishing
    illegal: Conteúdo ou atividades ilegais
    personal_info: Compartilhamento de informações pessoais/privadas
    impersonation: Representando outros
    breaks_hub_rules: Viola as regras do hub
    trolling: Trolling ou interrupção intencional
    misinformation: Informações falsas ou enganosas
    gore_violence: Sangue ou violência extrema
    raid_organizing: Organizar ataques ou incursões
    underage: Usuário ou conteúdo menor de idade
  dropdown:
    placeholder: Selecione um motivo para o seu relatório
  submitted: '{emoji} Relatório enviado com sucesso. Junte-se ao {support_command} para obter mais detalhes. Obrigado!'
  errors:
    noReasonSelected: '{emoji} No reason selected. Please try again.'
    hubNotFound: '{emoji} Hub not found. Please try again.'
  description: 'Report a message'
  options:
    message:
      description: 'The message to report'
  contextMenu:
    name: 'Report Message'
  selectReason: '{emoji} Please select a reason for your report:'
  bug:
    title: Relatório de erro
    affected: Componentes afetados
    description: . Selecione com qual componente do bot você está tendo problemas.
language:
  set: Idioma definido! Agora responderei a você em **{lang}**. 🇧🇷🇵🇹.
errors:
  messageNotSentOrExpired: '{emoji} Esta mensagem não foi enviada em um hub, expirou ou você não tem permissão para executar esta ação.'
  notYourAction: "{emoji} Desculpe, você não pode executar esta ação. Por favor, execute o comando você mesmo."
  notMessageAuthor: '{emoji} Você não é o autor desta mensagem.'
  commandError: |
    {emoji} Oops! Something unexpected happened while running this command. Don't worry - this isn't your fault!

    We've automatically logged this issue and our team will take a look. If this keeps happening, please drop by our friendly [support server]({support_invite}) and let us know about this error ID - we're always happy to help! 🤗

    **Error ID:**
    ```{errorId}```
  mustVote: Por favor, [vote](https://top.gg/bot/769921109209907241/vote) para que o InterChat use este comando, seu apoio é muito apreciado!
  inviteLinks: '{emoji} Você não pode enviar links de convite para este hub. Defina um convite em `/connection` em vez disso! Os moderadores do hub podem configurar isso usando `/hub edit settings`'
  invalidLangCode: '{emoji} Código de idioma inválido. Verifique se você inseriu um [código de idioma](https://cloud.google.com/translate/docs/languages) correto.'
  modalError: '{emoji} There was an error showing the modal. Please try again.'
  unknownServer: '{emoji} Servidor desconhecido. Certifique-se de ter inserido o **ID do servidor** correto.'
  unknownNetworkMessage: '{emoji} Mensagem desconhecida. Se foi enviada no último minuto, aguarde mais alguns segundos e tente novamente.'
  userNotFound: '.{emoji} Usuário não encontrado. Tente inserir o ID dele.'
  blacklisted: '.{emoji} Usuário não encontrado. Tente inserir o ID dele.'
  userBlacklisted: '.'
  serverBlacklisted: '{emoji} Este servidor está na lista negra deste hub.'
  serverNotBlacklisted: '{emoji} O servidor inserido não está na lista negra.'
  userNotBlacklisted: '{emoji} O usuário inserido não está na lista negra.'
  missingPermissions: '{emoji} Você não tem as seguintes permissões para executar esta ação: **{permissions}**'
  botMissingPermissions: '{emoji} Por favor, conceda-me as seguintes permissões para continuar: **{permissions}**'
  unknown: '{emoji} Ocorreu um erro desconhecido. Tente novamente mais tarde ou entre em contato conosco entrando em nosso [servidor de suporte]({support_invite}).'
  notUsable: '{emoji} Isso não pode mais ser usado.'
  cooldown: '{emoji} Você está em cooldown. Aguarde até **{time}** antes de tentar novamente.'
  serverNameInappropriate: '{emoji} O nome do seu servidor contém palavras inapropriadas. Por favor, altere-o antes de entrar no hub.'
  banned: |
    {emoji} Você foi banido do uso do InterChat por violar nossas [diretrizes](https://interchat.tech/guidelines).
    Se você acha que um recurso é aplicável, crie um tíquete no [servidor de suporte]{support_invite} ).
  commandLoadingError: 'There was an error loading the command. Please try again later.'
  errorLoadingHubs: 'Error Loading Hubs'
  errorShowingHubSelection: 'There was an error showing the hub selection screen. Please try again.'
  connectNotFound: 'Connect command not found. Please try again.'
config:
  setInvite:
    success: |
      ### {emoji} Conjunto de links de convite
      - O convite do seu servidor será usado quando as pessoas usarem `/joinserver`.
      - Ele será exibido em `/leaderboard server`.
    removed: '{emoji} Invite removed successfully!{emoji} Convite removido com sucesso!'
    invalid: '{emoji} Convite inválido. Certifique-se de ter inserido um link de convite válido. Por exemplo, `https://discord.gg/discord`'
    notFromServer: '{emoji} Este convite não é deste servidor'
badges:
  shown: '{emoji} Seus emblemas agora serão exibidos nas mensagens'
  hidden: '{emoji} Seus emblemas agora ficarão ocultos nas mensagens.'
  command:
    description: ' Configure suas preferências de exibição de crachá'
    options:
      show:
        name: 'mostrar'
        description: 'Se deseja mostrar ou ocultar seus emblemas nas mensagens'
  list:
    developer: 'Desenvolvedor principal do InterChat'
    staff: 'Membro da equipe InterChat'
    translator: 'Tradutor do InterChat'
    voter: 'Votou no InterChat nas últimas 12 horas'
global:
  webhookNoLongerExists: '{emoji} O webhook para este canal não existe mais. Para continuar usando o InterChat, recrie o webhook usando `/connection unpause`.'
  noReason: Nenhuma razão fornecida.
  noDesc: Sem descrição.
  version: InterChat v{version}
  loading: '{emoji} Aguarde enquanto processo sua solicitação...'
  reportOptionMoved: '{emoji} Esta opção foi movida! Para reportar uma mensagem aos moderadores do hub, use o comando atualizado `Apps > Message Info/Report`. Para reportar diretamente à equipe do InterChat, basta entrar no [servidor de suporte]((support_invite}) e crie um tíquete com comprovante.'
  private: 'Privado'
  public: 'Publico'
  yes: 'Sim'
  no: 'Não'
  cancelled: '{emoji} Cancelled. No changes were made.'
  #Common button labels
  buttons:
    openInbox: 'Open Inbox'
    modPanel: 'Mod Panel'
    joinServer: 'Join Server'
    disconnect: 'Disconnect'
    reconnect: 'Reconnect'
    editRule: 'Edit Rule'
    #Setup buttons
    joinPopularHub: 'Join Popular Hub'
    createNewHub: 'Create New Hub'
    finishSetup: 'Finish Setup'
    findMoreHubs: 'Find More Hubs'
    supportServer: 'Support Server'
    viewChannel: 'View Channel'
    hubDirectory: 'Hub Directory'
    learnMore: 'Learn More'
    connectToHub: 'Connect to a Hub'
    #Common buttons for calls and other features
    createYourHub: 'Create Your Hub'
    #Call buttons
    cancelCall: 'Cancel Call'
    newCall: 'New Call'
    #Leaderboard
    userLeaderboard: 'User Leaderboard'
    serverLeaderboard: 'Server Leaderboard'
  #Common modal titles and labels
  modals:
    editConnection:
      title: 'Edit Connection'
      channelName:
        label: 'Channel Name'
        placeholder: 'Enter a custom name for this channel'
      profanityFilter:
        label: 'Profanity Filter'
      compact:
        label: 'Compact Mode'
    hubCreation:
      title: 'Create New Hub'
      name:
        label: 'Hub Name'
        placeholder: 'e.g., Gaming Community, Art Gallery'
      description:
        label: 'Description'
        placeholder: 'What is this hub about?'
    messageInfo:
      title: 'Message Information'
    editRule:
      title: 'Edit Rule'
      content:
        label: 'Rule Content'
        placeholder: 'Enter the rule content...'
  #Common messages and responses
  messages:
    selectChannel: 'Select a channel'
    selectHub: 'Select a hub'
    noHubsAvailable: 'No hubs available'
    hubNotFound: 'Hub not found'
    channelNotFound: 'Channel not found'
    connectionNotFound: 'Connection not found'
    invalidSelection: 'Invalid selection'
    operationCancelled: 'Operation cancelled'
    setupComplete: 'Setup complete!'
    connectionEstablished: 'Connection established'
    connectionRemoved: 'Connection removed'
    settingsUpdated: 'Settings updated'
    ruleAdded: 'Rule added'
    ruleUpdated: 'Rule updated'
    ruleDeleted: 'Rule deleted'
    #Leaderboard messages
    noDataAvailable: 'No data available'
    loadingData: 'Loading data...'
    #Connection status
    connected: 'Connected'
    disconnected: 'Disconnected'
    paused: 'Paused'
    #Hub visibility
    publicHub: 'Public Hub'
    privateHub: 'Private Hub'
#Leaderboard
leaderboard:
  title: 'Global Message Leaderboard'
  description: 'Resets every month. Send a message in any hub to get on it!'
warn:
  description: 'Warn a user in your hub'
  options:
    user:
      description: 'The user to warn'
    hub:
      description: 'The hub to warn in'
    reason:
      description: 'Reason for the warning'
  errors:
    cannotWarnSelf: '{emoji} You cannot warn yourself.'
  modal:
    title: Avisar usuário
    reason:
      label: Razão
      placeholder: Insira o motivo do aviso a este usuário...
  success: |
    {emoji} **{name}** foi avisado com sucesso.

    -# Eles serão notificados do aviso mais recente na próxima vez que enviarem uma mensagem no hub. Evite emitir vários avisos de uma vez.
  dm:
    title: '{emoji} Notificação de Aviso'
    description: 'Você foi avisado no hub **{hubName}**'
  log:
    title: '{emoji} Usuário avisado'
    description: |
      {arrow} **Usuário:** {user} ({userId})
      {arrow} **Moderador:** {moderator} ({modId})
      {arrow} **Motivo:** {reason}
    footer: 'Avisado por: {moderador}'
calls:
  connected:
    title: "You're Connected! 🎉"
    description: "You've been matched with another awesome server! Say hello and start chatting - this is where the magic happens! ✨"
    instructions: 'Use `/hangup` to end the call • `/skip` to find a different server'
    serverInfo: '**Connected to:** {serverName} ({memberCount} members)'
    duration: '**Call Duration:** {duration}'
    messages: '**Messages Exchanged:** {count}'
  waiting:
    title: 'Finding Your Perfect Match'
    description: 'Added to call queue. Waiting for another server to join...'
  failed:
    title: 'Call Failed'
    description: "Check you're not already in a call and try again in a few moments."
    reasons:
      alreadyInCall: 'This channel is already in a call!'
      alreadyInQueue: 'This channel is already in the call queue!'
      webhookFailed: 'Failed to create webhook. Please try again later.'
      channelInvalid: 'Cannot skip call - invalid channel'
  cancelled:
    title: 'Call Cancelled'
    description: 'Call queue exited. Use `/call` to start a new call.'
    queueExit: 'You have been removed from the call queue'
  ended:
    title: 'Call Ended'
    description: 'Thanks for chatting! Hope you made some new friends! 🌟'
    stats: |
      **Call Summary:**
      • Duration: {duration}
      • Messages: {messages}
      • Server: {serverName}
    ratePrompt: 'How was your call experience?'
  skip:
    title: 'Finding New Call'
    description: 'Previous call ended • Waiting for another server • Use `/hangup` to cancel'
    newConnected:
      title: 'New Call Connected!'
      description: "You've been connected to a different server • Use `/hangup` to end"
    error: 'Unable to skip call. Please try again.'
  hangup:
    confirm: 'Are you sure you want to end this call?'
    success: 'Call ended successfully. Thanks for chatting!'
    queueOnly: 'Removed from call queue.'
  buttons:
    endCall: 'End Call'
    skipServer: 'Skip Server'
    skipAgain: 'Skip Again'
    cancelCall: 'Cancel Call'
    newCall: 'New Call'
    exploreHubs: 'Explore Hubs'
    browseAllHubs: 'Browse All Hubs'
    ratePositive: 'Good Call 👍'
    rateNegative: 'Poor Call 👎'
    reportCall: 'Report Call'
  hubs:
    promotion:
      title: '🌟 Discover InterChat Hubs!'
      description: 'Calls are in beta. For a more reliable experience, try InterChat Hubs - our main feature for connecting servers!'
    benefits:
      title: 'Why Choose Hubs?'
      description: 'Hubs offer a more reliable and feature-rich experience than calls:'
      list: |
        • **Persistent Connections** - Messages stay even when you're offline
        • **Multiple Communities** - Join various themed hubs or create your own
        • **Advanced Moderation** - Content filtering, anti-spam, and more
        • **Rich Features** - Custom welcome messages, rules, and settings
        • **Active Communities** - Thousands of servers already connected
    main:
      title: 'InterChat Hubs'
      description: 'Hubs are the main feature of InterChat, connecting servers in persistent chat communities'
  system:
    callStart: |
      {emoji} **Your Call is Connected!** Say hello! 🎉
      > - Say hello and start chatting with the other server!
      > - Use `/hangup` when you're ready to end the call
      > - Remember to keep things friendly and follow our [community guidelines]({guidelines})
  rating:
    success: 'Thanks for rating! Your **{type}** feedback has been recorded for {count} participant{plural}.'
    alreadyRated: '{emoji} You have already rated this call.'
    invalidButton: '{emoji} Invalid rating button. Please try again.'
    noCallData: '{emoji} Unable to find call data. The call might have ended too long ago.'
    noParticipants: '{emoji} Unable to find participants from the other channel.'
  report:
    prompt: '{emoji} Please select a reason for your report:'
    invalidButton: '{emoji} Invalid report button. Please try again.'
  leaderboard:
    title: 'Global Calls Leaderboard'
    description: 'Shows data from this month'
    noData: 'No data available.'
    userTab: 'User Leaderboard'
    serverTab: 'Server Leaderboard'
  errors:
    guildOnly: 'This command can only be used in a server text channel.'
#Command descriptions and help text
commands:
  about:
    title: 'About InterChat'
    description: 'Learn more about InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    support_text: 'Need help? Join our support server for assistance!'
    features:
      title: 'Features'
      list: |
        - Connect with other servers for active cross-server discussions
        - Messages flow naturally between servers in real-time
        - Build engaged topic-focused communities
        - Moderation tools to keep discussions healthy
        - Visual dashboard to manage your hubs, servers, and settings
    buttons:
      vote: 'Vote on top.gg'
      invite: 'Invite InterChat'
      dashboard: 'Visit Dashboard'
      support: 'Join Support Server'
      credits: 'View Credits'
    credits:
      title: 'Credits'
      developers: 'Developers'
      staff: 'Staff'
      translators: 'Translators'
      mentions: 'Special Mentions'
      mascot: 'Mascot'
      top_voter: 'Top Voter'
      footer: 'Version {version}'
    sections:
      invite: 'Invite InterChat to your server'
      dashboard: 'Visit the InterChat dashboard'
      support: 'Join our support server'
      credits: 'View the InterChat credits'
    errors:
      serverOnly: 'This command can only be used in a server.'
  help:
    description: '📚 Explore InterChat commands with our new help system'
    options:
      command:
        description: 'The command to get info about'
      category:
        description: 'View commands by category'
    errors:
      categoryNotFound: '{emoji} Category not found.'
      commandNotFound: '{emoji} Command `{command}` not found.'
      showingCategory: '{emoji} An error occurred while showing the category.'
      showingCommand: '{emoji} An error occurred while showing the command help.'
      showingMenu: '{emoji} An error occurred while showing the help menu.'
      showingSearch: '{emoji} An error occurred while showing the search interface.'
  setup:
    description: 'Setup InterChat in your server'
    errors:
      serverOnly: 'This command can only be used in a server.'
      missingPermissions: |
        I need the following permissions to work properly:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please give me these permissions and try again!
        Need help? [Join our support server]({supportInvite})
      setupError: 'There was an error starting the setup process. Please try again later.'
      completionError: 'There was an error completing the setup. Please try again or contact support if the issue persists.'
      channelNotSelected: 'No channel was selected. Please try again.'
      invalidChannelType: 'Please select a text or thread channel. Voice channels, forums, and other channel types are not supported.'
      missingChannelPermissions: |
        I need the following permissions in {channel}:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please update the channel permissions and try again!
      channelAlreadyConnected: 'This channel is already connected to the hub "{hubName}". Please select a different channel.'
      channelNotFound: 'Selected channel no longer exists. Please run the setup command again.'
      hubNotFound: 'This hub no longer exists. Please choose another one.'
      commandLoadingError: 'Failed to load commands. Please try again or join our support server for help.'
      interactionError: '{emoji} Oops! Something went a bit wonky there. No worries though - just give it another try! If this keeps happening, our friendly support team is always here to help.'
      userMismatch: 'This setup is for another user.'
      serverRequired: 'You must be in a server to use this.'
      timeout: "No worries! The setup just timed out while waiting for your response. When you're ready to continue your InterChat journey, just run `/setup` again and we'll pick up right where we left off!"
      noAvailableHubs: 'Your server is already connected to all available popular hubs! Try creating a new hub instead.'
      hubCreationFailed: 'Failed to create hub. Please try again.'
      validationError: 'Invalid hub data provided. Please try again.'
    welcome:
      title: '🎉 Welcome to InterChat Setup!'
      description: |
        Hey! Let's get your server connected to InterChat.

        This setup will guide you through everything you need:

        📍 Select a channel for the chat

        🏠 Join or create a hub (your community space)

        ⚙️ Finish setup to start chatting

        What's a Hub? It's a shared space where servers connect and chat together. Simple as that.

        Let's get this set up and running. 🚀
    channelSelection:
      title: '📍 Step 1: Choose Your Perfect Channel'
      description: "Let's pick the channel where all the exciting InterChat conversations will happen! This can be any text channel in your server - maybe create a special one just for this?"
      placeholder: 'Select a channel'
      tips:
        title: '💡 Helpful Tips for Success'
        content: |
          - **Create something special:** Try naming it `#interchat`, `#global-chat`, or `#world-chat`
          - **Think about visibility:** Make sure members who want to join the fun can see this channel
          - **Room to grow:** You can always connect more channels to different communities later
          - **Keep it organized:** A dedicated channel helps keep conversations flowing smoothly
    hubChoice:
      title: 'InterChat Setup (2/4)'
      description: "Great! Messages will appear in {channel}. Now, let's connect to a hub!"
      whatIsHub:
        title: 'What is a Hub?'
        description: "A hub is InterChat's main feature - a shared chat space where multiple servers can talk together. Hubs are persistent communities that stay connected 24/7, unlike temporary calls."
      popularHubs:
        title: 'Popular Hubs'
        description: |
          - Join thriving active communities with thousands of users
          - Start chatting immediately with other servers
          - Perfect for new users to experience InterChat
          - No additional setup required - just connect and chat!
      createHub:
        title: 'Create Your Own Hub'
        description: |
          - Start your own community themed around your interests
          - Full control over settings, moderation, and features
          - Invite specific servers to create a private network
          - Set custom rules, welcome messages, and more
      note: 'You can always join more hubs later with the `/connect` command!'
    hubSelection:
      title: 'InterChat Setup (2/4)'
      description: 'Choose a hub to join from our most active communities:'
      placeholder: 'Choose a hub to join'
      tip: '**Tip:** You can always join more hubs later using `/connect` and [the hub list](https://interchat.app/hubs).'
    hubCreation:
      modal:
        title: 'Create New Hub'
        name:
          label: 'Hub Name'
          placeholder: 'e.g., Gaming Community, Art Gallery'
        description:
          label: 'Description'
          placeholder: 'What is this hub about?'
    nextSteps:
      created:
        title: '✨ Almost Done!'
        description: "Your Hub \"{hubName}\" is Ready!\nClick Finish Setup to complete the process. After that, follow these steps:"
        inviteLink:
          title: '1️⃣ Create an Invite Link'
          description: "{hubInviteCommand} `hub:{hubName}`\nThis will generate an invite link you can share with other servers"
        shareHub:
          title: '2️⃣ Share Your Hub'
          description: |
            Share the invite link with at least one other server to start chatting!
            {dot} Send to your friends & servers
            {dot} Share in our [support server]({supportInvite})
        configuration:
          title: '3️⃣ Essential Configuration'
          description: |
            {hubRulesCommand}
            Create hub rules and guidelines

            {hubLoggingCommand}
            Set up logging channels for hub events

            {hubAntiSwearCommand}
            Configure word filters and auto-moderation

            {hubSettingsCommand}
            Manage message types and notifications
        proTips:
          title: '💡 Pro Tips'
          description: |
            {dot} Your hub is private by default - only servers with invites can join
            {dot} Vote for InterChat to unlock custom welcome messages and colors
            {dot} You can publish your hub to the [hub directory]({website}/hubs) using {hubVisibilityCommand}
            {dot} Join our [support server]({supportInvite}) for hub management tips!
        copyCommand: "`/hub invite create hub:{hubName}`\n✨ Command copied! Run this to create an invite link."
      joined:
        title: '✨ Ready to Join?'
        description: "Ready to Join \"{hubName}\"?\nClick Finish Setup to join the hub. After joining, you can use these commands:"
        commands: |
          {connectionEditCommand}
          Customize how you receive/send messages to the hub

          {connectionListCommand}
          View all your connected hubs

          {website}/hubs (New :sparkles:)
          Join more hubs
        help: 'Join our [support server]({supportInvite}) if you have questions!'
    completion:
      title: 'Setup Complete!'
      description: 'Your server has been successfully connected to the hub in {channel}. You can now start chatting!'
    buttons:
      supportServer: 'Support Server'
      documentation: 'Documentation'
      goBack: 'Go Back'
      finishSetup: 'Finish Setup'
      hubDirectory: 'Hub Directory'
      learnMore: 'Learn More'
      viewChannel: 'View Channel'
      joinPopularHub: 'Join Popular Hub'
      createNewHub: 'Create New Hub'
      copyInviteCommand: 'Copy Invite Command'
      findMoreHubs: 'Find More Hubs'
    existingConnections:
      title: 'Existing Connections'
      description: |
        Your server is already connected to the following hubs:

        {connectionList}

        You can continue to add more connections if you'd like.
  language:
    description: '🈂️ Set the language in which I should respond to you'
    options:
      lang:
        description: 'The language to set'
  badges:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  tutorial:
    description: '📚 Learn how to use InterChat with interactive tutorials'
    subcommands:
      start:
        description: 'Start a specific tutorial'
        options:
          tutorial:
            description: 'The tutorial to start'
      setup:
        description: 'Start the server setup tutorial (for admins)'
  rules:
    description: '📋 Sends the network rules for InterChat.'
    options:
      hub:
        description: 'View rules for a specific hub'
    hubRules:
      title: '{hubName} Rules'
      description: 'The following rules apply to this hub'
    botRules:
      title: 'InterChat Rules'
      description: 'Bot-wide rules for all users'
#Tutorial system
tutorial:
  errors:
    notFound: '{emoji} Tutorial not found.'
    noSteps: '{emoji} This tutorial has no steps.'
    prerequisitesRequired: '{emoji} You need to complete the prerequisite tutorials first.'
    noInProgress: '{emoji} You have no tutorials in progress.'
  completion:
    completed: '{emoji} Tutorial completed! Great job!'
    nextRecommendation: 'Next recommended tutorial: {tutorialName}'
  categories:
    newUser: 'New User Tutorials'
    admin: 'Server Admin Tutorials'
    moderator: 'Moderator Tutorials'
    all: 'General Tutorials'
  list:
    title: 'Available Tutorials'
    noTutorials: 'No tutorials available at the moment.'
    description: 'Choose a tutorial to get started with InterChat'
  progress:
    completed: '✅ Completed'
    inProgress: '▶️ In Progress'
    notStarted: '⭕ Not Started'
  buttons:
    start: 'Start Tutorial'
    resume: 'Resume'
    review: 'Review'
    next: 'Next'
    previous: 'Previous'
    skip: 'Skip'
    finish: 'Finish'
  about:
    description: '🚀 Learn how InterChat helps grow Discord communities'
    title: 'About InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    features:
      title: 'What makes InterChat different:'
      list: |
        - Built for real communities - Designed with Discord server owners' needs in mind
        - Active hubs - Find and join thriving communities around shared interests
        - Privacy first - Full control over your hub's connections and settings
        - Smart moderation - AI-powered image filtering and advanced content filtering keeps discussions healthy
        - Visual dashboard - Manage your hubs, servers, and settings through our web interface
    sections:
      invite: 'Invite InterChat to your server:'
      dashboard: 'Visit the InterChat dashboard:'
      support: 'Join our support server:'
      credits: 'Check out the InterChat team!'
    buttons:
      invite: 'Invite'
      dashboard: 'Dashboard'
      support: 'Support Server'
      credits: 'Credits & Team'
      vote: 'Vote!'
    support_text: 'InterChat is completely free to use. If you like InterChat, consider supporting us on Ko-fi! Or even a vote on top.gg helps us a lot!'
    credits:
      title: 'CREDITS'
      developers: 'Developers:'
      staff: 'Staff: ([Check Applications!]({website}/apply))'
      translators: 'Translators:'
      mentions: 'Deserving Mentions:'
      mascot: '(maker of our cute mascot chipi {emoji})'
      top_voter: '([top voter]({vote_url}) of all time {emoji})'
      footer: 'InterChat v{version} • Made with ❤️ by the InterChat Team'
#Hub configuration and management
hubConfig:
  antiSwear:
    title: 'Anti-Swear Configuration'
    description: 'Configure word filters and auto-moderation for this hub'
    noRules: "Let's set up some anti-swear rules!\nUse the `Add Rule` button to create one."
    selectRule: "Select a rule to edit it's words/actions"
    placeholder: 'Select a log type to configure'
    validating: '{emoji} Validating anti-swear rule...'
    buttons:
      addRule: 'Add Rule'
      editRule: 'Edit Rule'
      deleteRule: 'Delete Rule'
      back: 'Back'
    modal:
      addRule: 'Add Anti-Swear Rule'
      editRule: 'Editing Anti-Swear Rule'
      ruleName: 'Rule Name'
      words: 'Words'
      wordsPlaceholder: 'Words seperated by comma. (Use * for wildcard). Eg. word1, *word2*, *word3, word4*'
  logging:
    title: 'Logs Configuration'
    description: 'Configure logging channels and notifications for hub events'
    placeholder: 'Select a log type to configure'
    channelSelect: '#️⃣ Select a channel to send the logs'
    roleSelect: '🏓 Select the role to mention when a log is triggered.'
    config:
      title: 'Configure `{type}` Logs'
      description: |
        {arrow} Select a log channel and/or role to be pinged from the dropdown below.
        {arrow} You can also disable logging by using the button below.
      fields:
        channel: 'Channel'
        role: 'Role Mention'
    types:
      reports:
        label: 'Reports'
        description: 'Receive reports from users.'
      modLogs:
        label: 'Mod Logs'
        description: 'Log Moderation actions. (eg. blacklist, message deletes, etc.)'
      joinLeaves:
        label: 'Join/Leave'
        description: 'Log when a server joins or leaves this hub.'
      appeals:
        label: 'Appeals'
        description: 'Recieve appeals from blacklisted users/servers.'
      networkAlerts:
        label: 'Network Alerts'
        description: 'Recieve alerts about automatically blocked messages.'
      messageModeration:
        label: 'Message Moderation'
        description: 'Log message deletions and edits by moderators.'
  rules:
    title: 'Hub Rules Configuration'
    description: 'Manage rules and guidelines for your hub'
    noRules: "This hub has no rules configured yet. Let's add some!"
    maxRulesReached: 'Maximum number of rules ({max}) reached.'
    ruleExists: 'This rule already exists.'
    placeholder: 'Select a rule to edit or remove'
    modal:
      add:
        title: 'Add Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the rule text (max 1000 characters)'
      edit:
        title: 'Edit Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the new rule text (max 1000 characters)'
    buttons:
      add: 'Add Rule'
      edit: 'Edit Rule'
      delete: 'Delete Rule'
      back: 'Back'
    view:
      title: 'Rule {number}'
      select: 'Select an action for this rule'
  appealCooldown:
    errors:
      invalidCooldown: 'Please provide a valid cooldown duration.'
      tooShort: 'Cooldown must be atleast **1 hour** long.'
      tooLong: 'Cooldown cannot be longer than **1 year**.'
    success: '{emoji} Appeal cooldown has been set to **{hours}** hour(s).'
#Interaction and modal text
interactions:
  modals:
    warn:
      title: 'Warn User'
      reason:
        label: 'Reason'
        placeholder: 'Enter the reason for warning this user...'
  buttons:
    refresh: 'Refresh'
    cancel: 'Cancel'
    confirm: 'Confirm'
    back: 'Back'
    next: 'Next'
    finish: 'Finish'
    save: 'Save'
    delete: 'Delete'
    edit: 'Edit'
    add: 'Add'
    remove: 'Remove'
    view: 'View'
    close: 'Close'
  placeholders:
    selectOption: 'Select an option'
    selectChannel: 'Select a channel'
    selectRole: 'Select a role'
    selectUser: 'Select a user'
    selectServer: 'Select a server'
    enterText: 'Enter text here...'
    enterReason: 'Enter a reason...'
    enterDescription: 'Enter a description...'
#Moderation panel
modPanel:
  buttons:
    serverBanned: 'Server Banned'
    banServer: 'Ban Server'
  modals:
    blacklistUser: 'Blacklist User'
    blacklistServer: 'Blacklist Server'
#General UI text
ui:
  titles:
    error: 'Error'
    warning: 'Warning'
    success: 'Success'
    info: 'Information'
    confirmation: 'Confirmation'
  messages:
    loading: 'Loading...'
    processing: 'Processing your request...'
    pleaseWait: 'Please wait...'
    tryAgain: 'Please try again.'
    contactSupport: 'Please contact support if this issue persists.'
    operationCancelled: 'Operation cancelled.'
    operationCompleted: 'Operation completed successfully.'
    noDataAvailable: 'No data available.'
    permissionDenied: 'Permission denied.'
    invalidInput: 'Invalid input provided.'
    timeout: 'Operation timed out.'
    notFound: 'Not found.'
    alreadyExists: 'Already exists.'
    unavailable: 'Currently unavailable.'
#Message management commands
deleteMsg:
  description: 'Delete a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to delete'
  contextMenu:
    name: 'Delete Message'
  processing: '{emoji} Your request has been queued. Messages will be deleted shortly...'
  alreadyDeleted: '{emoji} This message is already deleted or is being deleted by another moderator.'
editMsg:
  description: 'Edit a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to edit'
    newContent:
      description: 'The new content for the message'
  contextMenu:
    name: 'Edit Message'
  modal:
    title: 'Edit Message'
    content:
      label: 'New Content'
      placeholder: 'Enter the new message content...'
  processing: '{emoji} Your request has been queued. Messages will be edited shortly...'
  alreadyEdited: '{emoji} This message is already being edited by another moderator.'
inbox:
  description: 'Check your inbox for latest important updates & announcements'
  title: '📬 InterChat Inbox'
  subtitle:
    new: 'Latest announcements and updates'
    older: 'Viewing older announcements'
  empty:
    title: '📬 All caught up!'
    description: "I'll let you know when there's more. But for now, there's only Chipi here: {emoji}"
  buttons:
    viewOlder: 'View Older'
    previous: 'Previous'
    next: 'Next'
  postedOn: 'Posted on {date}'
joinserver:
  description: 'Join a server or send a request to join a server through InterChat.'
  options:
    servername:
      description: 'The name of the server you want to join'
    messageorserverid:
      description: 'The message ID or server ID'
  errors:
    channelOnly: 'This command can only be used in a channel.'
    missingTarget: 'You must provide a message ID or server ID'
  success:
    inviteSent: "{emoji} I have DM'd you the invite link to the server!"
  request:
    title: 'Join Request'
    description: 'You requested to join the server `{serverName}` through InterChat. Here is the invite link:'
    broadcast: 'User `{username}` from `{guildName}` has requested to join this server. Do you want to accept them?'
  buttons:
    accept: 'Accept'
    reject: 'Reject'
  response:
    sent: "{emoji} Your request has been sent to the server. You will be DM'd the invite link if accepted."
    creating: '{emoji} This server does not have an invite link yet. Creating one...'
    dmSent: '{emoji} The invite link has been sent to the user.'
    dmFailed: '{emoji} The invite link could not be sent to the user. They may have DMs disabled.'
  status:
    accepted: 'Accepted by {username}'
    rejected: 'Rejected by {username}'
messageInfo:
  description: 'Get information about a message.'
  options:
    message:
      description: 'The message to get information about.'
  contextMenu:
    name: 'Message Info'
  errors:
    profileFetch: 'Failed to fetch user profile.'
connect:
  description: '🔗 Connect your channel to an InterChat hub'
  options:
    channel:
      description: 'The channel you want to connect to a hub'
    invite:
      description: 'The invite code of the private hub you want to join'
  errors:
    invalidIds: '{emoji} Invalid hub or channel ID.'
    channelNotFound: '{emoji} Channel not found or not a text channel.'
disconnect:
  description: '👋 Disconnect a channel from a hub'
#Staff commands
ban:
  description: '🔨 Ban users or servers from InterChat with comprehensive options'
  options:
    duration:
      description: 'Ban duration'
    reason:
      description: 'Reason for the ban (required)'
    user:
      description: 'User to ban (required for user bans)'
    serverId:
      description: 'Server ID to ban (required for server bans)'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to ban.'
unban:
  description: '🔓 Unban users or servers from InterChat'
  options:
    user:
      description: 'User to unban'
    serverId:
      description: 'Server ID to unban'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to unban.'
    invalidTarget: '{emoji} Invalid ban target. Please use the autocomplete to select a valid ban.'
    banNotFound: '{emoji} Ban not found.'
    serverBanNotFound: '{emoji} Server ban not found.'
    loadFailed: '{emoji} Failed to load ban information.'
achievements:
  description: "🏆 View your achievements or another user's achievements"
  options:
    user:
      description: 'The user to view achievements for (defaults to yourself)'
    view:
      description: 'Choose which achievements to view'
  title: "🏆 {username}'s Achievements"
  progress: '**Progress:** {unlocked}/{total} achievements unlocked'
  errors:
    userNotFound: 'User not found.'
achievement:
  settings:
    enabled: 'Achievement notifications are now **enabled**. You will receive notifications when you unlock new achievements!'
    disabled: 'Achievement notifications are now **disabled**. You will no longer receive notifications when you unlock achievements.'
profile:
  description: "View your profile or someone else's InterChat profile."
  options:
    user:
      description: 'The user to view the profile of.'
  errors:
    userNotFound: 'User not found.'
rank:
  description: 'Display user rank and statistics'
  options:
    user:
      description: 'The user to get the rank of'
  errors:
    createFailed: 'Failed to create rank card. Please try again later.'
#Userphone commands
call:
  description: '📞 [BETA] Start a call with another server'
  errors:
    skipFailed: 'Skip Failed'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
hangup:
  description: '📞 End the current call'
  callEnded: '{user} ended the call.'
  errors:
    error: 'Error'
    callFailed: 'Call Failed'
    guildOnly: '{emoji} This command can only be used in a server text channel.'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
skip:
  description: '[BETA] Skip the current call and find a new match'
  errors:
    error: 'Error'
    skipFailed: 'Skip Failed'
voteCommand:
  description: '✨ Voting perks and vote link.'
#Welcome message system for new servers
welcome:
  buttons:
    back: 'Back'
  calls:
    title: 'Setup Calls'
    description: 'Learn about InterChat calls - instant server-to-server connections!'
    commands: |
      ### Available Call Commands

      **{callCommand}** - Start a call with another server
      **{skipCommand}** - Skip current call and find a new match
      **{hangupCommand}** - End the current call
      **{leaderboardCommand}** - View call leaderboards
    examples:
      title: 'How to Use Calls'
      content: |
        1. Run `/call` in any text channel to start
        2. Wait to be matched with another server
        3. Chat with the other server in real-time
        4. Use `/skip` to find a different server
        5. Use `/hangup` when you're done chatting

        **Note:** Calls are in beta - for a more reliable experience, try InterChat Hubs!
  setup:
    title: '🏠 Setup Cross-Server Chat'
    description: 'Connect to hubs for persistent cross-server communities!'
    instructions: |
      ### Get Started with Hubs

      **{setupCommand}** - Guided setup to join your first hub
      **{connectCommand}** - Connect to a specific hub

      **What are Hubs?**
      Hubs are persistent chat communities where multiple servers connect and chat together 24/7. Unlike calls, hub messages stay even when you're offline!

      **Why Choose Hubs?**
      - Persistent connections that stay active
      - Join multiple themed communities
      - Advanced moderation and filtering
      - Custom welcome messages and rules
      - Thousands of servers already connected
    buttons:
      runSetup: 'Run Setup Now'
    errors:
      commandNotFound: '{emoji} Setup command not found. Please try running `/setup` manually.'
