rules:
  header: 'Reglas de InterChat'
  botRulesNote: 'Estas reglas existen para garantizar una experiencia segura y agradable para todos. Léelas con atención y respétalas:'
  rules: |
    1. **No discurso de odio ni acoso**
    -# > **Incluye:** Usar insultos o lenguaje de odio para atacar a otros, [y más]({guidelines_link}).
    2. **No contenido ilegal**
    -# > **Incluye:** Compartir enlaces a contenido ilegal, fomentar la violencia, [y más]({guidelines_link}).
    3. **No contenido NSFW extremo ni violencia explícita**
    -# > **Incluye:** Publicar violencia explícita o violencia extrema en InterChat, publicar contenido sexual en centros no NSFW, [y más]({guidelines_link}).
    4. **No spam o inundación**
    -# > **Incluye:** Envío masivo de spam o inundación de bots, [y más]({guidelines_link}).
    5. **No suplantación de identidad ni fraude**
    -# > **Incluye:** Hacerse pasar por el personal de InterChat o moderadores de centros, ejecutar estafas de criptomonedas o NFT, [y más]({guidelines_link}).
    6. **No explotación ni abuso**
    -# > **Incluye:** Acosos o comportamiento depredador hacia menores, compartir, solicitar, chantajear o amenazar para fomentar autolesiones, [y más]({guidelines_link}).
    7. **No compartir software malicioso**
    -# > **Incluye:** Compartir programas maliciosos, virus, enlaces de 'nitro gratis', scripts dañinos, [y más]({guidelines_link}).

    También aceptas seguir los [Términos de servicio de Discord](https://discord.com/terms) y las [Normas de la comunidad](https://discord.com/guidelines). Consulta la [lista completa de reglas]({guidelines_link}).
  welcome: |
    {emoji} ¡Hola {user}! Bienvenido/a a InterChat! 🎉

    ¡Estamos muy emocionados de que te hayas unido a nuestra increíble comunidad de servidores conectados a través de Discord! Antes de sumergirte en charlar con personas de todo el mundo, déjame que pase un momento para revisar nuestras amables directrices comunitarias.

    Estas simples normas ayudan a mantener InterChat como un espacio cálido, acogedor y seguro para que todos puedan hacer novios y compartir conversaciones maravillosas! ✨
  alreadyAccepted: "{emoji} Welcome back, {user}! You're all set to explore and chat with amazing communities. Have fun connecting with new friends! 🌟"
  continue: Continuar
  accept: Aceptar
  decline: Rechazar
  agreementNote: Al aceptar estas reglas, aceptas seguirlas mientras uses InterChat. Incumplirlas puede resultar en restricciones o prohibiciones.
  hubAgreementNote: |
    Al aceptar estas reglas, aceptas seguirlas mientras chateas en este centro.
    Incumplirlas puede resultar en tu expulsión del centro.

    ⚠️ **No puedes enviar mensajes en este centro hasta que aceptes estas reglas.**
  accepted: |
    {emoji} Fantastic! Welcome to the InterChat family! 🎉✨

    You're now part of an incredible community that connects thousands of servers and millions of people worldwide! We're thrilled to have you aboard this amazing journey.

    ### 🚀 Ready to Start Your Adventure?
    - **Discover Communities:** Use the [hub directory]({hubs_link}) to explore vibrant, active communities waiting for you
    - **Jump Into Conversations:** Join discussions that spark your interest, or create your own hub through our [dashboard]({dashboard_link})
    - **Try Quick Connections:** Use our `/call` command for instant one-on-one server connections - perfect for making new friends!
    - **Get Personalized Help:** Use `/setup` for a guided tour if you're feeling a bit lost

    ### 💝 We're Here for You!
    Feeling confused or need a helping hand? Don't worry - we've all been there! Join our friendly [support community]({support_invite}) where real people are excited to help you get started.

    **Love what we're doing?** Consider [supporting us]({donateLink}) to help InterChat grow and bring even more amazing features to connect communities worldwide! 🌍
  declined: |
    {emoji} Please take a moment to read and accept the community guidelines.

    These rules aren't optional—they're here to keep InterChat safe and enjoyable for everyone. Send another message or use any InterChat command to get started.

    ⚠️ Important: You won't be able to chat with other servers until you accept the rules. Take your time, but this step is required.
  hubAccepted: |
    {emoji} Has aceptado las reglas del centro.
    ¡Ahora puedes comenzar a chatear en este centro!
  hubDeclined: |
    {emoji} Has rechazado las reglas dé {hubName}.
    -# ⚠️ **No podrás enviar mensajes en este centro hasta que aceptes sus reglas.**
    -# Para intentarlo de nuevo, envía otro mensaje en este centro.
  noHubRules: Este centro aún no ha establecido reglas específicas. Sin embargo, siguen aplicando las [reglas generales de InterChat]({rules_link}).
  hubRules: Reglas del centro
  viewbotRules: 'Ver Reglas del Bot'
vote:
  description: |
    ¡Ayuda a más comunidades a descubrir InterChat! Tu voto en top.gg:
    - Ayuda a otros a encontrar comunidades activas
    - Desbloquea funciones especiales para ti
    - Apoya nuestro desarrollo independiente
  footer: 'Los votos se actualizan cada 12 horas • ¡Gracias por apoyar InterChat!'
  button:
    label: 'Vota en top.gg'
  perks:
    moreComingSoon: '¡Más beneficios próximamente! Sugiere algunos en el [servidor de soporte]({support_invite}).'
  fields:
    currentStreak: 'Current Streak:'
    lastVote: 'Last Vote'
    voterPerks: 'Voter Perks'
    voteNow: '[Vote Now]({vote_url})!'
    perks:
      messageLength: 'Increased message length (2000 characters)'
      stickers: 'Send stickers in hubs'
      createHubs: 'Create up to 4 hubs'
      welcomeMessages: 'Custom welcome messages'
      voterRole: 'Voter role in support server'
      voterBadge: 'Exclusive voter badge in /profile'
  embed:
    title: 'Vote for InterChat'
network:
  accountTooNew: '{emoji} Hey {user}! Your Discord account is still pretty new, so we need to wait a little bit before you can send messages through InterChat. This helps keep our community safe! Please try again in a little while.'
  deleteSuccess: '{emoji} El mensaje de {user} se ha eliminado de __**{deleted} de {total}**__ servidores.'
  editInProgress: '{emoji} Tu solicitación está siendo procesada. Mensajes serán editados en breve...'
  editInProgressError: '{emoji} Este mensaje ya está siendo editado por otro usuario.'
  emptyContent: '{emoji} Contenido del mensaje no puede estar vacío.'
  newMessageContent: 'Nuevo contenido de mensaje'
  editMessagePrompt: '{emoji} Por favor use el modal para editar tu mensaje.'
  editSuccess: '{emoji} El mensaje de {user} se ha editado en __**{edited} de {total}**__ servidores.'
  onboarding:
    welcome:
      title: '🎉 Welcome to InterChat!'
      description: |
        Welcome to InterChat! Let's get you set up with a personalized experience that matches your interests and helps you find the perfect communities to join.

        This quick setup will help us:
        - Learn about your interests and preferences
        - Find the best hubs for you to join
        - Connect you with like-minded people
        - Get you started with your first community

        Ready to begin your InterChat journey?
    embed:
      title: '🎉 Welcome to {hubName}!'
      description: |
        Congratulations! You've discovered an amazing, active community hub! 🌟

        Before you dive in and start making new friends, let's take a quick peek at our simple guidelines. They're designed to help everyone have the most fun and feel comfortable sharing their thoughts and experiences.

        Let's get started!
      footer: InterChat Network | Connecting Communities Worldwide 🌍
    inProgress: "{emoji} {channel} ya está en proceso de configuración para unirse a un centro.\nPor favor, espera a que se complete la configuración o cancélala si fuiste quien la inició."
blacklist:
  description: 'Mute/Ban a user or server from your hub.'
  success: '{emoji} ¡**{name}** ha sido agregado con éxito a la lista negra!'
  removed: '{emoji} ¡**{name}** ha sido eliminado con éxito de la lista negra!'
  modal:
    reason:
      label: Razón
      placeholder: Razón de la lista negra
    duration:
      label: Duración
      placeholder: 'Duración de la lista negra. Por ejemplo: 1d, 1w, 1m, 1y. Deje en blanco para que sea permanente.'
  user:
    description: 'Mute/Ban a user from your hub.'
    options:
      user:
        description: 'The ID of the user to blacklist (get id using /messageinfo command)'
      hub:
        description: 'Hub to blacklist from'
    selectDuration: 'Select blacklist duration for {username}:'
    cannotBlacklistMod: '{emoji} No puedes poner en la lista negra a un moderador. Por favor, elimina primero su rol de moderador.'
    alreadyBlacklisted: '{emoji} Este usuario ya está en la lista negra.'
    easterEggs:
      blacklistBot: No puedes incluirme en la lista negra wtf.
  server:
    description: 'Mute/Ban a server from your hub.'
    options:
      server:
        description: 'The server to blacklist'
      hub:
        description: 'Hub to blacklist from'
    selectDuration: 'Select blacklist duration for {serverName}:'
    alreadyBlacklisted: '{emoji} Este servidor ya está en la lista negra.'
    unknownError: Error al agregar a la lista negra a **{server}**. Consulte a los desarrolladores para obtener más información.
  list:
    description: 'List all blacklisted users and servers in your hub.'
    user: |
      **ID de Usuario:** {id}
      **Moderador:** {moderator}
      **Razón:** {reason}
      **Expira:** {expires}
    server: |
      **Id del Servidor:** {id}
      **Moderador:** {moderator}
      **Razón:** {reason}
      **Expira:** {expires}
msgInfo:
  buttons:
    message: Información del mensaje
    server: Información del servidor
    user: Información del usuario
    report: Reportar
  report:
    notEnabled: '{emoji} Este centro no tiene habilitado la acción de reportar.'
    success: '{emoji} Reporte enviado correctamente. ¡Gracias!'
invite: |
  Gracias por elegir invitar a InterChat. ¡Haga clic en el botón de abajo para invitarme!

  **[{invite_emoji} `Enlace de invitación`]({invite}) [{support_emoji} `Servidor de soporte`]({support})**
connection:
  joinRequestsDisabled: '{emoji} Las solicitudes de unión están desactivadas para este centro.'
  notFound: '{emoji} Conexión no válida. Verifique el ID del canal o seleccione de las opciones mostradas.'
  channelNotFound: '{emoji} No se puede encontrar el canal conectado. Para hablar nuevamente, elija un nuevo canal.'
  alreadyConnected: '{emoji} El canal {channel} ya está conectado a un centro.'
  switchChannel: '{emoji} Seleccione un canal para cambiar a usar el menú Seleccionar a continuación:'
  switchCalled: '{emoji} Interruptor de canal llamado, use el comando nuevamente para ver una nueva conexión.'
  switchSuccess: '{emoji} El canal ha sido conmutado. Ahora está conectado desde **{channel}**.'
  inviteRemoved: '{emoji} Invitación del servidor eliminada para este centro.'
  setInviteError: "{emoji} No se pudo crear la invitación.  \nPor favor, concédeme el permiso de `Crear Invitación` para el canal conectado."
  inviteAdded: '{emoji} Invitación añadida. Ahora otros pueden unirse a este servidor usando el comando `Apps > Información del mensaje/Reportar` y el comando `/joinrequest`.'
  emColorInvalid: '{emoji} Color no válido. Asegúrese de haber ingresado un código de color hexadecimal válido.'
  emColorChange: '{emoji} Se estableció el color exitosamente: {action}'
  embed:
    title: Detalles de la conexión
    fields:
      hub: Centro
      channel: Canal
      invite: Invitar
      connected: Conectada
      emColor: Color del embed
      compact: Modo compacto
    footer: Usa el menú desplegable de abajo para gestionar tu conexión.
  selects:
    placeholder: '🛠️ Seleccione una opción a continuación para administrar su conexión'
  unpaused:
    desc: |
      ### {tick_emoji} Conexión sin aliento

      Conexión no puesta para {channel}! Los mensajes del hub comenzarán a entrar al canal y puede enviar mensajes al hub nuevamente.
    tips: |
      **💡 Tip:** Use {pause_cmd} para pausar la conexión o {edit_cmd} para configurar los colores de embed, invitar a tu servidor y más.
  paused:
    desc: |
      ### {clock_emoji} Conexión pausada
      Conexión pausada para {channel}! Los mensajes del centro ya no entrarán en el canal y sus mensajes no se les transmitirá.
    tips: |
      **💡 consejo:** Use {unpause_cmd} para despausar la conexión o {leave_cmd} para dejar de recibir mensajes permanentemente.
hub:
  notFound: "{emoji} Hmm, we couldn't find that hub. Double-check the name you entered, or try browsing available hubs with [hub directory]({hubs_link}) to discover some amazing communities! 🔍"
  notFound_mod: '{emoji} No se puede encontrar hub. Asegúrese de haber ingresado el nombre correcto del concentrador y de que usted sea el propietario o un moderador del hub.'
  notManager: '{emoji} Debes ser un administrador del hub para realizar esta acción.'
  notModerator: '{emoji} Necesitas ser un moderador del hub para realizar esta acción.'
  notPrivate: '{emoji} Este hub no es privado.'
  notOwner: '{emoji} Solo el propietario de este hub puede realizar esta acción.'
  alreadyJoined: "Parece que hubo un problema con mi respuesta anterior. Déjame intentarlo de nuevo:\n\n{emoji} ¡Ya te has unido a otro hub **{hub}** desde {channel}! Usa `/disconnect` en ese hub y luego intenta nuevamente con `/connect`."
  invalidChannel: '{emoji} El canal no es válido. ¡Solo se admiten los canales de texto e hilo!'
  invalidImgurUrl: '{emoji} URL de imagen no válida para icono o banner. Asegúrese de haber ingresado una URL de imagen de Imgur válida que no es una galería o álbum.'
  join:
    success: |
      🎉 **Fantastic! Welcome to {hub}!** 🎉

      You've successfully connected {channel} to this amazing community! You can now chat with members from servers all around the world right from this channel. How exciting is that? ✨

      **🚀 Ready to explore?**
      - Use `/connection` to customize your connection and make it uniquely yours
      - Use `/disconnect` if you ever need to leave this hub
      - Use `/connection edit` to switch to a different channel anytime

      **💝 Pro tip:** Say hello and introduce yourself - everyone loves meeting new friends! Have fun connecting with the community! 🌟
    nsfwChannelSfwHub: '{emoji} NSFW channels cannot connect to SFW hubs. {channel} is marked as NSFW, but **{hub}** is a safe-for-work hub. Please use a non-NSFW channel or find an NSFW hub instead.'
    sfwChannelNsfwHub: '{emoji} SFW channels cannot connect to NSFW hubs. {channel} is not marked as NSFW, but **{hub}** is an adult content hub. Please use an NSFW channel or find a SFW hub instead.'
  servers:
    total: 'Servidores conectados actuales: {from}-{to} / **{total}**'
    noConnections: '{emoji} Ningún servidor se ha unido a este centro todavía. Use `/connect` para unirse a este centro.'
    notConnected: "{emoji} Ese servidor no es parte de **{hub}**."
    connectionInfo: |
      ID del servidor: {serverId}
      Canal: #{channelName} `({channelId})`
      Unido a: {joinedAt}
      Invitación: {invite}
      Conectado: {connected}
  blockwords:
    deleted: '{emoji} ¡Regla contra palabrotas eliminada con éxito!'
    notFound: '{emoji} Regla contra palabrotas no encontrada.'
    maxRules: '{emoji} Has alcanzado el número máximo de reglas contra palabrotas (2) para este hub. Por favor, elimina una regla antes de añadir otra.'
    configure: 'Configura las acciones para la regla: {rule}'
    actionsUpdated: '{emoji} Se han actualizado las acciones a tomar por la regla. **Nuevas acciones:** {actions}'
    selectRuleToEdit: Selecciona una regla para editar sus palabras/acciones
    listDescription: |
      ### {emoji} Reglas contra palabrotas
      Este hub tiene {totalRules}/2 reglas contra palabrotas configuradas.
    listFooter: Selecciona una regla usando el menú para ver sus detalles completos.
    ruleDescription: |
      ### {emoji} Editando regla: {ruleName}{words}
    ruleFooter: '¡Haz clic en el botón de abajo para editar las palabras o el nombre de la regla!'
    actionSelectPlaceholder: 'Selecciona las acciones que esta regla debería realizar.'
    embedFields:
      noActions: '{emoji} **¡Ninguna!** Configura usando el menú de abajo.'
      actionsName: 'Acciones configuradas:'
      actionsValue: '{actions}'
    modal:
      addRule: Añadir regla contra palabrotas
      editingRule: 'Advertido por: {moderator}'
      ruleNameLabel: Nombre de la regla
      wordsLabel: 'Palabras'
      wordsPlaceholder: 'Palabras separadas por comas. (Usa * como comodín). Ej.: palabra1, *palabra2*, *palabra3, palabra4*'
    validating: '{emoji} Validando regla contra palabrotas...'
    noRules: |
      ### {emoji} ¡Establezcamos algunas reglas contra las palabrotas!
      Usa el botón `Agregar regla` para crear una.
  create:
    modal:
      title: Crear centro
      name:
        label: Nombre
        placeholder: Ingrese un nombre para su centro.
      description:
        label: Descripción
        placeholder: Ingrese una descripción para su centro.
      icon:
        label: URL de icono
        placeholder: Ingrese una URL de imagen Imgur.
      banner:
        label: URL de banner
        placeholder: Ingrese una URL de imagen Imgur.
    maxHubs: '{emoji} [Vota por InterChat]({voteUrl}) para crear más hubs. Has alcanzado el número máximo de hubs ({maxHubs}) que puedes crear.'
    invalidName: '{emoji} Nombre de la concentración no válida. No debe contener `discord`,`clyde` o `/`. Elija otro nombre.'
    nameTaken: '{emoji} Este nombre del centro ya está tomado. Elija otro nombre.'
    success: |
      ## ¡Centro creado! Está __private__ por defecto.
      Usa `/hub edit hub:{name}` para personalizar tu hub. Sigue los pasos a continuación para comenzar:
      ### Próximos pasos:
      1. **Crear una invitación:**
      > Usa `/hub invite create` para generar una invitación y permitir que otros se unan.
      2. **Vincula un canal:**
      > Usa `/connect` **con el enlace de invitación generado previamente** para vincular un canal al hub y empezar a chatear.
      3. **Configurar el hub:** (Recomendado)
      > Usa `/hub config settings`, `/hub config logging` y `/hub config anti-swear` para ajustar la configuración del hub.
      4. **Agregar moderadores:**
      > Usa `/hub moderator add` para asignar moderadores al hub.
      5. **Personalizar el hub:**
      > Usa `/hub edit` para cambiar el ícono, el banner y la descripción del hub.
      6. **Hacerlo público:**
      > Usa `/hub visibility` para hacer el hub público y permitir que otros lo exploren y se unan sin necesidad de una invitación. (Opcional)

      Si tienes preguntas o necesitas ayuda, no dudes en preguntar en el [servidor de soporte]({support_invite}). Considera [donar]({donateLink}) para ayudar a cubrir los costos de desarrollo.
  delete:
    confirm: '¿Estás seguro de que desea eliminar **{hub}**? Esta acción es irreversible. Todos los servidores conectados, invitaciones y datos de mensajes se eliminarán de este centro.'
    ownerOnly: '{emoji} Solo el propietario de este centro puede eliminarlo.'
    success: '{emoji} El centro **{hub}** se ha eliminado.'
    cancelled: '{emoji} La eliminación de concentración ha sido cancelada.'
  invite:
    create:
      success: |
        ### Invitación creada!

        Su invitación ha sido creada con éxito. Otros ahora pueden unirse a este centro utilizando el comando `/connect`.

        - **Únete usando:** `/connect invite:{inviteCode}`
        - **Mira las invitaciones:** `/hub invite list`
        - **Expiración:** {expiry}
        - **Usos**: ∞

        **Nota:** Puedes revocar esta invitación usando `/hub invite revoke {inviteCode}`.
    revoke:
      invalidCode: '{emoji} Código de invitación no válido. Asegúrese de haber ingresado un código de invitación válido.'
      success: '{emoji} Invitación {inviteCode} revocada.'
    list:
      title: '**Códigos de invitación:**'
      noInvites: '{emoji} Este centro aún no tiene invitaciones. Use `/hub invite create` para crear uno.'
      notPrivate: '{emoji} Solo los centros privados pueden tener invitaciones. Use `/Hub Management` para que este centro sea privado.'
  joined:
    noJoinedHubs: '{emoji} This server has not joined any hubs yet. Use [hub directory]({hubs_link}) to view a list of hubs.'
    joinedHubs: Este servidor es parte de **{total}** hub(s). Use `/disconnect` para dejar un centro.
  leave:
    noHub: '{emoji} Ese canal no es válido o no ha unido ningún centro.'
    confirm: '¿Estás seguro de que deseas dejar **{hub}** de {channel}? No se enviarán más mensajes a este servidor desde este centro.'
    confirmFooter: Confirme usando el botón a continuación en 10 segundos.
    success: '{emoji} Nos fuimos del centro en {channel}. No se enviarán más mensajes a este servidor desde este centro. Puede reunirse usando `/connect`.'
  moderator:
    noModerators: '{emoji} Este centro todavía no tiene moderadores. Use el comando `/hub moderator add` para agregar uno.'
    add:
      success: '{emoji} **{user}** se ha agregado como moderador en posición **{position}**.'
      alreadyModerator: '{emoji} **{user}** ya es un moderador.'
    remove:
      success: '{emoji} **{user}** se ha eliminado como moderador.'
      notModerator: '{emoji} **{user}** no es un moderador.'
      notOwner: '{emoji} Solo el propietario de este centro puede eliminar un administrador.'
    update:
      success: "{emoji} **{user}** La posición se ha actualizado a **{position}**."
      notModerator: '{emoji} **{user}** no es un moderador.'
      notAllowed: "{emoji} Solo los administradores de centros pueden actualizar la posición de un moderador."
      notOwner: "{emoji} Solo el propietario de este centro puede actualizar la posición de un administrador."
  manage:
    dashboardTip: "**🛠️ NUEVO Dashboard:** ¡Interfaz mejorada y más funciones! Pruébalo en la [página de Dashboard de tu centro]({url})."
    enterImgurUrl: Ingrese una URL de imagen de Imgur válida que no sea una galería o álbum.
    icon:
      changed: El icono del centro cambió con éxito.
      modal:
        title: Editar Icono
        label: URL de icono
      selects:
        label: Editar Icono
        description: Cambie el icono de este centro.
    description:
      changed: La descripción del concentrador cambió con éxito.
      modal:
        title: Editar Descripción
        label: Descripción
        placeholder: Ingrese una descripción para este centro.
      selects:
        label: Cambiar Descripción
        description: Cambie la descripción de este centro.
    banner:
      changed: El banner del hub se cambió con éxito.
      removed: Banner de concentrador eliminado con éxito.
      modal:
        title: Editar Banner
        label: URL de banner
      selects:
        label: Editar Banner
        description: Cambia la pancarta de este centro.
    visibility:
      success: '{emoji} La visibilidad del centro cambió correctamente a **{visibility}**.'
      selects:
        label: Cambiar la visibilidad
        description: Haz este centro público o privado.
    toggleLock:
      selects:
        label: 'Bloquear/Desbloquear Centro'
        description: 'Bloquea o desbloquea los chats del centro'
      confirmation: 'Los chats de centro ahora son {status}.'
      announcementTitle: 'Los chats de centro ahora son {status}.'
      announcementDescription:
        locked: 'Solo los moderadores pueden enviar mensajes.'
        unlocked: 'Cualquiera pueden enviar mensajes.'
    toggleNsfw:
      modal:
        title: 'Toggle NSFW Status'
        label: 'NSFW Status'
        description: 'Mark this hub as containing adult content.'
      selects:
        label: 'Toggle NSFW Status'
        description: 'Mark hub as NSFW or SFW content'
      confirmation: 'Hub content rating is now {status}.'
      announcementTitle: 'Hub content rating changed to {status}'
      announcementDescription:
        nsfw: 'This hub now contains adult content. Only NSFW Discord channels can connect to this hub.'
        sfw: 'This hub is now safe for work. All channels can connect to this hub.'
    setNsfw:
      success: '{emoji} **{hub}** has been successfully marked as **{status}**.'
      announcement: "{emoji} **Hub Content Rating Changed**\n\nThis hub is now marked as **{status}**.\n\n{description}"
    nsfwAlreadySet: '{emoji} **{hub}** is already marked as **{status}**.'
    embed:
      visibility: 'Visibilidad'
      connections: 'Conexiones'
      chatsLocked: 'Chats Bloqueados'
      blacklists: 'Listas negras'
      total: 'Total'
      users: 'Usuarios'
      servers: 'Servidores'
      hubStats: 'Estadísticas del Centro'
      moderators: 'Moderadores'
      owner: 'Propietario'
    logs:
      title: Configurar registros
      reset: '{emoji} Se reinició con éxito la configuración de registros para `{type}`.'
      roleSuccess: '{emoji} registros de tipo `{type}` ahora mencionará {role}!'
      roleRemoved: '{emoji} Los registros de tipo `{type}` no volverán a mencionar un rol.'
      channelSuccess: '{emoji} registros de tipo `{type}` se enviará a {channel} de ahora!'
      channelSelect: '#️⃣ Seleccione el tipo de registro para configurar'
      roleSelect: '🏓 Seleccione el papel a mencionar cuándo se activa un registro.'
      reportChannelFirst: '{emoji} Establezca primero un canal de registro.'
      config:
        title: Configurar `{type}` registra
        description: |
          {arrow} Seleccione un canal de registro y/o rol que se haga ping desde el menú desplegable a continuación.
          {arrow} También puede deshabilitar el registro usando el botón a continuación.
        fields:
          channel: Canal
          role: Mención de roles
      reports:
        label: Informes
        description: Recibir informes de los usuarios.
      modLogs:
        label: Registros de mod
        description: Acciones de moderación de registro. (por ejemplo, la lista negra, el mensaje elimina, etc.)
      joinLeaves:
        label: Unir/salir
        description: Registre cuando un servidor se une o deja este centro.
      appeals:
        label: Apelaciones
        description: Reciba apelaciones de usuarios/servidores en la lista negra.
      networkAlerts:
        label: Alertas de la red
        description: Recibir alertas sobre mensajes bloqueados automáticamente.
      messageModeration:
        label: Moderación de mensajes
        description: Registrar eliminaciones y ediciones de mensajes por moderadores.
      messageDeletions:
        label: Message Deletions
        description: Log when messages are deleted from the hub.
      messageEdits:
        label: Message Edits
        description: Log when messages are edited in the hub.
  transfer:
    invalidUser: '{emoji} No se encontró el usuario especificado.'
    selfTransfer: '{emoji} No puedes transferirte la propiedad a ti mismo.'
    botUser: '{emoji} No puedes transferir la propiedad a un bot.'
    confirm: '¿Estás seguro de que quieres transferir la propiedad de **{hub}** a {newOwner}? Serás degradado al rol de administrador.'
    cancelled: '{emoji} La transferencia ha sido cancelada.'
    error: '{emoji} Se ha producido un error al transferir la propiedad del centro.'
    success: '{emoji} Se ha transferido con éxito la propiedad de **{hub}** a {newOwner}. Has sido añadido como administrador.'
    timeout: '{emoji} Se ha agotado el tiempo de espera de transferencia del centro.'
  rules:
    noRules: "{emoji} Este centro no tiene reglas configuradas aún. ¡Añadamos algunas!"
    list: "### {emoji} Reglas del centro\n{rules}"
    maxRulesReached: '{emoji} Se ha alcanzado el número máximo de reglas ({max}).'
    ruleExists: '{emoji} Esta regla ya existe.'
    selectedRule: 'Regla seleccionada {number}'
    modal:
      add:
        title: Añadir regla al centro
        label: Contenido de la regla
        placeholder: Introduzca el contenido de la regla (máx. 1000 caracteres)
      edit:
        title: Editar regla del centro
        label: Contenido de la regla
        placeholder: Introduzca el nuevo contenido de la regla (máx. 1000 caracteres)
    select:
      placeholder: Seleccione una regla para editar o eliminar
      option:
        label: Regla {number}
    buttons:
      add: Añadir regla
      edit: Editar regla
      delete: Eliminar regla
      back: Volver
    success:
      add: '{emoji} ¡Regla añadida con éxito!'
      edit: '{emoji} ¡Regla actualizada con éxito!'
      delete: '{emoji} ¡Regla eliminada con éxito!'
    view:
      title: 'Regla {number}'
      select: Seleccione una acción para esta regla
  welcome:
    set: '{emoji} ¡Mensaje de bienvenida actualizado con éxito!'
    removed: '{emoji} Mensaje de bienvenida eliminado.'
    voterOnly: '{emoji} ¡Los mensajes de bienvenida personalizados son un beneficio de solo votantes! Vota para desbloquear esta función.'
    placeholder: |
      ¡Bienvenido {user} de {serverName} a {hubName}! 🎉
      Miembros: {memberCount}, Centro: {totalConnections}!!
report:
  modal:
    title: Detalles del reporte
    other:
      label: Detalles del reporte
      placeholder: Una descripción detallada del reporte.
    bug:
      input1:
        label: Detalles del error
        placeholder: Ej. Fallas de interacción frecuentes para /help command...
      input2:
        label: Descripción detallada (opcional)
        placeholder: Pasos que tomaste. Ej. 1. Ejecuta /help 2. Espera 5 segundos...
  reasons:
    spam: Spam o mensajes excesivos
    advertising: Anuncios no deseados o autopromoción
    nsfw: NSFW o contenido inapropiado
    harassment: Acoso u hostigamiento
    hate_speech: Discurso de odio o discriminación
    scam: Intento de estafa, fraude o phishing
    illegal: Contenido ilegal o actividades ilegales
    personal_info: Compartir información personal/privada
    impersonation: Suplantar la identidad de otros
    breaks_hub_rules: Violar las reglas del centro
    trolling: Trolear o interrupción intencional
    misinformation: Información falsa o engañosa
    gore_violence: Gore o violencia extrema
    raid_organizing: Organizar raideos o ataques
    underage: Usuario menor de edad o contenido de menores de edad
  dropdown:
    placeholder: Seleccione una razón para su reporte
  submitted: '{emoji} Reporte enviado con éxito. Únete a {support_command} para obtener más detalles. ¡Gracias!'
  errors:
    noReasonSelected: '{emoji} No reason selected. Please try again.'
    hubNotFound: '{emoji} Hub not found. Please try again.'
  description: 'Report a message'
  options:
    message:
      description: 'The message to report'
  contextMenu:
    name: 'Report Message'
  selectReason: '{emoji} Please select a reason for your report:'
  bug:
    title: Informe de error
    affected: Componentes afectados
    description: Elija qué componente del bot con el que enfrenta problemas.
language:
  set: '¡Sé de idiomas! Ahora te responderé en **{lang}**.'
errors:
  messageNotSentOrExpired: '{emoji} Este mensaje no se envió en un centro, ha expirado o te faltan permisos para realizar esta acción.'
  notYourAction: "{emoji} Lo siento, no puedes realizar esta acción. Por favor ejecute el comando usted mismo."
  notMessageAuthor: '{emoji} No eres el autor de este mensaje.'
  commandError: |
    {emoji} Oops! Something unexpected happened while running this command. Don't worry - this isn't your fault!

    We've automatically logged this issue and our team will take a look. If this keeps happening, please drop by our friendly [support server]({support_invite}) and let us know about this error ID - we're always happy to help! 🤗

    **Error ID:**
    ```{errorId}```
  mustVote: Por favor [vote](https://top.gg/bot/769921109209907241/vote) para que InterChat use este comando, ¡su apoyo es muy apreciado!
  inviteLinks: '{emoji} No puedes enviar enlaces de invitación a este hub. Configura una invitación en `/connection` en su lugar. Los moderadores del hub pueden configurar esto usando `/hub edit settings`'
  invalidLangCode: '{emoji} Código de lenguaje no válido. Asegúrese de haber ingresado un [código de idioma] correcto (https://cloud.google.com/translate/docs/languages).'
  modalError: '{emoji} There was an error showing the modal. Please try again.'
  unknownServer: '{emoji} servidor desconocido. Asegúrese de haber ingresado correctamente la **ID del Servidor**.'
  unknownNetworkMessage: '{emoji} Mensaje desconocido. Si se ha enviado en el último minuto, espere unos segundos más e intente nuevamente.'
  userNotFound: '{emoji} usuario no encontrado. Intente ingresar su ID en su lugar.'
  blacklisted: '{emoji} Tú o este servidor están en la lista negra de este hub llamado {hub}.'
  userBlacklisted: '{emoji} Estás en la lista negra de este centro.'
  serverBlacklisted: '{emoji} Este servidor está en la lista negra de este centro.'
  serverNotBlacklisted: '{emoji} El servidor ingresado no está en la lista negra.'
  userNotBlacklisted: '{emoji} El usuario ingresado no está en la lista negra.'
  missingPermissions: '{emoji} Me falta los siguientes permisos para realizar esta acción: **{permissions}**'
  botMissingPermissions: '{emoji} Por favor, otorgame los siguientes permisos para continuar: **{permissions}**'
  unknown: '{emoji} Ocurrió un error desconocido. Vuelva a intentarlo más tarde o contáctenos uniéndose a nuestro [servidor de soporte]({support_invite}).'
  notUsable: '{emoji} Esto ya no se puede usar.'
  cooldown: '{emoji} Estás en el tiempo de reutilización. Espere **{time}** antes de intentarlo nuevamente.'
  serverNameInappropriate: '{emoji} El nombre de tu servidor contiene palabras inapropiadas. Por favor, cámbialo antes de unirte al centro.'
  banned: |
    {emoji} Se te ha prohibido usar Interchat por violar nuestras [reglas](https://interchat.tech/guidelines).
    Si cree que es aplicable una apelación, cree un ticket en el [Servidor de soporte]({support_invite}).
  commandLoadingError: 'There was an error loading the command. Please try again later.'
  errorLoadingHubs: 'Error Loading Hubs'
  errorShowingHubSelection: 'There was an error showing the hub selection screen. Please try again.'
  connectNotFound: 'Connect command not found. Please try again.'
config:
  setInvite:
    success: |
      ### {emoji} Conjunto de Enlace de Invitación
      - La invitación de tu servidor se utilizará cuando la gente use `/joinserver`.
      - Se mostrará en `/leaderboard server`.
    removed: '{emoji} ¡Invitacióeliminada con éxito!'
    invalid: '{emoji} La invitación es inválida. Asegúrate de haber ingresado un enlace de invitación válido. Por ejemplo. `https://discord.gg/discord`'
    notFromServer: '{emoji} Esta invitación no es de este servidor.'
badges:
  shown: '{emoji} Tus insignias ahora se mostrarán en los mensajes.'
  hidden: '{emoji} Tus insignias ahora no se mostrarán en los mensajes.'
  command:
    description: '🏅 Configura las preferencias al mostrar tus insignias'
    options:
      show:
        name: 'mostrar'
        description: 'Mostrar u ocultar tus insignias en los mensajes'
  list:
    developer: 'Desarrollador principal de InterChat'
    staff: 'Miembro del staff de InterChat'
    translator: 'Traductor del InterChat'
    voter: 'Ha votado por InterChat en las últimas 12 horas'
global:
  webhookNoLongerExists: '{emoji} El webhook para este canal ya no existe. Para continuar usando Interchat, vuelva a crear el webhook usando `/connection unpause`.'
  noReason: No se proporcionó ninguna razón.
  noDesc: Sin descripción.
  version: InterChat v{version}
  loading: '{emoji} Por favor, espere mientras proceso su solicitud...'
  reportOptionMoved: '{emoji} ¡Esta opción se ha movido! Para informar un mensaje a los moderadores del centro, use el comando actualizado `Apps > Message Info/Report`. Para informar directamente al personal de Interchat, simplemente entre al [Servidor de soporte]({support_invite}) y cree un ticket con pruebas adjuntas.'
  private: 'Privado'
  public: 'Público'
  yes: 'Sí'
  no: 'No'
  cancelled: '{emoji} Cancelado. No se han realizado cambios.'
  #Common button labels
  buttons:
    openInbox: 'Open Inbox'
    modPanel: 'Mod Panel'
    joinServer: 'Join Server'
    disconnect: 'Disconnect'
    reconnect: 'Reconnect'
    editRule: 'Edit Rule'
    #Setup buttons
    joinPopularHub: 'Join Popular Hub'
    createNewHub: 'Create New Hub'
    finishSetup: 'Finish Setup'
    findMoreHubs: 'Find More Hubs'
    supportServer: 'Support Server'
    viewChannel: 'View Channel'
    hubDirectory: 'Hub Directory'
    learnMore: 'Learn More'
    connectToHub: 'Connect to a Hub'
    #Common buttons for calls and other features
    createYourHub: 'Create Your Hub'
    #Call buttons
    cancelCall: 'Cancel Call'
    newCall: 'New Call'
    #Leaderboard
    userLeaderboard: 'User Leaderboard'
    serverLeaderboard: 'Server Leaderboard'
  #Common modal titles and labels
  modals:
    editConnection:
      title: 'Edit Connection'
      channelName:
        label: 'Channel Name'
        placeholder: 'Enter a custom name for this channel'
      profanityFilter:
        label: 'Profanity Filter'
      compact:
        label: 'Compact Mode'
    hubCreation:
      title: 'Create New Hub'
      name:
        label: 'Hub Name'
        placeholder: 'e.g., Gaming Community, Art Gallery'
      description:
        label: 'Description'
        placeholder: 'What is this hub about?'
    messageInfo:
      title: 'Message Information'
    editRule:
      title: 'Edit Rule'
      content:
        label: 'Rule Content'
        placeholder: 'Enter the rule content...'
  #Common messages and responses
  messages:
    selectChannel: 'Select a channel'
    selectHub: 'Select a hub'
    noHubsAvailable: 'No hubs available'
    hubNotFound: 'Hub not found'
    channelNotFound: 'Channel not found'
    connectionNotFound: 'Connection not found'
    invalidSelection: 'Invalid selection'
    operationCancelled: 'Operation cancelled'
    setupComplete: 'Setup complete!'
    connectionEstablished: 'Connection established'
    connectionRemoved: 'Connection removed'
    settingsUpdated: 'Settings updated'
    ruleAdded: 'Rule added'
    ruleUpdated: 'Rule updated'
    ruleDeleted: 'Rule deleted'
    #Leaderboard messages
    noDataAvailable: 'No data available'
    loadingData: 'Loading data...'
    #Connection status
    connected: 'Connected'
    disconnected: 'Disconnected'
    paused: 'Paused'
    #Hub visibility
    publicHub: 'Public Hub'
    privateHub: 'Private Hub'
#Leaderboard
leaderboard:
  title: 'Global Message Leaderboard'
  description: 'Resets every month. Send a message in any hub to get on it!'
warn:
  description: 'Warn a user in your hub'
  options:
    user:
      description: 'The user to warn'
    hub:
      description: 'The hub to warn in'
    reason:
      description: 'Reason for the warning'
  errors:
    cannotWarnSelf: '{emoji} You cannot warn yourself.'
  modal:
    title: Advertir al usuario
    reason:
      label: Razón
      placeholder: Introduzca la razón para advertir a este usuario...
  success: |
    {emoji} Se advirtió con éxito a **{name}**.

    -# Serán notificados de la advertencia más reciente la próxima vez que envíen un mensaje en el centro. Evite emitir varias advertencias a la vez.
  dm:
    title: '{emoji} Notificación de advertencia'
    description: 'Has sido advertido en el centro **{hubName}**'
  log:
    title: '{emoji} Usuario advertido'
    description: |
      {arrow} **Usuario:** {user} ({userId})
      {arrow} **Moderador:** {moderator} ({modId})
      {arrow} **Razón:** {reason}
    footer: 'Advertido por: {moderator}'
calls:
  connected:
    title: "You're Connected! 🎉"
    description: "You've been matched with another awesome server! Say hello and start chatting - this is where the magic happens! ✨"
    instructions: 'Use `/hangup` to end the call • `/skip` to find a different server'
    serverInfo: '**Connected to:** {serverName} ({memberCount} members)'
    duration: '**Call Duration:** {duration}'
    messages: '**Messages Exchanged:** {count}'
  waiting:
    title: 'Finding Your Perfect Match'
    description: 'Added to call queue. Waiting for another server to join...'
  failed:
    title: 'Call Failed'
    description: "Check you're not already in a call and try again in a few moments."
    reasons:
      alreadyInCall: 'This channel is already in a call!'
      alreadyInQueue: 'This channel is already in the call queue!'
      webhookFailed: 'Failed to create webhook. Please try again later.'
      channelInvalid: 'Cannot skip call - invalid channel'
  cancelled:
    title: 'Call Cancelled'
    description: 'Call queue exited. Use `/call` to start a new call.'
    queueExit: 'You have been removed from the call queue'
  ended:
    title: 'Call Ended'
    description: 'Thanks for chatting! Hope you made some new friends! 🌟'
    stats: |
      **Call Summary:**
      • Duration: {duration}
      • Messages: {messages}
      • Server: {serverName}
    ratePrompt: 'How was your call experience?'
  skip:
    title: 'Finding New Call'
    description: 'Previous call ended • Waiting for another server • Use `/hangup` to cancel'
    newConnected:
      title: 'New Call Connected!'
      description: "You've been connected to a different server • Use `/hangup` to end"
    error: 'Unable to skip call. Please try again.'
  hangup:
    confirm: 'Are you sure you want to end this call?'
    success: 'Call ended successfully. Thanks for chatting!'
    queueOnly: 'Removed from call queue.'
  buttons:
    endCall: 'End Call'
    skipServer: 'Skip Server'
    skipAgain: 'Skip Again'
    cancelCall: 'Cancel Call'
    newCall: 'New Call'
    exploreHubs: 'Explore Hubs'
    browseAllHubs: 'Browse All Hubs'
    ratePositive: 'Good Call 👍'
    rateNegative: 'Poor Call 👎'
    reportCall: 'Report Call'
  hubs:
    promotion:
      title: '🌟 Discover InterChat Hubs!'
      description: 'Calls are in beta. For a more reliable experience, try InterChat Hubs - our main feature for connecting servers!'
    benefits:
      title: 'Why Choose Hubs?'
      description: 'Hubs offer a more reliable and feature-rich experience than calls:'
      list: |
        • **Persistent Connections** - Messages stay even when you're offline
        • **Multiple Communities** - Join various themed hubs or create your own
        • **Advanced Moderation** - Content filtering, anti-spam, and more
        • **Rich Features** - Custom welcome messages, rules, and settings
        • **Active Communities** - Thousands of servers already connected
    main:
      title: 'InterChat Hubs'
      description: 'Hubs are the main feature of InterChat, connecting servers in persistent chat communities'
  system:
    callStart: |
      {emoji} **Your Call is Connected!** Say hello! 🎉
      > - Say hello and start chatting with the other server!
      > - Use `/hangup` when you're ready to end the call
      > - Remember to keep things friendly and follow our [community guidelines]({guidelines})
  rating:
    success: 'Thanks for rating! Your **{type}** feedback has been recorded for {count} participant{plural}.'
    alreadyRated: '{emoji} You have already rated this call.'
    invalidButton: '{emoji} Invalid rating button. Please try again.'
    noCallData: '{emoji} Unable to find call data. The call might have ended too long ago.'
    noParticipants: '{emoji} Unable to find participants from the other channel.'
  report:
    prompt: '{emoji} Please select a reason for your report:'
    invalidButton: '{emoji} Invalid report button. Please try again.'
  leaderboard:
    title: 'Global Calls Leaderboard'
    description: 'Shows data from this month'
    noData: 'No data available.'
    userTab: 'User Leaderboard'
    serverTab: 'Server Leaderboard'
  errors:
    guildOnly: 'This command can only be used in a server text channel.'
#Command descriptions and help text
commands:
  about:
    title: 'About InterChat'
    description: 'Learn more about InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    support_text: 'Need help? Join our support server for assistance!'
    features:
      title: 'Features'
      list: |
        - Connect with other servers for active cross-server discussions
        - Messages flow naturally between servers in real-time
        - Build engaged topic-focused communities
        - Moderation tools to keep discussions healthy
        - Visual dashboard to manage your hubs, servers, and settings
    buttons:
      vote: 'Vote on top.gg'
      invite: 'Invite InterChat'
      dashboard: 'Visit Dashboard'
      support: 'Join Support Server'
      credits: 'View Credits'
    credits:
      title: 'Credits'
      developers: 'Developers'
      staff: 'Staff'
      translators: 'Translators'
      mentions: 'Special Mentions'
      mascot: 'Mascot'
      top_voter: 'Top Voter'
      footer: 'Version {version}'
    sections:
      invite: 'Invite InterChat to your server'
      dashboard: 'Visit the InterChat dashboard'
      support: 'Join our support server'
      credits: 'View the InterChat credits'
    errors:
      serverOnly: 'This command can only be used in a server.'
  help:
    description: '📚 Explore InterChat commands with our new help system'
    options:
      command:
        description: 'The command to get info about'
      category:
        description: 'View commands by category'
    errors:
      categoryNotFound: '{emoji} Category not found.'
      commandNotFound: '{emoji} Command `{command}` not found.'
      showingCategory: '{emoji} An error occurred while showing the category.'
      showingCommand: '{emoji} An error occurred while showing the command help.'
      showingMenu: '{emoji} An error occurred while showing the help menu.'
      showingSearch: '{emoji} An error occurred while showing the search interface.'
  setup:
    description: 'Setup InterChat in your server'
    errors:
      serverOnly: 'This command can only be used in a server.'
      missingPermissions: |
        I need the following permissions to work properly:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please give me these permissions and try again!
        Need help? [Join our support server]({supportInvite})
      setupError: 'There was an error starting the setup process. Please try again later.'
      completionError: 'There was an error completing the setup. Please try again or contact support if the issue persists.'
      channelNotSelected: 'No channel was selected. Please try again.'
      invalidChannelType: 'Please select a text or thread channel. Voice channels, forums, and other channel types are not supported.'
      missingChannelPermissions: |
        I need the following permissions in {channel}:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please update the channel permissions and try again!
      channelAlreadyConnected: 'This channel is already connected to the hub "{hubName}". Please select a different channel.'
      channelNotFound: 'Selected channel no longer exists. Please run the setup command again.'
      hubNotFound: 'This hub no longer exists. Please choose another one.'
      commandLoadingError: 'Failed to load commands. Please try again or join our support server for help.'
      interactionError: '{emoji} Oops! Something went a bit wonky there. No worries though - just give it another try! If this keeps happening, our friendly support team is always here to help.'
      userMismatch: 'This setup is for another user.'
      serverRequired: 'You must be in a server to use this.'
      timeout: "No worries! The setup just timed out while waiting for your response. When you're ready to continue your InterChat journey, just run `/setup` again and we'll pick up right where we left off!"
      noAvailableHubs: 'Your server is already connected to all available popular hubs! Try creating a new hub instead.'
      hubCreationFailed: 'Failed to create hub. Please try again.'
      validationError: 'Invalid hub data provided. Please try again.'
    welcome:
      title: '🎉 Welcome to InterChat Setup!'
      description: |
        Hey! Let's get your server connected to InterChat.

        This setup will guide you through everything you need:

        📍 Select a channel for the chat

        🏠 Join or create a hub (your community space)

        ⚙️ Finish setup to start chatting

        What's a Hub? It's a shared space where servers connect and chat together. Simple as that.

        Let's get this set up and running. 🚀
    channelSelection:
      title: '📍 Step 1: Choose Your Perfect Channel'
      description: "Let's pick the channel where all the exciting InterChat conversations will happen! This can be any text channel in your server - maybe create a special one just for this?"
      placeholder: 'Select a channel'
      tips:
        title: '💡 Helpful Tips for Success'
        content: |
          - **Create something special:** Try naming it `#interchat`, `#global-chat`, or `#world-chat`
          - **Think about visibility:** Make sure members who want to join the fun can see this channel
          - **Room to grow:** You can always connect more channels to different communities later
          - **Keep it organized:** A dedicated channel helps keep conversations flowing smoothly
    hubChoice:
      title: 'InterChat Setup (2/4)'
      description: "Great! Messages will appear in {channel}. Now, let's connect to a hub!"
      whatIsHub:
        title: 'What is a Hub?'
        description: "A hub is InterChat's main feature - a shared chat space where multiple servers can talk together. Hubs are persistent communities that stay connected 24/7, unlike temporary calls."
      popularHubs:
        title: 'Popular Hubs'
        description: |
          - Join thriving active communities with thousands of users
          - Start chatting immediately with other servers
          - Perfect for new users to experience InterChat
          - No additional setup required - just connect and chat!
      createHub:
        title: 'Create Your Own Hub'
        description: |
          - Start your own community themed around your interests
          - Full control over settings, moderation, and features
          - Invite specific servers to create a private network
          - Set custom rules, welcome messages, and more
      note: 'You can always join more hubs later with the `/connect` command!'
    hubSelection:
      title: 'InterChat Setup (2/4)'
      description: 'Choose a hub to join from our most active communities:'
      placeholder: 'Choose a hub to join'
      tip: '**Tip:** You can always join more hubs later using `/connect` and [the hub list](https://interchat.app/hubs).'
    hubCreation:
      modal:
        title: 'Create New Hub'
        name:
          label: 'Hub Name'
          placeholder: 'e.g., Gaming Community, Art Gallery'
        description:
          label: 'Description'
          placeholder: 'What is this hub about?'
    nextSteps:
      created:
        title: '✨ Almost Done!'
        description: "Your Hub \"{hubName}\" is Ready!\nClick Finish Setup to complete the process. After that, follow these steps:"
        inviteLink:
          title: '1️⃣ Create an Invite Link'
          description: "{hubInviteCommand} `hub:{hubName}`\nThis will generate an invite link you can share with other servers"
        shareHub:
          title: '2️⃣ Share Your Hub'
          description: |
            Share the invite link with at least one other server to start chatting!
            {dot} Send to your friends & servers
            {dot} Share in our [support server]({supportInvite})
        configuration:
          title: '3️⃣ Essential Configuration'
          description: |
            {hubRulesCommand}
            Create hub rules and guidelines

            {hubLoggingCommand}
            Set up logging channels for hub events

            {hubAntiSwearCommand}
            Configure word filters and auto-moderation

            {hubSettingsCommand}
            Manage message types and notifications
        proTips:
          title: '💡 Pro Tips'
          description: |
            {dot} Your hub is private by default - only servers with invites can join
            {dot} Vote for InterChat to unlock custom welcome messages and colors
            {dot} You can publish your hub to the [hub directory]({website}/hubs) using {hubVisibilityCommand}
            {dot} Join our [support server]({supportInvite}) for hub management tips!
        copyCommand: "`/hub invite create hub:{hubName}`\n✨ Command copied! Run this to create an invite link."
      joined:
        title: '✨ Ready to Join?'
        description: "Ready to Join \"{hubName}\"?\nClick Finish Setup to join the hub. After joining, you can use these commands:"
        commands: |
          {connectionEditCommand}
          Customize how you receive/send messages to the hub

          {connectionListCommand}
          View all your connected hubs

          {website}/hubs (New :sparkles:)
          Join more hubs
        help: 'Join our [support server]({supportInvite}) if you have questions!'
    completion:
      title: 'Setup Complete!'
      description: 'Your server has been successfully connected to the hub in {channel}. You can now start chatting!'
    buttons:
      supportServer: 'Support Server'
      documentation: 'Documentation'
      goBack: 'Go Back'
      finishSetup: 'Finish Setup'
      hubDirectory: 'Hub Directory'
      learnMore: 'Learn More'
      viewChannel: 'View Channel'
      joinPopularHub: 'Join Popular Hub'
      createNewHub: 'Create New Hub'
      copyInviteCommand: 'Copy Invite Command'
      findMoreHubs: 'Find More Hubs'
    existingConnections:
      title: 'Existing Connections'
      description: |
        Your server is already connected to the following hubs:

        {connectionList}

        You can continue to add more connections if you'd like.
  language:
    description: '🈂️ Set the language in which I should respond to you'
    options:
      lang:
        description: 'The language to set'
  badges:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  tutorial:
    description: '📚 Learn how to use InterChat with interactive tutorials'
    subcommands:
      start:
        description: 'Start a specific tutorial'
        options:
          tutorial:
            description: 'The tutorial to start'
      setup:
        description: 'Start the server setup tutorial (for admins)'
  rules:
    description: '📋 Sends the network rules for InterChat.'
    options:
      hub:
        description: 'View rules for a specific hub'
    hubRules:
      title: '{hubName} Rules'
      description: 'The following rules apply to this hub'
    botRules:
      title: 'InterChat Rules'
      description: 'Bot-wide rules for all users'
#Tutorial system
tutorial:
  errors:
    notFound: '{emoji} Tutorial not found.'
    noSteps: '{emoji} This tutorial has no steps.'
    prerequisitesRequired: '{emoji} You need to complete the prerequisite tutorials first.'
    noInProgress: '{emoji} You have no tutorials in progress.'
  completion:
    completed: '{emoji} Tutorial completed! Great job!'
    nextRecommendation: 'Next recommended tutorial: {tutorialName}'
  categories:
    newUser: 'New User Tutorials'
    admin: 'Server Admin Tutorials'
    moderator: 'Moderator Tutorials'
    all: 'General Tutorials'
  list:
    title: 'Available Tutorials'
    noTutorials: 'No tutorials available at the moment.'
    description: 'Choose a tutorial to get started with InterChat'
  progress:
    completed: '✅ Completed'
    inProgress: '▶️ In Progress'
    notStarted: '⭕ Not Started'
  buttons:
    start: 'Start Tutorial'
    resume: 'Resume'
    review: 'Review'
    next: 'Next'
    previous: 'Previous'
    skip: 'Skip'
    finish: 'Finish'
  about:
    description: '🚀 Learn how InterChat helps grow Discord communities'
    title: 'About InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    features:
      title: 'What makes InterChat different:'
      list: |
        - Built for real communities - Designed with Discord server owners' needs in mind
        - Active hubs - Find and join thriving communities around shared interests
        - Privacy first - Full control over your hub's connections and settings
        - Smart moderation - AI-powered image filtering and advanced content filtering keeps discussions healthy
        - Visual dashboard - Manage your hubs, servers, and settings through our web interface
    sections:
      invite: 'Invite InterChat to your server:'
      dashboard: 'Visit the InterChat dashboard:'
      support: 'Join our support server:'
      credits: 'Check out the InterChat team!'
    buttons:
      invite: 'Invite'
      dashboard: 'Dashboard'
      support: 'Support Server'
      credits: 'Credits & Team'
      vote: 'Vote!'
    support_text: 'InterChat is completely free to use. If you like InterChat, consider supporting us on Ko-fi! Or even a vote on top.gg helps us a lot!'
    credits:
      title: 'CREDITS'
      developers: 'Developers:'
      staff: 'Staff: ([Check Applications!]({website}/apply))'
      translators: 'Translators:'
      mentions: 'Deserving Mentions:'
      mascot: '(maker of our cute mascot chipi {emoji})'
      top_voter: '([top voter]({vote_url}) of all time {emoji})'
      footer: 'InterChat v{version} • Made with ❤️ by the InterChat Team'
#Hub configuration and management
hubConfig:
  antiSwear:
    title: 'Anti-Swear Configuration'
    description: 'Configure word filters and auto-moderation for this hub'
    noRules: "Let's set up some anti-swear rules!\nUse the `Add Rule` button to create one."
    selectRule: "Select a rule to edit it's words/actions"
    placeholder: 'Select a log type to configure'
    validating: '{emoji} Validating anti-swear rule...'
    buttons:
      addRule: 'Add Rule'
      editRule: 'Edit Rule'
      deleteRule: 'Delete Rule'
      back: 'Back'
    modal:
      addRule: 'Add Anti-Swear Rule'
      editRule: 'Editing Anti-Swear Rule'
      ruleName: 'Rule Name'
      words: 'Words'
      wordsPlaceholder: 'Words seperated by comma. (Use * for wildcard). Eg. word1, *word2*, *word3, word4*'
  logging:
    title: 'Logs Configuration'
    description: 'Configure logging channels and notifications for hub events'
    placeholder: 'Select a log type to configure'
    channelSelect: '#️⃣ Select a channel to send the logs'
    roleSelect: '🏓 Select the role to mention when a log is triggered.'
    config:
      title: 'Configure `{type}` Logs'
      description: |
        {arrow} Select a log channel and/or role to be pinged from the dropdown below.
        {arrow} You can also disable logging by using the button below.
      fields:
        channel: 'Channel'
        role: 'Role Mention'
    types:
      reports:
        label: 'Reports'
        description: 'Receive reports from users.'
      modLogs:
        label: 'Mod Logs'
        description: 'Log Moderation actions. (eg. blacklist, message deletes, etc.)'
      joinLeaves:
        label: 'Join/Leave'
        description: 'Log when a server joins or leaves this hub.'
      appeals:
        label: 'Appeals'
        description: 'Recieve appeals from blacklisted users/servers.'
      networkAlerts:
        label: 'Network Alerts'
        description: 'Recieve alerts about automatically blocked messages.'
      messageModeration:
        label: 'Message Moderation'
        description: 'Log message deletions and edits by moderators.'
  rules:
    title: 'Hub Rules Configuration'
    description: 'Manage rules and guidelines for your hub'
    noRules: "This hub has no rules configured yet. Let's add some!"
    maxRulesReached: 'Maximum number of rules ({max}) reached.'
    ruleExists: 'This rule already exists.'
    placeholder: 'Select a rule to edit or remove'
    modal:
      add:
        title: 'Add Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the rule text (max 1000 characters)'
      edit:
        title: 'Edit Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the new rule text (max 1000 characters)'
    buttons:
      add: 'Add Rule'
      edit: 'Edit Rule'
      delete: 'Delete Rule'
      back: 'Back'
    view:
      title: 'Rule {number}'
      select: 'Select an action for this rule'
  appealCooldown:
    errors:
      invalidCooldown: 'Please provide a valid cooldown duration.'
      tooShort: 'Cooldown must be atleast **1 hour** long.'
      tooLong: 'Cooldown cannot be longer than **1 year**.'
    success: '{emoji} Appeal cooldown has been set to **{hours}** hour(s).'
#Interaction and modal text
interactions:
  modals:
    warn:
      title: 'Warn User'
      reason:
        label: 'Reason'
        placeholder: 'Enter the reason for warning this user...'
  buttons:
    refresh: 'Refresh'
    cancel: 'Cancel'
    confirm: 'Confirm'
    back: 'Back'
    next: 'Next'
    finish: 'Finish'
    save: 'Save'
    delete: 'Delete'
    edit: 'Edit'
    add: 'Add'
    remove: 'Remove'
    view: 'View'
    close: 'Close'
  placeholders:
    selectOption: 'Select an option'
    selectChannel: 'Select a channel'
    selectRole: 'Select a role'
    selectUser: 'Select a user'
    selectServer: 'Select a server'
    enterText: 'Enter text here...'
    enterReason: 'Enter a reason...'
    enterDescription: 'Enter a description...'
#Moderation panel
modPanel:
  buttons:
    serverBanned: 'Server Banned'
    banServer: 'Ban Server'
  modals:
    blacklistUser: 'Blacklist User'
    blacklistServer: 'Blacklist Server'
#General UI text
ui:
  titles:
    error: 'Error'
    warning: 'Warning'
    success: 'Success'
    info: 'Information'
    confirmation: 'Confirmation'
  messages:
    loading: 'Loading...'
    processing: 'Processing your request...'
    pleaseWait: 'Please wait...'
    tryAgain: 'Please try again.'
    contactSupport: 'Please contact support if this issue persists.'
    operationCancelled: 'Operation cancelled.'
    operationCompleted: 'Operation completed successfully.'
    noDataAvailable: 'No data available.'
    permissionDenied: 'Permission denied.'
    invalidInput: 'Invalid input provided.'
    timeout: 'Operation timed out.'
    notFound: 'Not found.'
    alreadyExists: 'Already exists.'
    unavailable: 'Currently unavailable.'
#Message management commands
deleteMsg:
  description: 'Delete a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to delete'
  contextMenu:
    name: 'Delete Message'
  processing: '{emoji} Your request has been queued. Messages will be deleted shortly...'
  alreadyDeleted: '{emoji} This message is already deleted or is being deleted by another moderator.'
editMsg:
  description: 'Edit a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to edit'
    newContent:
      description: 'The new content for the message'
  contextMenu:
    name: 'Edit Message'
  modal:
    title: 'Edit Message'
    content:
      label: 'New Content'
      placeholder: 'Enter the new message content...'
  processing: '{emoji} Your request has been queued. Messages will be edited shortly...'
  alreadyEdited: '{emoji} This message is already being edited by another moderator.'
inbox:
  description: 'Check your inbox for latest important updates & announcements'
  title: '📬 InterChat Inbox'
  subtitle:
    new: 'Latest announcements and updates'
    older: 'Viewing older announcements'
  empty:
    title: '📬 All caught up!'
    description: "I'll let you know when there's more. But for now, there's only Chipi here: {emoji}"
  buttons:
    viewOlder: 'View Older'
    previous: 'Previous'
    next: 'Next'
  postedOn: 'Posted on {date}'
joinserver:
  description: 'Join a server or send a request to join a server through InterChat.'
  options:
    servername:
      description: 'The name of the server you want to join'
    messageorserverid:
      description: 'The message ID or server ID'
  errors:
    channelOnly: 'This command can only be used in a channel.'
    missingTarget: 'You must provide a message ID or server ID'
  success:
    inviteSent: "{emoji} I have DM'd you the invite link to the server!"
  request:
    title: 'Join Request'
    description: 'You requested to join the server `{serverName}` through InterChat. Here is the invite link:'
    broadcast: 'User `{username}` from `{guildName}` has requested to join this server. Do you want to accept them?'
  buttons:
    accept: 'Accept'
    reject: 'Reject'
  response:
    sent: "{emoji} Your request has been sent to the server. You will be DM'd the invite link if accepted."
    creating: '{emoji} This server does not have an invite link yet. Creating one...'
    dmSent: '{emoji} The invite link has been sent to the user.'
    dmFailed: '{emoji} The invite link could not be sent to the user. They may have DMs disabled.'
  status:
    accepted: 'Accepted by {username}'
    rejected: 'Rejected by {username}'
messageInfo:
  description: 'Get information about a message.'
  options:
    message:
      description: 'The message to get information about.'
  contextMenu:
    name: 'Message Info'
  errors:
    profileFetch: 'Failed to fetch user profile.'
connect:
  description: '🔗 Connect your channel to an InterChat hub'
  options:
    channel:
      description: 'The channel you want to connect to a hub'
    invite:
      description: 'The invite code of the private hub you want to join'
  errors:
    invalidIds: '{emoji} Invalid hub or channel ID.'
    channelNotFound: '{emoji} Channel not found or not a text channel.'
disconnect:
  description: '👋 Disconnect a channel from a hub'
#Staff commands
ban:
  description: '🔨 Ban users or servers from InterChat with comprehensive options'
  options:
    duration:
      description: 'Ban duration'
    reason:
      description: 'Reason for the ban (required)'
    user:
      description: 'User to ban (required for user bans)'
    serverId:
      description: 'Server ID to ban (required for server bans)'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to ban.'
unban:
  description: '🔓 Unban users or servers from InterChat'
  options:
    user:
      description: 'User to unban'
    serverId:
      description: 'Server ID to unban'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to unban.'
    invalidTarget: '{emoji} Invalid ban target. Please use the autocomplete to select a valid ban.'
    banNotFound: '{emoji} Ban not found.'
    serverBanNotFound: '{emoji} Server ban not found.'
    loadFailed: '{emoji} Failed to load ban information.'
achievements:
  description: "🏆 View your achievements or another user's achievements"
  options:
    user:
      description: 'The user to view achievements for (defaults to yourself)'
    view:
      description: 'Choose which achievements to view'
  title: "🏆 {username}'s Achievements"
  progress: '**Progress:** {unlocked}/{total} achievements unlocked'
  errors:
    userNotFound: 'User not found.'
achievement:
  settings:
    enabled: 'Achievement notifications are now **enabled**. You will receive notifications when you unlock new achievements!'
    disabled: 'Achievement notifications are now **disabled**. You will no longer receive notifications when you unlock achievements.'
profile:
  description: "View your profile or someone else's InterChat profile."
  options:
    user:
      description: 'The user to view the profile of.'
  errors:
    userNotFound: 'User not found.'
rank:
  description: 'Display user rank and statistics'
  options:
    user:
      description: 'The user to get the rank of'
  errors:
    createFailed: 'Failed to create rank card. Please try again later.'
#Userphone commands
call:
  description: '📞 [BETA] Start a call with another server'
  errors:
    skipFailed: 'Skip Failed'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
hangup:
  description: '📞 End the current call'
  callEnded: '{user} ended the call.'
  errors:
    error: 'Error'
    callFailed: 'Call Failed'
    guildOnly: '{emoji} This command can only be used in a server text channel.'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
skip:
  description: '[BETA] Skip the current call and find a new match'
  errors:
    error: 'Error'
    skipFailed: 'Skip Failed'
voteCommand:
  description: '✨ Voting perks and vote link.'
#Welcome message system for new servers
welcome:
  buttons:
    back: 'Back'
  calls:
    title: 'Setup Calls'
    description: 'Learn about InterChat calls - instant server-to-server connections!'
    commands: |
      ### Available Call Commands

      **{callCommand}** - Start a call with another server
      **{skipCommand}** - Skip current call and find a new match
      **{hangupCommand}** - End the current call
      **{leaderboardCommand}** - View call leaderboards
    examples:
      title: 'How to Use Calls'
      content: |
        1. Run `/call` in any text channel to start
        2. Wait to be matched with another server
        3. Chat with the other server in real-time
        4. Use `/skip` to find a different server
        5. Use `/hangup` when you're done chatting

        **Note:** Calls are in beta - for a more reliable experience, try InterChat Hubs!
  setup:
    title: '🏠 Setup Cross-Server Chat'
    description: 'Connect to hubs for persistent cross-server communities!'
    instructions: |
      ### Get Started with Hubs

      **{setupCommand}** - Guided setup to join your first hub
      **{connectCommand}** - Connect to a specific hub

      **What are Hubs?**
      Hubs are persistent chat communities where multiple servers connect and chat together 24/7. Unlike calls, hub messages stay even when you're offline!

      **Why Choose Hubs?**
      - Persistent connections that stay active
      - Join multiple themed communities
      - Advanced moderation and filtering
      - Custom welcome messages and rules
      - Thousands of servers already connected
    buttons:
      runSetup: 'Run Setup Now'
    errors:
      commandNotFound: '{emoji} Setup command not found. Please try running `/setup` manually.'
