rules:
  header: 'InterChat 规则'
  botRulesNote: '这些规则的制定是为了给每个人带来安全且愉快的体验。请仔细阅读并遵守以下规则：'
  rules: |
    1. **禁止仇恨言论或骚扰行为**
    -# > **包括：** 使用侮辱性言论或仇恨言论攻击他人，[及其他行为]({guidelines_link})。
    2. **禁止非法内容**
    -# > **包括：** 分享非法内容的链接，煽动暴力，[及其他行为]({guidelines_link})。
    3. **禁止重度成人或血腥暴力内容**
    -# > **包括：** 在InterChat发布血腥暴力内容，在非成人内容中心发布成人内容，[及其他行为]({guidelines_link})。
    4. **禁止发送垃圾信息或刷屏行为**
    -# > **包括：** 大量发送垃圾信息或使用工具进行刷屏，[及其他行为]({guidelines_link})。
    5. **禁止假冒或欺诈他人**
    -# > **包括：** 假冒InterChat的工作人员或中心的监管员，进行虚拟货币或NFT诈骗活动，[及其他行为]({guidelines_link})。
    6. **禁止剥削或虐待行为**
    -# > **包括：** 对未成年人进行诱骗或性剥削行为，还有分享、索取、敲诈或威胁行为，或煽动自残，[及其他行为]({guidelines_link})。
    7. **禁止分享恶意软件**
    -# > **包括：** 分享恶意软件、病毒、“免费Nitro”链接或有害脚本，[及其他行为]({guidelines_link})。

    请同意遵守 [Discord的服务条款](https://discord.com/terms) 以及 [社区准则](https://discord.com/guidelines)。查看 [完整规则列表]({guidelines_link})。
  welcome: |
    {emoji} Hey there, {user}! Welcome to InterChat! 🎉

    We're so excited to have you join our amazing community of servers connecting across Discord! Before you dive into chatting with people from around the world, let's take a quick moment to go over our friendly community guidelines.

    These simple rules help keep InterChat a warm, welcoming, and safe space for everyone to make new friends and share great conversations! ✨
  alreadyAccepted: "{emoji} Welcome back, {user}! You're all set to explore and chat with amazing communities. Have fun connecting with new friends! 🌟"
  continue: 继续
  accept: 接受
  decline: 拒绝
  agreementNote: 接受这些规则，则表明您在使用InterChat期间同意遵守所有规则。若违反规则可能会产生限制或禁令。
  hubAgreementNote: |
    接受这些规则，则表明您在此中心聊天期间同意遵守所有规则。若违反规则您可能会被此中心移除。

    ⚠️ **只有接受这些规则，您才能在此中心发送消息。**
  accepted: |
    {emoji} Fantastic! Welcome to the InterChat family! 🎉✨

    You're now part of an incredible community that connects thousands of servers and millions of people worldwide! We're thrilled to have you aboard this amazing journey.

    ### 🚀 Ready to Start Your Adventure?
    - **Discover Communities:** Use the [hub directory]({hubs_link}) to explore vibrant, active communities waiting for you
    - **Jump Into Conversations:** Join discussions that spark your interest, or create your own hub through our [dashboard]({dashboard_link})
    - **Try Quick Connections:** Use our `/call` command for instant one-on-one server connections - perfect for making new friends!
    - **Get Personalized Help:** Use `/setup` for a guided tour if you're feeling a bit lost

    ### 💝 We're Here for You!
    Feeling confused or need a helping hand? Don't worry - we've all been there! Join our friendly [support community]({support_invite}) where real people are excited to help you get started.

    **Love what we're doing?** Consider [supporting us]({donateLink}) to help InterChat grow and bring even more amazing features to connect communities worldwide! 🌍
  declined: |
    {emoji} Please take a moment to read and accept the community guidelines.

    These rules aren't optional—they're here to keep InterChat safe and enjoyable for everyone. Send another message or use any InterChat command to get started.

    ⚠️ Important: You won't be able to chat with other servers until you accept the rules. Take your time, but this step is required.
  hubAccepted: |
    {emoji}您已接受中心规则。现在可以在此中心开始聊天了！
  hubDeclined: |
    {emoji} 您已拒绝{hubName}的规则。
    -# ⚠️ **只有接受中心规则后，您才能在此中心发送消息。**
    -# 如重试，请在此中心另发送一条消息。
  noHubRules: 此中心尚未设置任何细则。但是 [InterChat通用规则]({rules_link}) 仍然适用。
  hubRules: 中心规则
  viewbotRules: '查看 Bot 规则'
vote:
  description: |
    帮助更多社区发现InterChat！您在top.gg的投票将:
    - 帮助其他人找到活跃社区
    - 为您解锁特殊功能
    - 支持我们的独立发展
  footer: '每隔12小时刷新一轮投票 • 感谢您对InterChat的支持！'
  button:
    label: '在top.gg进行投票'
  perks:
    moreComingSoon: '更多福利即将推出！关于福利，有任何建议请在[支持服务器]({support_invite})提出。'
  fields:
    currentStreak: 'Current Streak:'
    lastVote: 'Last Vote'
    voterPerks: 'Voter Perks'
    voteNow: '[Vote Now]({vote_url})!'
    perks:
      messageLength: 'Increased message length (2000 characters)'
      stickers: 'Send stickers in hubs'
      createHubs: 'Create up to 4 hubs'
      welcomeMessages: 'Custom welcome messages'
      voterRole: 'Voter role in support server'
      voterBadge: 'Exclusive voter badge in /profile'
  embed:
    title: 'Vote for InterChat'
network:
  accountTooNew: '{emoji} Hey {user}! Your Discord account is still pretty new, so we need to wait a little bit before you can send messages through InterChat. This helps keep our community safe! Please try again in a little while.'
  deleteSuccess: '{emoji}{user}发送的消息已从 __**{deleted}/{total}**__ 服务器中删除。'
  editInProgress: '{emoji}您的请求正在排队中。消息将很快编辑完成……'
  editInProgressError: '{emoji}此消息已被其他用户编辑。'
  emptyContent: '{emoji}消息内容不能为空。'
  newMessageContent: '新消息内容'
  editMessagePrompt: '{emoji}请使用此模式编辑您的消息。'
  editSuccess: '{emoji}{user}发送的消息已在__**{edited}／{total}**__服务器中完成编辑。'
  onboarding:
    welcome:
      title: '🎉 Welcome to InterChat!'
      description: |
        Welcome to InterChat! Let's get you set up with a personalized experience that matches your interests and helps you find the perfect communities to join.

        This quick setup will help us:
        - Learn about your interests and preferences
        - Find the best hubs for you to join
        - Connect you with like-minded people
        - Get you started with your first community

        Ready to begin your InterChat journey?
    embed:
      title: '🎉 Welcome to {hubName}!'
      description: |
        Congratulations! You've discovered an amazing, active community hub! 🌟

        Before you dive in and start making new friends, let's take a quick peek at our simple guidelines. They're designed to help everyone have the most fun and feel comfortable sharing their thoughts and experiences.

        Let's get started!
      footer: InterChat Network | Connecting Communities Worldwide 🌍
    inProgress: '{emoji}{channel}加入中心的流程正在进行中。您启动安装后，请等待安装完成或取消安装。'
blacklist:
  description: 'Mute/Ban a user or server from your hub.'
  success: '{emoji}**{name}**已成功列入黑名单！'
  removed: '{emoji}**{name}**已从黑名单中移除！'
  modal:
    reason:
      label: 原因
      placeholder: 列入黑名单的原因
    duration:
      label: 持续时间
      placeholder: '黑名单的持续时间。例如：一天、一周、一个月、一年。留空表示永久。'
  user:
    description: 'Mute/Ban a user from your hub.'
    options:
      user:
        description: 'The ID of the user to blacklist (get id using /messageinfo command)'
      hub:
        description: 'Hub to blacklist from'
    selectDuration: 'Select blacklist duration for {username}:'
    cannotBlacklistMod: '{emoji}您不能将监管员列入黑名单。请先移除他们的监管员身份。'
    alreadyBlacklisted: '{emoji}该用户已被列入黑名单。'
    easterEggs:
      blacklistBot: 你不能把我列入黑名单。
  server:
    description: 'Mute/Ban a server from your hub.'
    options:
      server:
        description: 'The server to blacklist'
      hub:
        description: 'Hub to blacklist from'
    selectDuration: 'Select blacklist duration for {serverName}:'
    alreadyBlacklisted: '{emoji}此服务器已被列入黑名单。'
    unknownError: 未能将{server}列入黑名单。向开发者咨询更多信息。
  list:
    description: 'List all blacklisted users and servers in your hub.'
    user: |
      **用户名称**：{id}
      **监管员：**：{moderator}
      **原因**：{reason}
      **期限**：{expires}
    server: |
      服务器名称：{id}
      监管员：{moderator}
      原因：{reason}
      期限：{expires}
msgInfo:
  buttons:
    message: 信息
    server: 服务器信息
    user: 用户信息
    report: 报告
  report:
    notEnabled: '{emoji}此中心未启用报告功能。'
    success: '{emoji}报告已成功提交。非常感谢！'
invite: |
  感谢您选择邀请InterChat！如果您有任何问题或需要帮助，我们随时在支持服务器上为您提供帮助！

  [{invite_emoji}“邀请链接”]({invite})[{support_emoji}“支持服务器”]({support})
connection:
  joinRequestsDisabled: '{emoji}此中心暂不开放加入申请。'
  notFound: '{emoji}连接无效。请检查频道名称或从显示的选项中选择。'
  channelNotFound: '{emoji}无法找到目标频道。请重新选择频道。'
  alreadyConnected: '{emoji}频道{channel}已连接中心。'
  switchChannel: '{emoji}请通过下方的选择菜单来选择目标频道：'
  switchCalled: '{emoji}频道切换已调用，请再次使用该指令查看新连接。'
  switchSuccess: '{emoji}频道已切换。您现在已从{channel}连接。'
  inviteRemoved: '{emoji}已移除此中心的服务器邀请。'
  setInviteError: '{emoji}无法创建邀请。请授予我在已连接频道的“创建邀请”权限。'
  inviteAdded: '{emoji}邀请已添加。其他人现在可通过使用“应用程序＞信息／报告”指令和“／加入服务器”指令来加入此服务器。'
  emColorInvalid: '{emoji}颜色无效。请确保您输入了有效的十六进制颜色代码。'
  emColorChange: '{emoji}嵌入颜色成功{action}'
  embed:
    title: 连接详情
    fields:
      hub: 中心
      channel: 频道
      invite: 邀请
      connected: 已连接
      emColor: 嵌入颜色
      compact: 简洁模式
    footer: 使用下方的下拉列表管理您的连接。
  selects:
    placeholder: '🛠️ 选择一个选项来编辑此连接'
  unpaused:
    desc: |
      {tick_emoji}已恢复连接

      {channel}的连接已恢复！来自此中心的信息将开始进入此频道，并且您可以再次发送信息到中心。
    tips: |
      提示：使用{pause_cmd}来中断连接，或使用{edit_cmd}来设置嵌入颜色、邀请到您的服务器等。
  paused:
    desc: |
      {clock_emoji}已中断连接
      {channel}的连接已中断！来自此中心的信息将不再进入此频道，并且您的信息将不会发送出去。
    tips: |
      提示：使用{unpause_cmd}来恢复连接，或使用{leave_cmd}来永久停收信息。
hub:
  notFound: "{emoji} Hmm, we couldn't find that hub. Double-check the name you entered, or try browsing available hubs with [hub directory]({hubs_link}) to discover some amazing communities! 🔍"
  notFound_mod: '{emoji}无法找到中心。请确保您输入了正确的中心名字以及您是此中心的所有者或监管员。'
  notManager: '{emoji}您必须是中心管理员才能执行此操作。'
  notModerator: '{emoji}您需要成为中心监管员才能执行此操作。'
  notPrivate: '{emoji}此中心非私有。'
  notOwner: '{emoji}只有此中心的所有者可以执行此操作。'
  alreadyJoined: '{emoji} 您已从 {channel}加入另一个中心**{hub}** ！使用“/断开连接”后再次尝试使用“/连接”。'
  invalidChannel: '{emoji}频道无效。仅支持文本频道和对话线程频道！'
  invalidImgurUrl: '{emoji}图标或标语的图片网址无效。请确保您输入了有效的Imgur图片网址且该网址不是图库或相册。'
  join:
    success: |
      🎉 **Fantastic! Welcome to {hub}!** 🎉

      You've successfully connected {channel} to this amazing community! You can now chat with members from servers all around the world right from this channel. How exciting is that? ✨

      **🚀 Ready to explore?**
      - Use `/connection` to customize your connection and make it uniquely yours
      - Use `/disconnect` if you ever need to leave this hub
      - Use `/connection edit` to switch to a different channel anytime

      **💝 Pro tip:** Say hello and introduce yourself - everyone loves meeting new friends! Have fun connecting with the community! 🌟
    nsfwChannelSfwHub: '{emoji} NSFW channels cannot connect to SFW hubs. {channel} is marked as NSFW, but **{hub}** is a safe-for-work hub. Please use a non-NSFW channel or find an NSFW hub instead.'
    sfwChannelNsfwHub: '{emoji} SFW channels cannot connect to NSFW hubs. {channel} is not marked as NSFW, but **{hub}** is an adult content hub. Please use an NSFW channel or find a SFW hub instead.'
  servers:
    total: '目前已连接的服务器：{from}-­­­­­­­­­­{to}­／{total}'
    noConnections: '{emoji}尚未有服务器加入此中心。使用“/连接”加入此中心。'
    notConnected: "{emoji}目标服务器未连接{hub}。"
    connectionInfo: |
      服务器名称：{serverId}
      频道：#{channelName}“({channelId})”
      加入时间：{joinedAt}
      邀请：{invite}
      已连接：{connected}
  blockwords:
    deleted: '{emoji}过滤规则已成功删除！'
    notFound: '{emoji}未找到过滤规则。'
    maxRules: '{emoji}已达到此中心的过滤规则最大数量（2条）。请删除旧规则后新增。'
    configure: '{rule}规则配置'
    actionsUpdated: '{emoji}已更新此规则将进行的操作。新操作：{actions}'
    selectRuleToEdit: 选择一条规则来编辑关键词／操作
    listDescription: |
      {emoji}过滤规则
      此中心已配置{totalRules}／2条过滤规则。
    listFooter: 选择一条规则并用菜单查看详情。
    ruleDescription: |
      {emoji}编辑规则：{ruleName}{words}
    ruleFooter: '点击下方按钮来编辑关键词或规则名称！'
    actionSelectPlaceholder: '选择此规则应该执行的操作。'
    embedFields:
      noActions: '{emoji}无配置！请通过下方菜单进行配置。'
      actionsName: '配置操作：'
      actionsValue: '{actions}'
    modal:
      addRule: 添加过滤规则
      editingRule: 编辑过滤规则
      ruleNameLabel: 规则名称
      wordsLabel: '关键词'
      wordsPlaceholder: '用逗号分隔关键词。(使用 * 作为通配符)。例如：关键词一，*关键词二*，*关键词三，关键词四*'
    validating: '{emoji}正在校验过滤规则……'
    noRules: |
      ### {emoji} 让我们制定一些过滤规则吧！
      通过“添加规则”按钮进行创建。
  create:
    modal:
      title: 创建中心
      name:
        label: 中心名称
        placeholder: 为您的中心输入一个名称。
      description:
        label: 描述
        placeholder: 为您的中心输入描述。
      icon:
        label: 图标网址
        placeholder: 输入一个Imgur图片网址。
      banner:
        label: 标语网址
        placeholder: 输入一个Imgur图片网址。
    maxHubs: '{emoji} 已达到可以创建中心的最大数量({maxHubs})。请删除旧中心后新建。[为InterChat投票]({voteUrl}) 可以创建更多中心。'
    invalidName: '{emoji}中心名称无效。名称不能包含“discord”、“clyde”或“＼”。请另选名称。'
    nameTaken: '{emoji}此中心名称已被占用。请另选名称。'
    success: |
      ## 中心已创建！默认情况为__private__。
      使用“/编辑中心 中心：{name}”来自定义您的中心。请按照以下步骤开始操作：
      ### 接下来的步骤：
      1. **创建邀请：**
      > 使用“/创建中心邀请”来创建让其他人加入的邀请。
      2. **关联频道：**
      > 使用“/连接”同时**附上之前生成的邀请链接**，将一个频道关联到此中心并开始聊天。
      3. **设置中心：**（推荐）
      > 使用“/中心配置设定”、“/中心配置记录”和“/中心过滤规则配置”来设置此中心。
      4. **添加监管员：**
      > 使用“/添加中心监管员”来为此中心添加监管员。
      5. **自定义中心：**
      > 使用“/编辑中心”来更改此中心的图标、标语和描述。
      6. **公开中心：**
      > 使用“/中心可见”将中心设为公开，让其他人不用邀请也可以浏览并加入。(可选)

      如果您有任何问题或需要帮助，请随时在 [支持服务器]({support_invite})中提问。请考虑一下 [捐赠]({donateLink})来支持开发成本。
  delete:
    confirm: 您确定要删除{hub}吗？该行动无法撤回。所有已连接的服务器、邀请和消息数据都将从此中心删除。
    ownerOnly: '{emoji}只有此中心的所有者可以删除它。'
    success: '{emoji}中心{hub}已被删除。'
    cancelled: '{emoji}删除中心已取消。'
  invite:
    create:
      success: |
        ### 邀请已创建！

        您的邀请已成功创建。其他人现在可以通过使用“/连接”指令加入此中心。

        - **加入方式：**“/连接邀请：{inviteCode}”
        - **查看邀请：**“/中心邀请列表”
        - **到期时间：**{expiry}
        - **使用次数**：无限

        **注意：**您可以随时使用“/中心邀请撤回{inviteCode}”来撤回此邀请。
    revoke:
      invalidCode: '{emoji}邀请码无效。请确保您输入了有效的邀请码。'
      success: '{emoji}邀请{inviteCode}撤回。'
    list:
      title: '邀请码'
      noInvites: '{emoji}此中心尚未收到邀请。使用“／创建中心邀请”来创建邀请。'
      notPrivate: '{emoji}只有私人中心才能收到邀请。使用“／编辑编辑”将此中心设置为私有。'
  joined:
    noJoinedHubs: '{emoji} This server has not joined any hubs yet. Use [hub directory]({hubs_link}) to view a list of hubs.'
    joinedHubs: 此服务器已连接**{total}**中心(群)。使用“/断开连接”离开中心。
  leave:
    noHub: '{emoji}该频道无效或尚未加入任何中心。'
    confirm: 您确定想要从{channel}离开{hub}吗？此中心将不再向此服务器发送任何消息。
    confirmFooter: 请在10秒内使用下面的按钮进行确认。
    success: '{emoji}从{channel}离开中心。此中心将不再向此服务器发送任何消息。您可以使用“/连接”重新加入。'
  moderator:
    noModerators: '{emoji}此中心尚未有监管员。使用“／添加中心监管员”添加监管员。'
    add:
      success: '{emoji}{user}已被添加为{position}级监管员。'
      alreadyModerator: '{emoji}{user}已成为监管员。'
    remove:
      success: '{emoji}{user}已被移除监管员身份。'
      notModerator: '{emoji}{user}不是监管员。'
      notOwner: '{emoji}只有此中心的所有者可以移除管理员。'
    update:
      success: "{emoji}{user}的级别已更新为{position}。"
      notModerator: '{emoji}{user}不是监管员。'
      notAllowed: "{emoji}只有中心管理员可以更新监管员级别。"
      notOwner: "{emoji}只有此中心的所有者可以更新管理员的级别。"
  manage:
    dashboardTip: "**🛠️ 新控制面板：** 界面优化，功能增多！在 [您中心的控制面板页]({url})试试看。"
    enterImgurUrl: 输入一个有效的Imgur图片网址，且该网址不是图库或相册。
    icon:
      changed: 中心图标已成功更改。
      modal:
        title: 编辑图标
        label: 图标网址
      selects:
        label: 编辑图标
        description: 更改此中心的图标。
    description:
      changed: 中心的描述成功更改。
      modal:
        title: 编辑描述
        label: 描述
        placeholder: 输入此中心的描述。
      selects:
        label: 更改描述
        description: 更改此中心的描述。
    banner:
      changed: 中心的标语已成功更改。
      removed: 中心的标语已被成功移除。
      modal:
        title: 编辑标语
        label: 标语网址
      selects:
        label: 编辑标语
        description: 更改此中心的标语。
    visibility:
      success: '{emoji}中心可见已成功更改为{visibility}。'
      selects:
        label: 可见性设置
        description: 将中心设为公开或私有。
    toggleLock:
      selects:
        label: '锁定／解锁中心'
        description: '关闭或打开此中心聊天区'
      confirmation: '中心聊天区现在为{status}。'
      announcementTitle: '中心聊天区现在为{status}。'
      announcementDescription:
        locked: '只有监管员可以发送信息。'
        unlocked: '所有人都可以发送信息。'
    toggleNsfw:
      modal:
        title: 'Toggle NSFW Status'
        label: 'NSFW Status'
        description: 'Mark this hub as containing adult content.'
      selects:
        label: 'Toggle NSFW Status'
        description: 'Mark hub as NSFW or SFW content'
      confirmation: 'Hub content rating is now {status}.'
      announcementTitle: 'Hub content rating changed to {status}'
      announcementDescription:
        nsfw: 'This hub now contains adult content. Only NSFW Discord channels can connect to this hub.'
        sfw: 'This hub is now safe for work. All channels can connect to this hub.'
    setNsfw:
      success: '{emoji} **{hub}** has been successfully marked as **{status}**.'
      announcement: "{emoji} **Hub Content Rating Changed**\n\nThis hub is now marked as **{status}**.\n\n{description}"
    nsfwAlreadySet: '{emoji} **{hub}** is already marked as **{status}**.'
    embed:
      visibility: '可见性'
      connections: '连接'
      chatsLocked: '聊天区已关闭'
      blacklists: '黑名单'
      total: '总数'
      users: '用户'
      servers: '服务器'
      hubStats: '中心统计数据'
      moderators: '监管员'
      owner: '所有者'
    logs:
      title: 配置记录
      reset: '{emoji}成功重置“{type}”记录的配置记录。'
      roleSuccess: '{emoji}“{type}”记录现在将通知{role}！'
      roleRemoved: '{emoji}“{type}”记录将不再通知对象。'
      channelSuccess: '{emoji}“{type}”记录从现在开始将被发送至{channel}！'
      channelSelect: '#️⃣ 选择一个频道发送记录'
      roleSelect: '🏓 选择触发记录时通知的对象。'
      reportChannelFirst: '{emoji}请先设置一个记录频道。'
      config:
        title: '“{type}”配置记录'
        description: |
          {arrow}从下方的下拉列表中选择要通知的记录频道和／或对象。
          {arrow}您也可以通过下方按钮来停用记录功能。
        fields:
          channel: 频道
          role: 通知对象
      reports:
        label: 报告
        description: 从用户处收到报告。
      modLogs:
        label: 监管员记录
        description: 监管操作记录。（例如：列入黑名单、删除信息等等）
      joinLeaves:
        label: 加入／离开
        description: 当服务器加入或离开此中心时记录下来。
      appeals:
        label: 上诉
        description: 从被列入黑名单的用户／服务器处受理上诉。
      networkAlerts:
        label: 网络警报
        description: 受理关于自动屏蔽信息的警报。
      messageModeration:
        label: Message Moderation
        description: Log message deletions and edits by moderators.
      messageDeletions:
        label: Message Deletions
        description: Log when messages are deleted from the hub.
      messageEdits:
        label: Message Edits
        description: Log when messages are edited in the hub.
  transfer:
    invalidUser: '{emoji}未找到指定用户。'
    selfTransfer: '{emoji}您不能把所有权转给自己。'
    botUser: '{emoji}您不能把所有权转给机器人。'
    confirm: '您确定要把**{hub}**的所有权转给{newOwner}吗？您将降为管理员身份。'
    cancelled: '{emoji}转移中心已取消。'
    error: '{emoji}转移中心所有权时发生了错误。'
    success: '{emoji}成功把{hub}的所有权转给{newOwner}。您已被添加为管理员。'
    timeout: '{emoji}转移中心已超时。'
  rules:
    noRules: "{emoji} 此中心尚未配置规则。添加一些规则吧！"
    list: "### {emoji} 中心规则\n{rules}"
    maxRulesReached: '{emoji}已达到规则最大数量({max}) 。'
    ruleExists: '{emoji}此规则已存在。'
    selectedRule: '选择规则{number}'
    modal:
      add:
        title: 添加中心规则
        label: 规则内容
        placeholder: 输入规则内容（最多一千字）
      edit:
        title: 编辑中心规则
        label: 规则内容
        placeholder: 输入新规则内容（最多一千字）
    select:
      placeholder: 选择要编辑或删除的规则
      option:
        label: 规则{number}
    buttons:
      add: 添加规则
      edit: 编辑规则
      delete: 删除规则
      back: 返回
    success:
      add: '{emoji}规则已成功添加！'
      edit: '{emoji}规则已成功更新！'
      delete: '{emoji}规则已成功删除！'
    view:
      title: '规则{number}'
      select: 选择此规则的操作
  welcome:
    set: '{emoji}欢迎信息已成功更新！'
    removed: '{emoji}欢迎信息已移除。'
    voterOnly: '{emoji}自定义欢迎信息是属于为我们投票的人独有的福利！去投票解锁此功能。'
    placeholder: |
      欢迎 {user} 从 {serverName} 来到 {hubName}! 🎉
      成员： {memberCount}，中心：{totalConnections}！
report:
  modal:
    title: 报告详情
    other:
      label: 报告详情
      placeholder: 报告中的一份详细描述。
    bug:
      input1:
        label: 故障详情
        placeholder: 例如，“／帮助” 指令频繁出现互动失败的情况……
      input2:
        label: 描述详情（可选）
        placeholder: 要做的步骤。例如：1.运行／帮助 2.等待5秒……
  reasons:
    spam: 垃圾或过多消息
    advertising: 垃圾推广
    nsfw: 成人或不恰当内容
    harassment: 骚扰或欺凌
    hate_speech: 侮辱性或仇恨言论
    scam: 诈骗、欺诈或网络钓鱼
    illegal: 非法的内容或活动
    personal_info: 泄露个人/私人信息
    impersonation: 仿冒他人
    breaks_hub_rules: 违反中心规则
    trolling: 拖钓或扰乱
    misinformation: 错误或误导性信息
    gore_violence: 血腥或极端暴力
    raid_organizing: 组织袭击
    underage: 未达年龄限制的用户或内容
  dropdown:
    placeholder: 选择您举报的原因
  submitted: '{emoji}报告已成功提交。加入{support_command}获取详情。谢谢！'
  errors:
    noReasonSelected: '{emoji} No reason selected. Please try again.'
    hubNotFound: '{emoji} Hub not found. Please try again.'
  description: 'Report a message'
  options:
    message:
      description: 'The message to report'
  contextMenu:
    name: 'Report Message'
  selectReason: '{emoji} Please select a reason for your report:'
  bug:
    title: 故障报告
    affected: 受影响的部分
    description: 请选择出现问题的程序部分。
language:
  set: 语言设置！现在将用{lang}进行回应。
errors:
  messageNotSentOrExpired: '{emoji}此消息未在中心发送，已过期，或者您没有执行此操作的权限。'
  notYourAction: "{emoji}抱歉，您无法执行此操作。请由本人行此指令。"
  notMessageAuthor: '{emoji}您不是这条信息的发布者。'
  commandError: |
    {emoji} Oops! Something unexpected happened while running this command. Don't worry - this isn't your fault!

    We've automatically logged this issue and our team will take a look. If this keeps happening, please drop by our friendly [support server]({support_invite}) and let us know about this error ID - we're always happy to help! 🤗

    **Error ID:**
    ```{errorId}```
  mustVote: 请[投票](https://top.gg/bot/769921109209907241/vote)给InterChat以使用此指令，非常感谢您的支持！
  inviteLinks: '{emoji}您不能向此中心发送邀请链接。请改为在“／连接”处设置邀请！中心监管员可以用“／中心编辑设定”来配置'
  invalidLangCode: '{emoji}语言模式无效。请确保您输入了正确的[语言模式](https://cloud.google.com/translate/docs/languages)。'
  modalError: '{emoji} There was an error showing the modal. Please try again.'
  unknownServer: '{emoji}未知服务器。请确保您输入了正确的服务器名称。'
  unknownNetworkMessage: '{emoji}未知信息。如果该内容在过去一分钟内已发送过，请再等待几秒钟后重试。'
  userNotFound: '{emoji}找不到该用户。请尝试输入他们的名称。'
  blacklisted: '{emoji}您或者此服务器已被此中心{hub}列入黑名单。'
  userBlacklisted: '{emoji}您已被此中心列入黑名单。'
  serverBlacklisted: '{emoji}此服务器已被此中心列入黑名单。'
  serverNotBlacklisted: '{emoji}输入的服务器未被列入黑名单。'
  userNotBlacklisted: '{emoji}输入的用户未被列入黑名单。'
  missingPermissions: '{emoji}您没有执行此操作的以下权限：{permissions}'
  botMissingPermissions: '{emoji}请授予我以下权限以继续：{permissions}'
  unknown: '{emoji}出现一个未知错误。请稍后重试或通过我们的[支持服务器]({support_invite})来联系我们。'
  notUsable: '{emoji}这已无法再用。'
  cooldown: '{emoji}您处于冷却时间内。请等到{time}后再尝试。'
  serverNameInappropriate: '{emoji}您的服务器名称包含不当内容。请更改后再加入中心。'
  banned: |
    {emoji}由于违反了我们的[准则](https://interchat.tech/guidelines)，您已被禁止使用Interchat。
    如果您认为可以上诉，请在[支持服务器]({support_invite})创建票证。
  commandLoadingError: 'There was an error loading the command. Please try again later.'
  errorLoadingHubs: 'Error Loading Hubs'
  errorShowingHubSelection: 'There was an error showing the hub selection screen. Please try again.'
  connectNotFound: 'Connect command not found. Please try again.'
config:
  setInvite:
    success: |
      ### {emoji} 邀请链接设置
      - 当用户使用“／加入服务器”指令时，将自动使用您的服务器邀请链接。
      - 该链接同时会在“／服务器排行榜”中显示。
    removed: '{emoji}邀请已成功移除！'
    invalid: '{emoji}邀请无效。请确保您输入了有效的邀请链接。例如：“https://discord.gg/discord”'
    notFromServer: '{emoji}此邀请不是来自此服务器。'
badges:
  shown: '{emoji}现在您的徽章将在消息中显示。'
  hidden: '{emoji}现在您的徽章将在消息中隐藏。'
  command:
    description: '🏅 配置您的首选显示徽章'
    options:
      show:
        name: '显示'
        description: '是否在消息中显示或隐藏您的徽章'
  list:
    developer: 'InterChat的核心开发者'
    staff: 'InterChat的工作人员'
    translator: 'InterChat的译者'
    voter: '在过去 12 小时内投票给InterChat'
global:
  webhookNoLongerExists: '{emoji}此频道的网络回调已不存在。若要继续使用 InterChat，请使用“／恢复连接”指令重新创建网络回调。'
  noReason: 未提供原因。
  noDesc: 无说明。
  version: InterChat版本{version}
  loading: '{emoji}我正在处理您的请求，请稍候……'
  reportOptionMoved: '{emoji}此选项已移动！如向中心监管员报告，请使用更新的“应用程序＞信息／报告”指令。如直接向InterChat工作人员报告，只需登录[支持服务器]({support_invite})并创建一张带有证明的票证。'
  private: '私人'
  public: '公开'
  yes: '是'
  no: '否'
  cancelled: '{emoji}已取消。未进行任何更改。'
  #Common button labels
  buttons:
    openInbox: 'Open Inbox'
    modPanel: 'Mod Panel'
    joinServer: 'Join Server'
    disconnect: 'Disconnect'
    reconnect: 'Reconnect'
    editRule: 'Edit Rule'
    #Setup buttons
    joinPopularHub: 'Join Popular Hub'
    createNewHub: 'Create New Hub'
    finishSetup: 'Finish Setup'
    findMoreHubs: 'Find More Hubs'
    supportServer: 'Support Server'
    viewChannel: 'View Channel'
    hubDirectory: 'Hub Directory'
    learnMore: 'Learn More'
    connectToHub: 'Connect to a Hub'
    #Common buttons for calls and other features
    createYourHub: 'Create Your Hub'
    #Call buttons
    cancelCall: 'Cancel Call'
    newCall: 'New Call'
    #Leaderboard
    userLeaderboard: 'User Leaderboard'
    serverLeaderboard: 'Server Leaderboard'
  #Common modal titles and labels
  modals:
    editConnection:
      title: 'Edit Connection'
      channelName:
        label: 'Channel Name'
        placeholder: 'Enter a custom name for this channel'
      profanityFilter:
        label: 'Profanity Filter'
      compact:
        label: 'Compact Mode'
    hubCreation:
      title: 'Create New Hub'
      name:
        label: 'Hub Name'
        placeholder: 'e.g., Gaming Community, Art Gallery'
      description:
        label: 'Description'
        placeholder: 'What is this hub about?'
    messageInfo:
      title: 'Message Information'
    editRule:
      title: 'Edit Rule'
      content:
        label: 'Rule Content'
        placeholder: 'Enter the rule content...'
  #Common messages and responses
  messages:
    selectChannel: 'Select a channel'
    selectHub: 'Select a hub'
    noHubsAvailable: 'No hubs available'
    hubNotFound: 'Hub not found'
    channelNotFound: 'Channel not found'
    connectionNotFound: 'Connection not found'
    invalidSelection: 'Invalid selection'
    operationCancelled: 'Operation cancelled'
    setupComplete: 'Setup complete!'
    connectionEstablished: 'Connection established'
    connectionRemoved: 'Connection removed'
    settingsUpdated: 'Settings updated'
    ruleAdded: 'Rule added'
    ruleUpdated: 'Rule updated'
    ruleDeleted: 'Rule deleted'
    #Leaderboard messages
    noDataAvailable: 'No data available'
    loadingData: 'Loading data...'
    #Connection status
    connected: 'Connected'
    disconnected: 'Disconnected'
    paused: 'Paused'
    #Hub visibility
    publicHub: 'Public Hub'
    privateHub: 'Private Hub'
#Leaderboard
leaderboard:
  title: 'Global Message Leaderboard'
  description: 'Resets every month. Send a message in any hub to get on it!'
warn:
  description: 'Warn a user in your hub'
  options:
    user:
      description: 'The user to warn'
    hub:
      description: 'The hub to warn in'
    reason:
      description: 'Reason for the warning'
  errors:
    cannotWarnSelf: '{emoji} You cannot warn yourself.'
  modal:
    title: 用户警告
    reason:
      label: 原因
      placeholder: 输入警告此用户的原因……
  success: |
    {emoji} **{name}**警告成功。

    -# 下次在中心发送消息时，他们将收到最新警告的通知。请勿同时发出多个警告。
  dm:
    title: '{emoji}警告通知'
    description: '您在中心{hubName}已被警告'
  log:
    title: '{emoji}被警告用户'
    description: |
      {arrow} **用户：** {user} ({userId})
      {arrow} **监管员：** {moderator} ({modId})
      {arrow} **原因：** {reason}
    footer: '警告来自：{moderator}'
calls:
  connected:
    title: "You're Connected! 🎉"
    description: "You've been matched with another awesome server! Say hello and start chatting - this is where the magic happens! ✨"
    instructions: 'Use `/hangup` to end the call • `/skip` to find a different server'
    serverInfo: '**Connected to:** {serverName} ({memberCount} members)'
    duration: '**Call Duration:** {duration}'
    messages: '**Messages Exchanged:** {count}'
  waiting:
    title: 'Finding Your Perfect Match'
    description: 'Added to call queue. Waiting for another server to join...'
  failed:
    title: 'Call Failed'
    description: "Check you're not already in a call and try again in a few moments."
    reasons:
      alreadyInCall: 'This channel is already in a call!'
      alreadyInQueue: 'This channel is already in the call queue!'
      webhookFailed: 'Failed to create webhook. Please try again later.'
      channelInvalid: 'Cannot skip call - invalid channel'
  cancelled:
    title: 'Call Cancelled'
    description: 'Call queue exited. Use `/call` to start a new call.'
    queueExit: 'You have been removed from the call queue'
  ended:
    title: 'Call Ended'
    description: 'Thanks for chatting! Hope you made some new friends! 🌟'
    stats: |
      **Call Summary:**
      • Duration: {duration}
      • Messages: {messages}
      • Server: {serverName}
    ratePrompt: 'How was your call experience?'
  skip:
    title: 'Finding New Call'
    description: 'Previous call ended • Waiting for another server • Use `/hangup` to cancel'
    newConnected:
      title: 'New Call Connected!'
      description: "You've been connected to a different server • Use `/hangup` to end"
    error: 'Unable to skip call. Please try again.'
  hangup:
    confirm: 'Are you sure you want to end this call?'
    success: 'Call ended successfully. Thanks for chatting!'
    queueOnly: 'Removed from call queue.'
  buttons:
    endCall: 'End Call'
    skipServer: 'Skip Server'
    skipAgain: 'Skip Again'
    cancelCall: 'Cancel Call'
    newCall: 'New Call'
    exploreHubs: 'Explore Hubs'
    browseAllHubs: 'Browse All Hubs'
    ratePositive: 'Good Call 👍'
    rateNegative: 'Poor Call 👎'
    reportCall: 'Report Call'
  hubs:
    promotion:
      title: '🌟 Discover InterChat Hubs!'
      description: 'Calls are in beta. For a more reliable experience, try InterChat Hubs - our main feature for connecting servers!'
    benefits:
      title: 'Why Choose Hubs?'
      description: 'Hubs offer a more reliable and feature-rich experience than calls:'
      list: |
        • **Persistent Connections** - Messages stay even when you're offline
        • **Multiple Communities** - Join various themed hubs or create your own
        • **Advanced Moderation** - Content filtering, anti-spam, and more
        • **Rich Features** - Custom welcome messages, rules, and settings
        • **Active Communities** - Thousands of servers already connected
    main:
      title: 'InterChat Hubs'
      description: 'Hubs are the main feature of InterChat, connecting servers in persistent chat communities'
  system:
    callStart: |
      {emoji} **Your Call is Connected!** Say hello! 🎉
      > - Say hello and start chatting with the other server!
      > - Use `/hangup` when you're ready to end the call
      > - Remember to keep things friendly and follow our [community guidelines]({guidelines})
  rating:
    success: 'Thanks for rating! Your **{type}** feedback has been recorded for {count} participant{plural}.'
    alreadyRated: '{emoji} You have already rated this call.'
    invalidButton: '{emoji} Invalid rating button. Please try again.'
    noCallData: '{emoji} Unable to find call data. The call might have ended too long ago.'
    noParticipants: '{emoji} Unable to find participants from the other channel.'
  report:
    prompt: '{emoji} Please select a reason for your report:'
    invalidButton: '{emoji} Invalid report button. Please try again.'
  leaderboard:
    title: 'Global Calls Leaderboard'
    description: 'Shows data from this month'
    noData: 'No data available.'
    userTab: 'User Leaderboard'
    serverTab: 'Server Leaderboard'
  errors:
    guildOnly: 'This command can only be used in a server text channel.'
#Command descriptions and help text
commands:
  about:
    title: 'About InterChat'
    description: 'Learn more about InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    support_text: 'Need help? Join our support server for assistance!'
    features:
      title: 'Features'
      list: |
        - Connect with other servers for active cross-server discussions
        - Messages flow naturally between servers in real-time
        - Build engaged topic-focused communities
        - Moderation tools to keep discussions healthy
        - Visual dashboard to manage your hubs, servers, and settings
    buttons:
      vote: 'Vote on top.gg'
      invite: 'Invite InterChat'
      dashboard: 'Visit Dashboard'
      support: 'Join Support Server'
      credits: 'View Credits'
    credits:
      title: 'Credits'
      developers: 'Developers'
      staff: 'Staff'
      translators: 'Translators'
      mentions: 'Special Mentions'
      mascot: 'Mascot'
      top_voter: 'Top Voter'
      footer: 'Version {version}'
    sections:
      invite: 'Invite InterChat to your server'
      dashboard: 'Visit the InterChat dashboard'
      support: 'Join our support server'
      credits: 'View the InterChat credits'
    errors:
      serverOnly: 'This command can only be used in a server.'
  help:
    description: '📚 Explore InterChat commands with our new help system'
    options:
      command:
        description: 'The command to get info about'
      category:
        description: 'View commands by category'
    errors:
      categoryNotFound: '{emoji} Category not found.'
      commandNotFound: '{emoji} Command `{command}` not found.'
      showingCategory: '{emoji} An error occurred while showing the category.'
      showingCommand: '{emoji} An error occurred while showing the command help.'
      showingMenu: '{emoji} An error occurred while showing the help menu.'
      showingSearch: '{emoji} An error occurred while showing the search interface.'
  setup:
    description: 'Setup InterChat in your server'
    errors:
      serverOnly: 'This command can only be used in a server.'
      missingPermissions: |
        I need the following permissions to work properly:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please give me these permissions and try again!
        Need help? [Join our support server]({supportInvite})
      setupError: 'There was an error starting the setup process. Please try again later.'
      completionError: 'There was an error completing the setup. Please try again or contact support if the issue persists.'
      channelNotSelected: 'No channel was selected. Please try again.'
      invalidChannelType: 'Please select a text or thread channel. Voice channels, forums, and other channel types are not supported.'
      missingChannelPermissions: |
        I need the following permissions in {channel}:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please update the channel permissions and try again!
      channelAlreadyConnected: 'This channel is already connected to the hub "{hubName}". Please select a different channel.'
      channelNotFound: 'Selected channel no longer exists. Please run the setup command again.'
      hubNotFound: 'This hub no longer exists. Please choose another one.'
      commandLoadingError: 'Failed to load commands. Please try again or join our support server for help.'
      interactionError: '{emoji} Oops! Something went a bit wonky there. No worries though - just give it another try! If this keeps happening, our friendly support team is always here to help.'
      userMismatch: 'This setup is for another user.'
      serverRequired: 'You must be in a server to use this.'
      timeout: "No worries! The setup just timed out while waiting for your response. When you're ready to continue your InterChat journey, just run `/setup` again and we'll pick up right where we left off!"
      noAvailableHubs: 'Your server is already connected to all available popular hubs! Try creating a new hub instead.'
      hubCreationFailed: 'Failed to create hub. Please try again.'
      validationError: 'Invalid hub data provided. Please try again.'
    welcome:
      title: '🎉 Welcome to InterChat Setup!'
      description: |
        Hey! Let's get your server connected to InterChat.

        This setup will guide you through everything you need:

        📍 Select a channel for the chat

        🏠 Join or create a hub (your community space)

        ⚙️ Finish setup to start chatting

        What's a Hub? It's a shared space where servers connect and chat together. Simple as that.

        Let's get this set up and running. 🚀
    channelSelection:
      title: '📍 Step 1: Choose Your Perfect Channel'
      description: "Let's pick the channel where all the exciting InterChat conversations will happen! This can be any text channel in your server - maybe create a special one just for this?"
      placeholder: 'Select a channel'
      tips:
        title: '💡 Helpful Tips for Success'
        content: |
          - **Create something special:** Try naming it `#interchat`, `#global-chat`, or `#world-chat`
          - **Think about visibility:** Make sure members who want to join the fun can see this channel
          - **Room to grow:** You can always connect more channels to different communities later
          - **Keep it organized:** A dedicated channel helps keep conversations flowing smoothly
    hubChoice:
      title: 'InterChat Setup (2/4)'
      description: "Great! Messages will appear in {channel}. Now, let's connect to a hub!"
      whatIsHub:
        title: 'What is a Hub?'
        description: "A hub is InterChat's main feature - a shared chat space where multiple servers can talk together. Hubs are persistent communities that stay connected 24/7, unlike temporary calls."
      popularHubs:
        title: 'Popular Hubs'
        description: |
          - Join thriving active communities with thousands of users
          - Start chatting immediately with other servers
          - Perfect for new users to experience InterChat
          - No additional setup required - just connect and chat!
      createHub:
        title: 'Create Your Own Hub'
        description: |
          - Start your own community themed around your interests
          - Full control over settings, moderation, and features
          - Invite specific servers to create a private network
          - Set custom rules, welcome messages, and more
      note: 'You can always join more hubs later with the `/connect` command!'
    hubSelection:
      title: 'InterChat Setup (2/4)'
      description: 'Choose a hub to join from our most active communities:'
      placeholder: 'Choose a hub to join'
      tip: '**Tip:** You can always join more hubs later using `/connect` and [the hub list](https://interchat.app/hubs).'
    hubCreation:
      modal:
        title: 'Create New Hub'
        name:
          label: 'Hub Name'
          placeholder: 'e.g., Gaming Community, Art Gallery'
        description:
          label: 'Description'
          placeholder: 'What is this hub about?'
    nextSteps:
      created:
        title: '✨ Almost Done!'
        description: "Your Hub \"{hubName}\" is Ready!\nClick Finish Setup to complete the process. After that, follow these steps:"
        inviteLink:
          title: '1️⃣ Create an Invite Link'
          description: "{hubInviteCommand} `hub:{hubName}`\nThis will generate an invite link you can share with other servers"
        shareHub:
          title: '2️⃣ Share Your Hub'
          description: |
            Share the invite link with at least one other server to start chatting!
            {dot} Send to your friends & servers
            {dot} Share in our [support server]({supportInvite})
        configuration:
          title: '3️⃣ Essential Configuration'
          description: |
            {hubRulesCommand}
            Create hub rules and guidelines

            {hubLoggingCommand}
            Set up logging channels for hub events

            {hubAntiSwearCommand}
            Configure word filters and auto-moderation

            {hubSettingsCommand}
            Manage message types and notifications
        proTips:
          title: '💡 Pro Tips'
          description: |
            {dot} Your hub is private by default - only servers with invites can join
            {dot} Vote for InterChat to unlock custom welcome messages and colors
            {dot} You can publish your hub to the [hub directory]({website}/hubs) using {hubVisibilityCommand}
            {dot} Join our [support server]({supportInvite}) for hub management tips!
        copyCommand: "`/hub invite create hub:{hubName}`\n✨ Command copied! Run this to create an invite link."
      joined:
        title: '✨ Ready to Join?'
        description: "Ready to Join \"{hubName}\"?\nClick Finish Setup to join the hub. After joining, you can use these commands:"
        commands: |
          {connectionEditCommand}
          Customize how you receive/send messages to the hub

          {connectionListCommand}
          View all your connected hubs

          {website}/hubs (New :sparkles:)
          Join more hubs
        help: 'Join our [support server]({supportInvite}) if you have questions!'
    completion:
      title: 'Setup Complete!'
      description: 'Your server has been successfully connected to the hub in {channel}. You can now start chatting!'
    buttons:
      supportServer: 'Support Server'
      documentation: 'Documentation'
      goBack: 'Go Back'
      finishSetup: 'Finish Setup'
      hubDirectory: 'Hub Directory'
      learnMore: 'Learn More'
      viewChannel: 'View Channel'
      joinPopularHub: 'Join Popular Hub'
      createNewHub: 'Create New Hub'
      copyInviteCommand: 'Copy Invite Command'
      findMoreHubs: 'Find More Hubs'
    existingConnections:
      title: 'Existing Connections'
      description: |
        Your server is already connected to the following hubs:

        {connectionList}

        You can continue to add more connections if you'd like.
  language:
    description: '🈂️ Set the language in which I should respond to you'
    options:
      lang:
        description: 'The language to set'
  badges:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  tutorial:
    description: '📚 Learn how to use InterChat with interactive tutorials'
    subcommands:
      start:
        description: 'Start a specific tutorial'
        options:
          tutorial:
            description: 'The tutorial to start'
      setup:
        description: 'Start the server setup tutorial (for admins)'
  rules:
    description: '📋 Sends the network rules for InterChat.'
    options:
      hub:
        description: 'View rules for a specific hub'
    hubRules:
      title: '{hubName} Rules'
      description: 'The following rules apply to this hub'
    botRules:
      title: 'InterChat Rules'
      description: 'Bot-wide rules for all users'
#Tutorial system
tutorial:
  errors:
    notFound: '{emoji} Tutorial not found.'
    noSteps: '{emoji} This tutorial has no steps.'
    prerequisitesRequired: '{emoji} You need to complete the prerequisite tutorials first.'
    noInProgress: '{emoji} You have no tutorials in progress.'
  completion:
    completed: '{emoji} Tutorial completed! Great job!'
    nextRecommendation: 'Next recommended tutorial: {tutorialName}'
  categories:
    newUser: 'New User Tutorials'
    admin: 'Server Admin Tutorials'
    moderator: 'Moderator Tutorials'
    all: 'General Tutorials'
  list:
    title: 'Available Tutorials'
    noTutorials: 'No tutorials available at the moment.'
    description: 'Choose a tutorial to get started with InterChat'
  progress:
    completed: '✅ Completed'
    inProgress: '▶️ In Progress'
    notStarted: '⭕ Not Started'
  buttons:
    start: 'Start Tutorial'
    resume: 'Resume'
    review: 'Review'
    next: 'Next'
    previous: 'Previous'
    skip: 'Skip'
    finish: 'Finish'
  about:
    description: '🚀 Learn how InterChat helps grow Discord communities'
    title: 'About InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    features:
      title: 'What makes InterChat different:'
      list: |
        - Built for real communities - Designed with Discord server owners' needs in mind
        - Active hubs - Find and join thriving communities around shared interests
        - Privacy first - Full control over your hub's connections and settings
        - Smart moderation - AI-powered image filtering and advanced content filtering keeps discussions healthy
        - Visual dashboard - Manage your hubs, servers, and settings through our web interface
    sections:
      invite: 'Invite InterChat to your server:'
      dashboard: 'Visit the InterChat dashboard:'
      support: 'Join our support server:'
      credits: 'Check out the InterChat team!'
    buttons:
      invite: 'Invite'
      dashboard: 'Dashboard'
      support: 'Support Server'
      credits: 'Credits & Team'
      vote: 'Vote!'
    support_text: 'InterChat is completely free to use. If you like InterChat, consider supporting us on Ko-fi! Or even a vote on top.gg helps us a lot!'
    credits:
      title: 'CREDITS'
      developers: 'Developers:'
      staff: 'Staff: ([Check Applications!]({website}/apply))'
      translators: 'Translators:'
      mentions: 'Deserving Mentions:'
      mascot: '(maker of our cute mascot chipi {emoji})'
      top_voter: '([top voter]({vote_url}) of all time {emoji})'
      footer: 'InterChat v{version} • Made with ❤️ by the InterChat Team'
#Hub configuration and management
hubConfig:
  antiSwear:
    title: 'Anti-Swear Configuration'
    description: 'Configure word filters and auto-moderation for this hub'
    noRules: "Let's set up some anti-swear rules!\nUse the `Add Rule` button to create one."
    selectRule: "Select a rule to edit it's words/actions"
    placeholder: 'Select a log type to configure'
    validating: '{emoji} Validating anti-swear rule...'
    buttons:
      addRule: 'Add Rule'
      editRule: 'Edit Rule'
      deleteRule: 'Delete Rule'
      back: 'Back'
    modal:
      addRule: 'Add Anti-Swear Rule'
      editRule: 'Editing Anti-Swear Rule'
      ruleName: 'Rule Name'
      words: 'Words'
      wordsPlaceholder: 'Words seperated by comma. (Use * for wildcard). Eg. word1, *word2*, *word3, word4*'
  logging:
    title: 'Logs Configuration'
    description: 'Configure logging channels and notifications for hub events'
    placeholder: 'Select a log type to configure'
    channelSelect: '#️⃣ Select a channel to send the logs'
    roleSelect: '🏓 Select the role to mention when a log is triggered.'
    config:
      title: 'Configure `{type}` Logs'
      description: |
        {arrow} Select a log channel and/or role to be pinged from the dropdown below.
        {arrow} You can also disable logging by using the button below.
      fields:
        channel: 'Channel'
        role: 'Role Mention'
    types:
      reports:
        label: 'Reports'
        description: 'Receive reports from users.'
      modLogs:
        label: 'Mod Logs'
        description: 'Log Moderation actions. (eg. blacklist, message deletes, etc.)'
      joinLeaves:
        label: 'Join/Leave'
        description: 'Log when a server joins or leaves this hub.'
      appeals:
        label: 'Appeals'
        description: 'Recieve appeals from blacklisted users/servers.'
      networkAlerts:
        label: 'Network Alerts'
        description: 'Recieve alerts about automatically blocked messages.'
      messageModeration:
        label: 'Message Moderation'
        description: 'Log message deletions and edits by moderators.'
  rules:
    title: 'Hub Rules Configuration'
    description: 'Manage rules and guidelines for your hub'
    noRules: "This hub has no rules configured yet. Let's add some!"
    maxRulesReached: 'Maximum number of rules ({max}) reached.'
    ruleExists: 'This rule already exists.'
    placeholder: 'Select a rule to edit or remove'
    modal:
      add:
        title: 'Add Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the rule text (max 1000 characters)'
      edit:
        title: 'Edit Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the new rule text (max 1000 characters)'
    buttons:
      add: 'Add Rule'
      edit: 'Edit Rule'
      delete: 'Delete Rule'
      back: 'Back'
    view:
      title: 'Rule {number}'
      select: 'Select an action for this rule'
  appealCooldown:
    errors:
      invalidCooldown: 'Please provide a valid cooldown duration.'
      tooShort: 'Cooldown must be atleast **1 hour** long.'
      tooLong: 'Cooldown cannot be longer than **1 year**.'
    success: '{emoji} Appeal cooldown has been set to **{hours}** hour(s).'
#Interaction and modal text
interactions:
  modals:
    warn:
      title: 'Warn User'
      reason:
        label: 'Reason'
        placeholder: 'Enter the reason for warning this user...'
  buttons:
    refresh: 'Refresh'
    cancel: 'Cancel'
    confirm: 'Confirm'
    back: 'Back'
    next: 'Next'
    finish: 'Finish'
    save: 'Save'
    delete: 'Delete'
    edit: 'Edit'
    add: 'Add'
    remove: 'Remove'
    view: 'View'
    close: 'Close'
  placeholders:
    selectOption: 'Select an option'
    selectChannel: 'Select a channel'
    selectRole: 'Select a role'
    selectUser: 'Select a user'
    selectServer: 'Select a server'
    enterText: 'Enter text here...'
    enterReason: 'Enter a reason...'
    enterDescription: 'Enter a description...'
#Moderation panel
modPanel:
  buttons:
    serverBanned: 'Server Banned'
    banServer: 'Ban Server'
  modals:
    blacklistUser: 'Blacklist User'
    blacklistServer: 'Blacklist Server'
#General UI text
ui:
  titles:
    error: 'Error'
    warning: 'Warning'
    success: 'Success'
    info: 'Information'
    confirmation: 'Confirmation'
  messages:
    loading: 'Loading...'
    processing: 'Processing your request...'
    pleaseWait: 'Please wait...'
    tryAgain: 'Please try again.'
    contactSupport: 'Please contact support if this issue persists.'
    operationCancelled: 'Operation cancelled.'
    operationCompleted: 'Operation completed successfully.'
    noDataAvailable: 'No data available.'
    permissionDenied: 'Permission denied.'
    invalidInput: 'Invalid input provided.'
    timeout: 'Operation timed out.'
    notFound: 'Not found.'
    alreadyExists: 'Already exists.'
    unavailable: 'Currently unavailable.'
#Message management commands
deleteMsg:
  description: 'Delete a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to delete'
  contextMenu:
    name: 'Delete Message'
  processing: '{emoji} Your request has been queued. Messages will be deleted shortly...'
  alreadyDeleted: '{emoji} This message is already deleted or is being deleted by another moderator.'
editMsg:
  description: 'Edit a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to edit'
    newContent:
      description: 'The new content for the message'
  contextMenu:
    name: 'Edit Message'
  modal:
    title: 'Edit Message'
    content:
      label: 'New Content'
      placeholder: 'Enter the new message content...'
  processing: '{emoji} Your request has been queued. Messages will be edited shortly...'
  alreadyEdited: '{emoji} This message is already being edited by another moderator.'
inbox:
  description: 'Check your inbox for latest important updates & announcements'
  title: '📬 InterChat Inbox'
  subtitle:
    new: 'Latest announcements and updates'
    older: 'Viewing older announcements'
  empty:
    title: '📬 All caught up!'
    description: "I'll let you know when there's more. But for now, there's only Chipi here: {emoji}"
  buttons:
    viewOlder: 'View Older'
    previous: 'Previous'
    next: 'Next'
  postedOn: 'Posted on {date}'
joinserver:
  description: 'Join a server or send a request to join a server through InterChat.'
  options:
    servername:
      description: 'The name of the server you want to join'
    messageorserverid:
      description: 'The message ID or server ID'
  errors:
    channelOnly: 'This command can only be used in a channel.'
    missingTarget: 'You must provide a message ID or server ID'
  success:
    inviteSent: "{emoji} I have DM'd you the invite link to the server!"
  request:
    title: 'Join Request'
    description: 'You requested to join the server `{serverName}` through InterChat. Here is the invite link:'
    broadcast: 'User `{username}` from `{guildName}` has requested to join this server. Do you want to accept them?'
  buttons:
    accept: 'Accept'
    reject: 'Reject'
  response:
    sent: "{emoji} Your request has been sent to the server. You will be DM'd the invite link if accepted."
    creating: '{emoji} This server does not have an invite link yet. Creating one...'
    dmSent: '{emoji} The invite link has been sent to the user.'
    dmFailed: '{emoji} The invite link could not be sent to the user. They may have DMs disabled.'
  status:
    accepted: 'Accepted by {username}'
    rejected: 'Rejected by {username}'
messageInfo:
  description: 'Get information about a message.'
  options:
    message:
      description: 'The message to get information about.'
  contextMenu:
    name: 'Message Info'
  errors:
    profileFetch: 'Failed to fetch user profile.'
connect:
  description: '🔗 Connect your channel to an InterChat hub'
  options:
    channel:
      description: 'The channel you want to connect to a hub'
    invite:
      description: 'The invite code of the private hub you want to join'
  errors:
    invalidIds: '{emoji} Invalid hub or channel ID.'
    channelNotFound: '{emoji} Channel not found or not a text channel.'
disconnect:
  description: '👋 Disconnect a channel from a hub'
#Staff commands
ban:
  description: '🔨 Ban users or servers from InterChat with comprehensive options'
  options:
    duration:
      description: 'Ban duration'
    reason:
      description: 'Reason for the ban (required)'
    user:
      description: 'User to ban (required for user bans)'
    serverId:
      description: 'Server ID to ban (required for server bans)'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to ban.'
unban:
  description: '🔓 Unban users or servers from InterChat'
  options:
    user:
      description: 'User to unban'
    serverId:
      description: 'Server ID to unban'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to unban.'
    invalidTarget: '{emoji} Invalid ban target. Please use the autocomplete to select a valid ban.'
    banNotFound: '{emoji} Ban not found.'
    serverBanNotFound: '{emoji} Server ban not found.'
    loadFailed: '{emoji} Failed to load ban information.'
achievements:
  description: "🏆 View your achievements or another user's achievements"
  options:
    user:
      description: 'The user to view achievements for (defaults to yourself)'
    view:
      description: 'Choose which achievements to view'
  title: "🏆 {username}'s Achievements"
  progress: '**Progress:** {unlocked}/{total} achievements unlocked'
  errors:
    userNotFound: 'User not found.'
achievement:
  settings:
    enabled: 'Achievement notifications are now **enabled**. You will receive notifications when you unlock new achievements!'
    disabled: 'Achievement notifications are now **disabled**. You will no longer receive notifications when you unlock achievements.'
profile:
  description: "View your profile or someone else's InterChat profile."
  options:
    user:
      description: 'The user to view the profile of.'
  errors:
    userNotFound: 'User not found.'
rank:
  description: 'Display user rank and statistics'
  options:
    user:
      description: 'The user to get the rank of'
  errors:
    createFailed: 'Failed to create rank card. Please try again later.'
#Userphone commands
call:
  description: '📞 [BETA] Start a call with another server'
  errors:
    skipFailed: 'Skip Failed'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
hangup:
  description: '📞 End the current call'
  callEnded: '{user} ended the call.'
  errors:
    error: 'Error'
    callFailed: 'Call Failed'
    guildOnly: '{emoji} This command can only be used in a server text channel.'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
skip:
  description: '[BETA] Skip the current call and find a new match'
  errors:
    error: 'Error'
    skipFailed: 'Skip Failed'
voteCommand:
  description: '✨ Voting perks and vote link.'
#Welcome message system for new servers
welcome:
  buttons:
    back: 'Back'
  calls:
    title: 'Setup Calls'
    description: 'Learn about InterChat calls - instant server-to-server connections!'
    commands: |
      ### Available Call Commands

      **{callCommand}** - Start a call with another server
      **{skipCommand}** - Skip current call and find a new match
      **{hangupCommand}** - End the current call
      **{leaderboardCommand}** - View call leaderboards
    examples:
      title: 'How to Use Calls'
      content: |
        1. Run `/call` in any text channel to start
        2. Wait to be matched with another server
        3. Chat with the other server in real-time
        4. Use `/skip` to find a different server
        5. Use `/hangup` when you're done chatting

        **Note:** Calls are in beta - for a more reliable experience, try InterChat Hubs!
  setup:
    title: '🏠 Setup Cross-Server Chat'
    description: 'Connect to hubs for persistent cross-server communities!'
    instructions: |
      ### Get Started with Hubs

      **{setupCommand}** - Guided setup to join your first hub
      **{connectCommand}** - Connect to a specific hub

      **What are Hubs?**
      Hubs are persistent chat communities where multiple servers connect and chat together 24/7. Unlike calls, hub messages stay even when you're offline!

      **Why Choose Hubs?**
      - Persistent connections that stay active
      - Join multiple themed communities
      - Advanced moderation and filtering
      - Custom welcome messages and rules
      - Thousands of servers already connected
    buttons:
      runSetup: 'Run Setup Now'
    errors:
      commandNotFound: '{emoji} Setup command not found. Please try running `/setup` manually.'
