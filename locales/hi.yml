rules:
  header: 'InterChat नियम'
  botRulesNote: 'ये नियम सभी के लिए सुरक्षित और सुखद अनुभव सुनिश्चित करने के लिए बनाए गए हैं। कृपया इन्हें ध्यान से पढ़ें और पालन करें:'
  rules: |
    1. **कोई घृणास्पद भाषण या उत्पीड़न नहीं**
    -# > **इनमें शामिल है:** दूसरों पर हमला करने के लिए गाली गलौच या नफरत फैलाने वाली भाषा का उपयोग करना, [और भी]({guidelines_link}).

    2. **कोई अवैध सामग्री नहीं**
    -# > **इनमें शामिल है:** अवैध सामग्री के लिंक साझा करना, हिंसा को बढ़ावा देना, [और भी]({guidelines_link}).

    3. **कोई अत्यधिक अनुचित या वीभत्स सामग्री नहीं**
    -# > **इनमें शामिल है:** InterChat में वीभत्स या अत्यधिक वीभत्स सामग्री पोस्ट करना, गैर-NSFW हब्स में यौन सामग्री पोस्ट करना, [और भी]({guidelines_link}).

    4. **कोई स्पैमिंग या बाढ़ जैसी गतिविधि नहीं**
    -# > **इनमें शामिल है:** भारी मात्रा में स्पैम भेजना या बॉट्स द्वारा बाढ़ (फ्लडिंग) करना, [और भी]({guidelines_link})

    5. **कोई पहचान की नकल या धोखाधड़ी नहीं**
    -# > **इनमें शामिल है:** InterChat स्टाफ या हब मॉडरेटर की नकल करना, क्रिप्टोकरेंसी या NFT घोटाले चलाना, [और भी]({guidelines_link}).

    6. **कोई शोषण या दुर्व्यवहार नहीं**
    -# > **इनमें शामिल है:** नाबालिगों के प्रति अनुचित या शिकारी व्यवहार, आत्म-हानि को बढ़ावा देने के लिए धमकी देना, ब्लैकमेल करना या मांग करना, [और भी]({guidelines_link}).

    7. **कोई हानिकारक सॉफ़्टवेयर साझा नहीं करना**
    -# > **इनमें शामिल है:** मैलवेयर, वायरस, 'free nitro' लिंक, हानिकारक स्क्रिप्ट साझा करना, [और भी]({guidelines_link}).

    आप [Discord's Terms of Service](https://discord.com/terms) और [Community Guidelines](https://discord.com/guidelines) का भी पालन करने के लिए सहमत होते हैं। [नियमों की पूरी सूची]({guidelines_link}) देखें।
  welcome: |
    {emoji} अरे हाय! {user}, InterChat में आपका स्वागत है! 🎉

    हम आपको Discord के विभिन्न सर्वरों को जोड़ने वाले इस अद्भुत समुदाय में देखकर बहुत उत्साहित हैं! दुनिया भर के लोगों से बातचीत शुरू करने से पहले, चलिए हमारी मित्रतापूर्ण सामुदायिक दिशानिर्देशों पर एक नज़र डालते हैं।

    ये सरल नियम InterChat को एक गर्मजोशी से भरा, स्वागत करने वाला और सुरक्षित स्थान बनाए रखने में मदद करते हैं, ताकि हर कोई नए दोस्त बना सके और शानदार बातचीत का आनंद ले सके! ✨
  alreadyAccepted: "{emoji} फिर से स्वागत है, {user}! \nआप अब शानदार समुदायों से जुड़ने और बातचीत करने के लिए पूरी तरह तैयार हैं। नए दोस्तों से मिलने का आनंद लें! 🌟"
  continue: आगे बढ़ें
  accept: स्वीकार करें
  decline: अस्वीकार करें
  agreementNote: इन नियमों को स्वीकार करके, आप InterChat का उपयोग करते समय इन्हें पालन करने के लिए सहमति देते हैं। इन नियमों का उल्लंघन करने पर प्रतिबंध या बैन हो सकता है।
  hubAgreementNote: |
    इन नियमों को स्वीकार करके, आप इस हब में बात करते समय इन्हें पालन करने के लिए सहमति देते हैं। इन नियमों का उल्लंघन करने पर हब से हटा दिया जा सकता है।

    ⚠️ **जब तक आप इन नियमों को स्वीकार नहीं करते, आप इस हब में संदेश नहीं भेज सकते।**
  accepted: |
    {emoji} शानदार! InterChat परिवार में आपका स्वागत है! 🎉✨

    अब आप उस अद्भुत समुदाय का हिस्सा हैं जो हज़ारों सर्वरों और लाखों लोगों को पूरी दुनिया में जोड़ता है! इस शानदार सफ़र में आपका साथ पाकर हमें बहुत ख़ुशी है।

    ### 🚀 क्या आप तैयार हैं अपनी यात्रा शुरू करने के लिए?
    - **समुदायों को खोजें:** [hub directory]({hubs_link}) का उपयोग करके जीवंत और सक्रिय समुदायों को खोजें जो आपका इंतज़ार कर रहे हैं
    - **बातचीत में शामिल हों:** उन चर्चाओं में भाग लें जो आपकी रुचि जगाएं, या हमारे [dashboard]({dashboard_link}) के माध्यम से अपना खुद का हब बनाएं
    - **त्वरित कनेक्शन आज़माएं:** तुरंत एक-के-बाद-एक सर्वर कनेक्शन के लिए हमारे `/call` कमांड का उपयोग करें - नए दोस्तों से मिलने के लिए एकदम सही!
    - **व्यक्तिगत सहायता प्राप्त करें:** अगर आप थोड़े उलझन में हैं तो `/setup` का उपयोग करें और एक गाइडेड टूर पाएं

    ### 💝 हम हमेशा आपके साथ हैं!
    अगर आप भ्रमित हैं या मदद चाहिए, तो घबराएं नहीं - हम सबने शुरुआत में ऐसा महसूस किया है! हमारे मित्रवत [support community]({support_invite}) से जुड़ें जहाँ असली लोग आपकी मदद करने के लिए उत्साहित हैं।

    **क्या आपको हमारा काम पसंद आया?** InterChat को बढ़ने और भी शानदार फीचर्स लाने में मदद करने के लिए [हमें समर्थन दें]({donateLink}), जिससे दुनिया भर के समुदाय और जुड़ सकें! 🌍
  declined: |
    {emoji} कृपया एक पल निकालकर सामुदायिक दिशानिर्देशों को पढ़ें और स्वीकार करें।

    ये नियम वैकल्पिक नहीं हैं, ये InterChat को सभी के लिए सुरक्षित और आनंददायक बनाए रखने के लिए हैं। आरंभ करने के लिए कोई और संदेश भेजें या कोई भी InterChat कमांड इस्तेमाल करें।

    ⚠️ महत्वपूर्ण: जब तक आप नियम स्वीकार नहीं करते, तब तक आप अन्य सर्वरों से चैट नहीं कर पाएंगे। अपना समय लें, लेकिन यह चरण अनिवार्य है।
  hubAccepted: |
    {emoji} आपने हब के नियम स्वीकार कर लिए हैं। 
    अब आप इस हब में बातचीत शुरू कर सकते हैं!
  hubDeclined: |
    {emoji} आपने {hubName} के नियम अस्वीकार कर दिए हैं। 
    -# ⚠️ **जब तक आप इन नियमों को स्वीकार नहीं करते, तब तक आप इस हब में संदेश नहीं भेज पाएंगे।** 
    -# दोबारा प्रयास करने के लिए, इस हब में कोई और संदेश भेजें।
  noHubRules: इस हब ने अभी तक कोई विशेष नियम निर्धारित नहीं किए हैं। हालांकि, [general InterChat rules]({rules_link}) अभी भी लागू हैं।
  hubRules: हब नियम
  viewbotRules: 'बॉट नियम देखें'
vote:
  description: |
    अधिक समुदायों को InterChat खोजने में मदद करें! top.gg पर आपका वोट:

    - दूसरों को सक्रिय समुदाय खोजने में मदद करता है 
    - आपके लिए विशेष सुविधाओं को अनलॉक करता है 
    - हमारे स्वतंत्र विकास का समर्थन करता है
  footer: 'वोट हर बारह (12) घंटे में रीफ़्रेश होते हैं • InterChat का समर्थन करने के लिए धन्यवाद!'
  button:
    label: 'top.gg पर वोट करें'
  perks:
    moreComingSoon: 'और फायदे जल्द ही आ रहे हैं! कुछ सुझाव [सपोर्ट सर्वर]({support_invite}) में दें।'
  fields:
    currentStreak: 'वर्तमान स्ट्रीक:'
    lastVote: 'पिछला वोट'
    voterPerks: 'वोटर के लाभ'
    voteNow: '[अभी वोट करें]({vote_url})!'
    perks:
      messageLength: 'बढ़ी हुई संदेश लंबाई (2000 अक्षर)'
      stickers: 'हब में स्टिकर्स भेजें'
      createHubs: '4 हब तक बनाएँ'
      welcomeMessages: 'मनचाहे स्वागत संदेश'
      voterRole: 'सपोर्ट सर्वर में वोटर की भूमिका'
      voterBadge: '/profil​e में विशेष वोटर बैज'
  embed:
    title: 'InterChat के लिए वोट करें'
network:
  accountTooNew: '{emoji} अरे हाय {user}! आपका Discord खाता अभी नया है, इसलिए InterChat के माध्यम से संदेश भेजने से पहले आपको थोड़ी देर इंतज़ार करना होगा। यह हमारी समुदाय को सुरक्षित बनाए रखने में मदद करता है! कृपया थोड़ी देर बाद फिर से प्रयास करें।'
  deleteSuccess: '{emoji} {user} द्वारा भेजा गया संदेश __**{deleted}/{total}**__ सर्वरों से हटा दिया गया है।'
  editInProgress: '{emoji} आपका अनुरोध कतार में जोड़ दिया गया है। संदेशों जल्द ही संपादन किया जाएगा...'
  editInProgressError: '{emoji} यह संदेश पहले से ही किसी अन्य उपयोगकर्ता द्वारा संपादित किया जा रहा है।'
  emptyContent: '{emoji} संदेश खाली नहीं हो सकता।'
  newMessageContent: 'नया संदेश सामग्री'
  editMessagePrompt: '{emoji} कृपया अपना संदेश संपादित करने के लिए मोडल का उपयोग करें।'
  editSuccess: '{emoji} {user} द्वारा भेजा गया संदेश __**{edited}/{total}**__ सर्वरों में संपादित कर दिया गया है।'
  onboarding:
    welcome:
      title: '🎉 Welcome to InterChat!'
      description: |
        Welcome to InterChat! Let's get you set up with a personalized experience that matches your interests and helps you find the perfect communities to join.

        This quick setup will help us:
        - Learn about your interests and preferences
        - Find the best hubs for you to join
        - Connect you with like-minded people
        - Get you started with your first community

        Ready to begin your InterChat journey?
    embed:
      title: '🎉 {hubName} में आपका स्वागत है!'
      description: |
        Congratulations! You've discovered an amazing, active community hub! 🌟

        Before you dive in and start making new friends, let's take a quick peek at our simple guidelines. They're designed to help everyone have the most fun and feel comfortable sharing their thoughts and experiences.

        Let's get started!
      footer: InterChat नेटवर्क | दुनियाभर के समुदायों को जोड़ना 🌍
    inProgress: '{emoji} {channel} पहले से ही एक हब में शामिल होने के लिए सेटअप होने की प्रक्रिया में है। कृपया सेटअप को पूरा करने या रद्द करने के लिए प्रतीक्षा करें यदि आप इसे शुरू करने वाले थे।'
blacklist:
  description: 'अपने हब से किसी उपयोगकर्ता या सर्वर को म्यूट/बैन करें।'
  success: '{emoji} **{name}** सफलतापूर्ण ब्लैकलिस्ट हो गया है!'
  removed: '{emoji} **{name}** ब्लैकलिस्ट से हटा दिया गया है!'
  modal:
    reason:
      label: कारण
      placeholder: ब्लैकलिस्ट करने का कारण
    duration:
      label: अवधि
      placeholder: 'ब्लैकलिस्ट की अवधि। जैसे: 1d, 1w, 1m, 1y। स्थायी के लिए खाली छोड़ें।'
  user:
    description: 'अपने हब से किसी उपयोगकर्ता को म्यूट/बैन करें।'
    options:
      user:
        description: 'ब्लैकलिस्ट करने वाले उपयोगकर्ता की ID (/messageinfo कमांड का उपयोग करके ID प्राप्त करें)'
      hub:
        description: 'जिस हब से ब्लैकलिस्ट करना है'
    selectDuration: '{username} के लिए ब्लैकलिस्ट की अवधि चुनें:'
    cannotBlacklistMod: '{emoji} आप किसी मॉडरेटर को ब्लैकलिस्ट नहीं कर सकते। कृपया पहले उनका मॉडरेटर रोल हटाएं।'
    alreadyBlacklisted: '{emoji} यह User पहले से ही ब्लैकलिस्टेड है।'
    easterEggs:
      blacklistBot: आप मुझे ब्लैकलिस्ट नहीं कर सकते, wtf.
  server:
    description: 'अपने हब से किसी सर्वर को म्यूट/बैन करें।'
    options:
      server:
        description: 'ब्लैकलिस्ट करने वाला सर्वर'
      hub:
        description: 'जिस हब से ब्लैकलिस्ट करना है'
    selectDuration: '{serverName} के लिए ब्लैकलिस्ट की अवधि चुनें:'
    alreadyBlacklisted: '{emoji} यह सर्वर पहले से ही ब्लैकलिस्टेड है।'
    unknownError: '**{server}** को ब्लैकलिस्ट करने में विफल रहा। अधिक जानकारी के लिए डेवलपर्स से पूछताछ करें।'
  list:
    description: 'अपने हब में ब्लैकलिस्ट किए गए सभी उपयोगकर्ताओं और सर्वरों की सूची देखें।'
    user: |
      **User ID:** {id}
      **मॉडरेटर:** {moderator}
      **कारण:** {reason}
      **समाप्ति:** {expires}
    server: |
      **सर्वर ID:** {id}
      **मॉडरेटर:** {moderator}
      **कारण:** {reason}
      **समाप्ति:** {expires}
msgInfo:
  buttons:
    message: संदेश जानकारी
    server: सर्वर जानकारी
    user: User जानकारी
    report: रिपोर्ट
  report:
    notEnabled: '{emoji} इस हब के लिए रिपोर्टिंग सक्षम नहीं है।'
    success: '{emoji} रिपोर्ट सफलतापूर्वक सबमिट की गई। धन्यवाद!'
invite: |
  InterChat को आमंत्रित करने के लिए धन्यवाद! यदि आपके कोई प्रश्न हैं या सहायता की आवश्यकता है, तो हम सपोर्ट सर्वर में आपकी मदद के लिए हमेशा उपलब्ध हैं!

  **[{invite_emoji} `आमंत्रण लिंक`]( {invite} ) [{support_emoji} `सपोर्ट सर्वर`]( {support} )**
connection:
  joinRequestsDisabled: '{emoji} इस हब के लिए जुड़ने के अनुरोध अक्षम हैं।'
  notFound: '{emoji} अमान्य कनेक्शन। चैनल ID की पुष्टि करें या प्रदर्शित विकल्पों से चुनें।'
  channelNotFound: '{emoji} कनेक्टेड चैनल नहीं मिला। फिर से बात करने के लिए एक नया चैनल चुनें।'
  alreadyConnected: '{emoji} चैनल {channel} पहले से ही एक हब से कनेक्टेड है।'
  switchChannel: '{emoji} नीचे दिए गए चयन मेनू का उपयोग करके स्विच करने के लिए एक चैनल चुनें:'
  switchCalled: '{emoji} चैनल स्विच कॉल किया गया, नए कनेक्शन को देखने के लिए फिर से कमांड का उपयोग करें।'
  switchSuccess: '{emoji} चैनल स्विच हो गया है। आप अब **{channel}** से कनेक्ट हो गए हैं।'
  inviteRemoved: '{emoji} इस हब के लिए सर्वर आमंत्रण हटा दिया गया है।'
  setInviteError: '{emoji} इनवाइट बनाने में असमर्थ। कृपया कनेक्टेड चैनल के लिए मुझे `Create Invite` अनुमति दें।'
  inviteAdded: '{emoji} आमंत्रण जोड़ा गया। अब अन्य इस सर्वर को `Apps > Message Info/Report` कमांड का उपयोग करके ज्वाइन कर सकते हैं।'
  emColorInvalid: '{emoji} अमान्य रंग। कृपया सुनिश्चित करें कि आपने एक वैध हेक्स रंग कोड दर्ज किया है।'
  emColorChange: '{emoji} एंबेड रंग सफलतापूर्वक {action}'
  embed:
    title: कनेक्शन जानकारी
    fields:
      hub: हब
      channel: चैनल
      invite: आमंत्रण
      connected: कनेक्ट हो गया
      emColor: एंबेड रंग
      compact: कॉम्पैक्ट मोड
    footer: अपने संपर्क का प्रबंधन करने के लिए नीचे दिए गए चयन सूची का उपयोग करें।
  selects:
    placeholder: '🛠️ इस कनेक्शन को संपादित करने के लिए एक विकल्प चुनें'
  unpaused:
    desc: |
      ### {tick_emoji} कनेक्शन अनपॉज किया गया

      {channel} के लिए कनेक्शन अनपॉज किया गया! हब से संदेश अब चैनल में आने लगेंगे और आप फिर से हब में संदेश भेज सकते हैं।
    tips: |
      **💡 सुझाव:** कनेक्शन को रोकने के लिए {pause_cmd} का उपयोग करें या {edit_cmd} से एम्बेड रंग सेट करें, अपने सर्वर में आमंत्रित करें और बहुत कुछ करें।
  paused:
    desc: |
      ### {clock_emoji} कनेक्शन पॉज किया गया
      {channel} के लिए कनेक्शन पॉज किया गया! हब से संदेश अब चैनल में नहीं आएंगे और आपके संदेश हब में प्रसारित नहीं होंगे।
    tips: |
      **💡 सुझाव:** कनेक्शन को अनपॉज करने के लिए {unpause_cmd} का उपयोग करें या संदेश प्राप्त करना स्थायी रूप से बंद करने के लिए {leave_cmd} का उपयोग करें।
hub:
  notFound: "{emoji} Hmm, हमें वह हब नहीं मिला। कृपया आपने जो नाम दर्ज किया है उसे दोबारा जांचें, या कुछ अद्भुत समुदायों को खोजने के लिए [हब निर्देशिका]({hubs_link}) में ब्राउज़ करने का प्रयास करें! 🔍"
  notFound_mod: '{emoji} हब नहीं मिल सका। कृपया सुनिश्चित करें कि आपने सही हब नाम दर्ज किया है और आप उस हब के ओनर या मॉडरेटर हैं।'
  notManager: '{emoji} यह क्रिया करने के लिए आपको हब मैनेजर होना ज़रूरी है।'
  notModerator: '{emoji} यह क्रिया करने के लिए आपको हब मॉडरेटर होना आवश्यक है।'
  notPrivate: '{emoji} यह हब प्राइवेट नहीं है।'
  notOwner: '{emoji} यह क्रिया केवल इस हब का ओनर ही कर सकता है।'
  alreadyJoined: '{emoji} आप पहले ही **{channel}** से एक और हब **{hub}** में जुड़ चुके हैं! पहले उस पर `/disconnect` कमांड का उपयोग करें, फिर `/connect` से दोबारा प्रयास करें।'
  invalidChannel: '{emoji} अमान्य चैनल। केवल टेक्स्ट और थ्रेड चैनल ही समर्थित हैं!'
  invalidImgurUrl: '{emoji} आइकन या बैनर के लिए इमेज URL अमान्य है। कृपया सुनिश्चित करें कि आपने एक वैध Imgur इमेज URL दर्ज किया है, जो गैलरी या एलबम नहीं है।'
  join:
    success: |
      🎉 **शानदार! {hub} में आपका स्वागत है!** 🎉

      आपने {channel} को इस शानदार समुदाय से जोड़ लिया है! अब आप इस चैनल से दुनिया भर के सर्वरों के सदस्यों से बातचीत कर सकते हैं। कितना रोमांचक है ना? ✨

      **🚀 क्या आप एक्सप्लोर करने के लिए तैयार हैं?**
      - अपना कनेक्शन पर्सनलाइज़ करने के लिए `/connection` का उपयोग करें
      - यदि कभी इस हब को छोड़ना चाहें तो `/disconnect` का उपयोग करें
      - किसी भी समय दूसरे चैनल पर स्विच करने के लिए `/connection edit` का उपयोग करें

      **💝 प्रो टिप:** नमस्ते कहें और अपना परिचय दें – सभी को नए दोस्त बनाना अच्छा लगता है! समुदाय से जुड़ने का आनंद लें! 🌟
    nsfwChannelSfwHub: '{emoji} NSFW channels cannot connect to SFW hubs. {channel} is marked as NSFW, but **{hub}** is a safe-for-work hub. Please use a non-NSFW channel or find an NSFW hub instead.'
    sfwChannelNsfwHub: '{emoji} SFW channels cannot connect to NSFW hubs. {channel} is not marked as NSFW, but **{hub}** is an adult content hub. Please use an NSFW channel or find a SFW hub instead.'
  servers:
    total: 'वर्तमान जुड़े हुए सर्वर: {from}-{to} / **{total}**'
    noConnections: '{emoji} अभी तक कोई सर्वर इस हब से नहीं जुड़ा है। इस हब से जुड़ने के लिए `/connect` का उपयोग करें।'
    notConnected: "{emoji} वह सर्वर **{hub}** का हिस्सा नहीं है।"
    connectionInfo: |
      सर्वर ID: {serverId} 
      चैनल: #{channelName} `({channelId})` 
      जुड़ने की तिथि: {joinedAt} 
      आमंत्रण लिंक: {invite} 
      जुड़ाव स्थिति: {connected}
  blockwords:
    deleted: '{emoji} एंटी-गाली नियम सफलतापूर्वक हटा दिया गया!'
    notFound: '{emoji} एंटी-गाली नियम नहीं मिला।'
    maxRules: '{emoji} आपने इस हब के लिए एंटी-गाली नियमों की अधिकतम सीमा (2) तक पहुंच प्राप्त कर ली है। कृपया कोई नियम हटाएं, फिर नया जोड़ें।'
    configure: 'नियम के लिए कार्रवाइयाँ कॉन्फ़िगर करें: {rule}'
    actionsUpdated: '{emoji} नियम द्वारा ली जाने वाली कार्रवाइयाँ अपडेट कर दी गई हैं। **नई कार्रवाइयाँ:** {actions}'
    selectRuleToEdit: किसी नियम को चुनें ताकि आप उसके शब्दों या कार्रवाइयों को संपादित कर सकें
    listDescription: |
      ### {emoji} एंटी-गाली नियम
      इस हब में {totalRules}/2 एंटी-गाली नियम सेटअप हैं।
    listFooter: मेनू से कोई नियम चुनें ताकि आप उसके सभी विवरण देख सकें।
    ruleDescription: |
      ### {emoji} नियम संपादन: {ruleName}  
      {words}
    ruleFooter: 'शब्दों या नियम के नाम को संपादित करने के लिए नीचे दिए गए बटन पर क्लिक करें!'
    actionSelectPlaceholder: 'इस नियम द्वारा की जाने वाली कार्रवाइयाँ चुनें।'
    embedFields:
      noActions: '{emoji} **कुछ भी नहीं!** नीचे दिए गए मेनू का उपयोग करके कॉन्फ़िगर करें।'
      actionsName: 'कॉन्फ़िगर की गई कार्रवाइयाँ:'
      actionsValue: '{actions}'
    modal:
      addRule: एंटी-गाली नियम जोड़ें
      editingRule: एंटी-गाली नियम संपादन
      ruleNameLabel: नियम का नाम
      wordsLabel: 'शब्दों'
      wordsPlaceholder: 'शब्दों को कॉमा से अलग करें। (वाइल्डकार्ड के लिए * का उपयोग करें)। Eg. word1, *word2*, *word3, word4*'
    validating: '{emoji} एंटी-गाली नियम को वैध किया जा रहा है…'
    noRules: |
      ### {emoji} चलिए कुछ एंटी-गाली नियम सेटअप करते हैं!
      नया नियम बनाने के लिए `Add Rule` बटन का उपयोग करें।
  create:
    modal:
      title: हब बनाएं
      name:
        label: हब का नाम
        placeholder: अपने हब के लिए एक नाम दर्ज करें
      description:
        label: विवरण
        placeholder: अपने हब का विवरण दर्ज करें।
      icon:
        label: आइकन URL
        placeholder: एक Imgur तस्वीर का URL दर्ज करें।
      banner:
        label: बैनर URL
        placeholder: एक Imgur तस्वीर का URL दर्ज करें।
    maxHubs: "{emoji} [InterChat के लिए वोट करें]({voteUrl}) ताकि आप और हब बना सकें! आपने अधिकतम हब्स की सीमा ({maxHubs}) तक पहुंच प्राप्त कर ली है।\n"
    invalidName: '{emoji} अमान्य हब नाम। इसमें `discord`, `clyde` या \`\`\` शामिल नहीं होना चाहिए। कृपया कोई और नाम चुनें।'
    nameTaken: '{emoji} यह हब नाम पहले से लिया जा चुका है। कृपया कोई और नाम चुनें।'
    success: |
      ## हब बन गया है! यह डिफ़ॉल्ट रूप से __private__ है। 
      अपने हब को कस्टमाइज़ करने के लिए `/hub edit hub:{name}` कमांड का उपयोग करें। शुरू करने के लिए कृपया नीचे दिए गए चरणों का पालन करें:

      ### अगले कदम:
      1. **इनवाइट बनाएं:** 
      > दूसरों को जुड़ने के लिए इनवाइट बनाने के लिए `/hub invite create` का उपयोग करें।

      2. **चैनल लिंक करें:** 
      > पहले बनाए गए इनवाइट लिंक के साथ `/connect` का उपयोग करें ताकि हब से कोई चैनल जोड़ा जा सके और बातचीत शुरू हो सके।

      3. **हब कॉन्फ़िगर करें:** (अनुशंसित) 
      > हब कॉन्फ़िगर करने के लिए `/hub config settings`, `/hub config logging` और `/hub config anti-swear` का उपयोग करें।

      4. **मॉडरेटर जोड़ें:** 
      > हब में मॉडरेटर जोड़ने के लिए `/hub moderator add` का उपयोग करें।

      5. **हब को कस्टमाइज़ करें:** 
      > हब का आइकन, बैनर और विवरण बदलने के लिए `/hub edit` का उपयोग करें।

      6. **हब को सार्वजनिक बनाएं:** 
      > हब को पब्लिक करने और दूसरों को बिना इनवाइट के ब्राउज़/जॉइन करने की अनुमति देने के लिए `/hub visibility` का उपयोग करें। (वैकल्पिक)

      अगर आपके कोई सवाल हैं या मदद चाहिए, तो बेझिझक [सपोर्ट सर्वर]({support_invite}) में पूछें। विकास लागत में सहायता के लिए [डोनेट करें]({donateLink}) पर विचार करें।
  delete:
    confirm: क्या आप वाकई **{hub}** को हटाना चाहते हैं? यह कार्रवाई अपरिवर्तनीय है। इस हब से जुड़े सभी सर्वर, इनवाइट्स और संदेश डेटा हटाए जाएंगे।
    ownerOnly: '{emoji} केवल इस हब का ओनर ही इसे हटा सकता है।'
    success: '{emoji} हब **{hub}** को हटा दिया गया है।'
    cancelled: '{emoji} हब को हटाने की प्रक्रिया रद्द कर दी गई है।'
  invite:
    create:
      success: |
        ### इनवाइट बन गया है!

        आपका इनवाइट सफलतापूर्वक बनाया गया है। अब अन्य लोग `/connect` कमांड का उपयोग करके इस हब में शामिल हो सकते हैं।

        - **शामिल होने के लिए उपयोग करें:** `/connect invite:{inviteCode}`
        - **इनवाइट्स देखें:** `/hub invite list`
        - **समाप्ति समय:** {expiry}
        - **प्रयोग:** ∞

        **नोट:** आप किसी भी समय इस इनवाइट को `/hub invite revoke {inviteCode}` कमांड का उपयोग करके रद्द कर सकते हैं।
    revoke:
      invalidCode: '{emoji} अमान्य इनवाइट कोड। कृपया सुनिश्चित करें कि आपने एक वैध इनवाइट कोड दर्ज किया है।'
      success: '{emoji} इनवाइट {inviteCode} को रद्द कर दिया गया है।'
    list:
      title: '**इनवाइट कोड्स:**'
      noInvites: '{emoji} इस हब में अभी तक कोई इनवाइट नहीं है। इनवाइट बनाने के लिए `/hub invite create` का उपयोग करें।'
      notPrivate: '{emoji} केवल प्राइवेट हब्स में इनवाइट्स हो सकते हैं। इस हब को प्राइवेट बनाने के लिए `/hub edit` का उपयोग करें।'
  joined:
    noJoinedHubs: '{emoji} यह सर्वर अभी तक किसी भी हब से नहीं जुड़ा है। हब्स की सूची देखने के लिए [हब निर्देशिका]({hubs_link}) का उपयोग करें।'
    joinedHubs: यह सर्वर **{total}** हब(s) का हिस्सा है। किसी हब को छोड़ने के लिए `/disconnect` का उपयोग करें।
  leave:
    noHub: '{emoji} वह चैनल अमान्य है या किसी भी हब में शामिल नहीं है।'
    confirm: क्या आप वाकई {channel} से **{hub}** को छोड़ना चाहते हैं? इस हब से इस सर्वर को अब कोई संदेश नहीं भेजा जाएगा।
    confirmFooter: नीचे दिए गए बटन का उपयोग करके 10 सेकंड के भीतर पुष्टि करें।
    success: '{emoji} {channel} से हब छोड़ दिया गया है। अब इस हब से इस सर्वर को कोई संदेश नहीं भेजा जाएगा। आप `/connect` का उपयोग करके दोबारा जुड़ सकते हैं।'
  moderator:
    noModerators: '{emoji} इस हब में अभी तक कोई मॉडरेटर नहीं है। एक जोड़ने के लिए `/hub moderator add` का उपयोग करें।'
    add:
      success: '{emoji} **{user}** को **{position}** के रूप में मॉडरेटर बना दिया गया है।'
      alreadyModerator: '{emoji} **{user}** पहले से ही मॉडरेटर है।'
    remove:
      success: '{emoji} **{user}** को मॉडरेटर से हटा दिया गया है।'
      notModerator: '{emoji} **{user}** मॉडरेटर नहीं है।'
      notOwner: '{emoji} केवल हब का ओनर ही मैनेजर को हटा सकता है।'
    update:
      success: "{emoji} **{user}** की स्थिति को **{position}** में अपडेट कर दिया गया है।"
      notModerator: '{emoji} **{user}** मॉडरेटर नहीं है।'
      notAllowed: "{emoji} केवल हब मैनेजर ही मॉडरेटर की स्थिति को अपडेट कर सकते हैं।"
      notOwner: "{emoji} केवल हब का ओनर ही मैनेजर की स्थिति को अपडेट कर सकता है।"
  manage:
    dashboardTip: "**🛠️ नया डैशबोर्ड:** बेहतर इंटरफेस और अधिक फ़ीचर्स! इसे आज़माएं [अपने हब के डैशबोर्ड पेज]({url}) पर।"
    enterImgurUrl: एक वैध Imgur इमेज URL दर्ज करें जो गैलरी या एलबम न हो।
    icon:
      changed: हब का आइकन सफलतापूर्वक बदला गया।
      modal:
        title: आइकन संपादित करें
        label: आइकन URL
      selects:
        label: आइकन संपादित करें
        description: इस हब का आइकन बदलें।
    description:
      changed: हब का विवरण सफलतापूर्वक बदला गया।
      modal:
        title: विवरण संपादित करें
        label: विवरण
        placeholder: इस हब के लिए एक विवरण दर्ज करें।
      selects:
        label: विवरण बदलें
        description: इस हब का विवरण बदलें।
    banner:
      changed: हब का बैनर सफलतापूर्वक बदला गया।
      removed: हब का बैनर सफलतापूर्वक हटाया गया।
      modal:
        title: बैनर संपादित करें
        label: बैनर URL
      selects:
        label: बैनर संपादित करें
        description: इस हब का बैनर बदलें।
    visibility:
      success: '{emoji} हब की विज़िबिलिटी सफलतापूर्वक **{visibility}** में बदल दी गई है।'
      selects:
        label: विज़िबिलिटी बदलें
        description: इस हब को पब्लिक या प्राइवेट बनाएं।
    toggleLock:
      selects:
        label: 'हब लॉक/अनलॉक करें'
        description: 'हब चैट्स को लॉक या अनलॉक करें'
      confirmation: 'हब चैट्स अब {status} हैं।'
      announcementTitle: 'हब चैट्स अब {status} हैं।'
      announcementDescription:
        locked: 'केवल मॉडरेटर ही संदेश भेज सकते हैं।'
        unlocked: 'हर कोई संदेश भेज सकता है।'
    toggleNsfw:
      modal:
        title: 'Toggle NSFW Status'
        label: 'NSFW Status'
        description: 'Mark this hub as containing adult content.'
      selects:
        label: 'Toggle NSFW Status'
        description: 'Mark hub as NSFW or SFW content'
      confirmation: 'Hub content rating is now {status}.'
      announcementTitle: 'Hub content rating changed to {status}'
      announcementDescription:
        nsfw: 'This hub now contains adult content. Only NSFW Discord channels can connect to this hub.'
        sfw: 'This hub is now safe for work. All channels can connect to this hub.'
    setNsfw:
      success: '{emoji} **{hub}** has been successfully marked as **{status}**.'
      announcement: "{emoji} **Hub Content Rating Changed**\n\nThis hub is now marked as **{status}**.\n\n{description}"
    nsfwAlreadySet: '{emoji} **{hub}** is already marked as **{status}**.'
    embed:
      visibility: 'विज़िबिलिटी'
      connections: 'कनेक्शन'
      chatsLocked: 'चैट्स लॉक हैं'
      blacklists: 'ब्लैकलिस्ट्स'
      total: 'कुल'
      users: 'उपयोगकर्ता'
      servers: 'सर्वर'
      hubStats: 'हब आँकड़े'
      moderators: 'मॉडरेटर'
      owner: 'ओनर'
    logs:
      title: लॉग्स कॉन्फ़िगरेशन
      reset: '{emoji} `{type}` लॉग्स के लिए लॉग्स कॉन्फ़िगरेशन सफलतापूर्वक रीसेट कर दिया गया है।'
      roleSuccess: '{emoji} प्रकार के लॉग `{type}` अब {role} का उल्लेख करेंगे!'
      roleRemoved: '{emoji} `{type}` प्रकार के लॉग्स अब किसी भी रोल को मेंशन नहीं करेंगे।'
      channelSuccess: '{emoji} `{type}` प्रकार के लॉग्स अब से {channel} पर भेजे जाएंगे!'
      channelSelect: '#️⃣ लॉग्स भेजने के लिए एक चैनल चुनें'
      roleSelect: '🏓 जब कोई लॉग ट्रिगर हो तो मेंशन किए जाने वाला रोल चुनें।'
      reportChannelFirst: '{emoji} कृपया पहले एक लॉग चैनल सेट करें।'
      config:
        title: '`{type}` लॉग्स को कॉन्फ़िगर करें'
        description: |
          {arrow} नीचे दिए गए ड्रॉपडाउन से लॉग चैनल और/या रोल को चुनें जिसे पिंग किया जाएगा।  
          {arrow} आप नीचे दिए गए बटन का उपयोग करके लॉगिंग को बंद भी कर सकते हैं।
        fields:
          channel: चैनल
          role: रोल मेंशन
      reports:
        label: रिपोर्ट्स
        description: उपयोगकर्ताओं से रिपोर्ट प्राप्त करें।
      modLogs:
        label: मॉड लॉग्स
        description: 'मॉडरेशन गतिविधियों के लॉग रखें। (जैसे: ब्लैकलिस्ट, संदेश हटाना आदि)'
      joinLeaves:
        label: जॉइन/लीव
        description: जब कोई सर्वर इस हब में शामिल हो या छोड़ दे, तो लॉग करें।
      appeals:
        label: अपील्स
        description: ब्लैकलिस्ट किए गए उपयोगकर्ताओं/सर्वरों से अपील प्राप्त करें।
      networkAlerts:
        label: नेटवर्क अलर्ट्स
        description: अपने आप ब्लॉक हुए संदेशों के अलर्ट प्राप्त करें।
      messageModeration:
        label: संदेश मॉडरेशन
        description: मॉडरेटर द्वारा हटाए या संपादित किए गए संदेशों का लॉग रखें।
      messageDeletions:
        label: संदेश हटाना
        description: जब किसी संदेश को हटाया जाए, तो लॉग करें।
      messageEdits:
        label: संदेश संपादन
        description: जब किसी संदेश को संपादित किया जाए, तो लॉग करें।
  transfer:
    invalidUser: '{emoji} निर्दिष्ट उपयोगकर्ता नहीं मिला।'
    selfTransfer: '{emoji} आप स्वंय को ओनरशिप ट्रांसफर नहीं कर सकते।'
    botUser: '{emoji} आप किसी बॉट को ओनरशिप ट्रांसफर नहीं कर सकते।'
    confirm: 'क्या आप वाकई **{hub}** की ओनरशिप {newOwner} को ट्रांसफर करना चाहते हैं? आप मैनेजर रोल में डाउनग्रेड कर दिए जाएंगे।'
    cancelled: '{emoji} हब ट्रांसफर रद्द कर दिया गया है।'
    error: '{emoji} हब की ओनरशिप ट्रांसफर करते समय कोई त्रुटि हुई।'
    success: '{emoji} **{hub}** की ओनरशिप सफलतापूर्वक {newOwner} को ट्रांसफर कर दी गई है। आपको मैनेजर के रूप में जोड़ दिया गया है।'
    timeout: '{emoji} हब ट्रांसफर का समय समाप्त हो गया है।'
  rules:
    noRules: "{emoji} इस हब में अभी तक कोई नियम सेट नहीं किए गए हैं। चलिए कुछ जोड़ते हैं!"
    list: "### {emoji} हब नियम\n{rules}"
    maxRulesReached: '{emoji} नियमों की अधिकतम सीमा ({max}) तक पहुंच गई है।'
    ruleExists: '{emoji} यह नियम पहले से मौजूद है।'
    selectedRule: 'चयनित नियम {number}'
    modal:
      add:
        title: हब नियम जोड़ें
        label: नियम का नाम
        placeholder: नियम का पाठ दर्ज करें (अधिकतम 1000 अक्षर)
      edit:
        title: हब नियम संपादित करें
        label: नियम का नाम
        placeholder: नया नियम पाठ दर्ज करें (अधिकतम 1000 अक्षर)
    select:
      placeholder: संपादन या हटाने के लिए कोई नियम चुनें
      option:
        label: नियम {number}
    buttons:
      add: नियम जोड़ें
      edit: नियम संपादित करें
      delete: नियम हटाएं
      back: वापस जाएं
    success:
      add: '{emoji} नियम सफलतापूर्वक जोड़ा गया!'
      edit: '{emoji} नियम सफलतापूर्वक अपडेट किया गया!'
      delete: '{emoji} नियम सफलतापूर्वक हटाया गया!'
    view:
      title: 'नियम {number}'
      select: इस नियम के लिए कोई क्रिया चुनें
  welcome:
    set: '{emoji} वेलकम मैसेज सफलतापूर्वक अपडेट किया गया!'
    removed: '{emoji} वेलकम मैसेज हटा दिया गया है।'
    voterOnly: '{emoji} कस्टम वेलकम मैसेज केवल वोटर के लिए उपलब्ध सुविधा है! इस फ़ीचर को अनलॉक करने के लिए वोट करें।'
    placeholder: |
      {user} को {serverName} से {hubName} में स्वागत है! 🎉  
      सदस्य: {memberCount}, हब: {totalConnections}!
report:
  modal:
    title: रिपोर्ट विवरण
    other:
      label: "रिपोर्ट विवरण।\n"
      placeholder: रिपोर्ट का विस्तृत विवरण।
    bug:
      input1:
        label: बग विवरण
        placeholder: 'उदाहरण: /help कमांड पर बार-बार फेल होना...'
      input2:
        label: विस्तृत विवरण (वैकल्पिक)
        placeholder: "आपके द्वारा उठाए गए चरण। उदाहरण:  \n1. /help चलाएं\n2. 5 सेकंड प्रतीक्षा करें..."
  reasons:
    spam: स्पैम या अत्यधिक संदेश
    advertising: अनचाहा विज्ञापन या स्वयं-प्रचार
    nsfw: NSFW या अनुचित सामग्री
    harassment: उत्पीड़न या बदमाशी
    hate_speech: घृणास्पद भाषण या भेदभाव
    scam: धोखाधड़ी, फ्रॉड, या फ़िशिंग प्रयास
    illegal: अवैध सामग्री या गतिविधियाँ
    personal_info: निजी/व्यक्तिगत जानकारी साझा करना
    impersonation: दूसरों की नकल करना
    breaks_hub_rules: हब नियमों का उल्लंघन
    trolling: ट्रोलिंग या जानबूझकर बाधा उत्पन्न करना
    misinformation: झूठी या भ्रामक जानकारी
    gore_violence: हिंसा या अत्यधिक खून-खराबा
    raid_organizing: रेड या हमले आयोजित करना
    underage: कम उम्र का उपयोगकर्ता या सामग्री
  dropdown:
    placeholder: अपनी रिपोर्ट के लिए एक कारण चुनें
  submitted: '{emoji} रिपोर्ट सफलतापूर्वक सबमिट की गई। अधिक जानकारी के लिए {support_command} में शामिल हों। धन्यवाद!'
  errors:
    noReasonSelected: '{emoji} कोई कारण नहीं चुना गया। कृपया दोबारा प्रयास करें।'
    hubNotFound: '{emoji} हब नहीं मिला। कृपया दोबारा प्रयास करें।'
  description: 'संदेश की रिपोर्ट करें'
  options:
    message:
      description: 'रिपोर्ट करने वाला संदेश'
  contextMenu:
    name: 'संदेश की रिपोर्ट करें'
  selectReason: '{emoji} कृपया अपनी रिपोर्ट के लिए एक कारण चुनें:'
  bug:
    title: बग रिपोर्ट
    affected: प्रभावित घटक
    description: कृपया चुनें कि बॉट के किस हिस्से में आपको समस्या आ रही है।
language:
  set: भाषा सेट कर दी गई है! अब मैं आपसे **{lang}** में उत्तर दूंगा।
errors:
  messageNotSentOrExpired: '{emoji} यह संदेश किसी हब में नहीं भेजा गया था, या इसकी समय-सीमा समाप्त हो गई है, या आपके पास यह कार्रवाई करने की अनुमति नहीं है।'
  notYourAction: "{emoji} क्षमा करें, आप यह कार्रवाई नहीं कर सकते। कृपया यह कमांड स्वयं चलाएं।"
  notMessageAuthor: '{emoji} आप इस संदेश के लेखक नहीं हैं।'
  commandError: |
    {emoji} अरे! इस कमांड को चलाते समय कुछ अप्रत्याशित हुआ। चिंता न करें – यह आपकी गलती नहीं है!

    हमने इस समस्या को स्वचालित रूप से लॉग कर लिया है और हमारी टीम इसे देखेगी। अगर यह बार-बार होता है, तो कृपया हमारे मित्रतापूर्ण [सपोर्ट सर्वर]({support_invite}) में आकर हमें इस त्रुटि ID के बारे में बताएं – हम हमेशा आपकी सहायता करने को तैयार हैं! 🤗

    **त्रुटि ID:** 
    ```{errorId}```
  mustVote: कृपया इस कमांड को उपयोग करने के लिए [InterChat को वोट करें](https://top.gg/bot/769921109209907241/vote), आपका समर्थन हमारे लिए बहुत मूल्यवान है!
  inviteLinks: '{emoji} आप इस हब में इनवाइट लिंक नहीं भेज सकते। इसके बजाय `/connection` में इनवाइट सेट करें! हब मॉड इसे `/hub edit settings` से कॉन्फ़िगर कर सकते हैं।'
  invalidLangCode: '{emoji} अमान्य भाषा कोड। कृपया सुनिश्चित करें कि आपने सही [भाषा कोड](https://cloud.google.com/translate/docs/languages) दर्ज किया है।'
  modalError: '{emoji} मोडल दिखाने में त्रुटि हुई। कृपया फिर से प्रयास करें।'
  unknownServer: '{emoji} अज्ञात सर्वर। कृपया सुनिश्चित करें कि आपने सही **Server ID** दर्ज किया है।'
  unknownNetworkMessage: '{emoji} अज्ञात संदेश। यदि यह पिछले मिनट में भेजा गया है, तो कृपया कुछ और सेकंड प्रतीक्षा करें और फिर से प्रयास करें।'
  userNotFound: '{emoji} उपयोगकर्ता नहीं मिला। इसके बजाय उनकी आईडी इनपुट करने का प्रयास करें।'
  blacklisted: '{emoji} आप या यह सर्वर इस हब ({hub}) से ब्लैकलिस्टेड है।'
  userBlacklisted: '{emoji} आपको इस हब से ब्लैकलिस्ट किया गया है।'
  serverBlacklisted: '{emoji} इस सर्वर को इस हब से ब्लैकलिस्ट किया गया है।'
  serverNotBlacklisted: '{emoji} इनपुट सर्वर को ब्लैकलिस्ट नहीं किया गया है।'
  userNotBlacklisted: '{emoji} इनपुट उपयोगकर्ता को ब्लैकलिस्ट नहीं किया गया है।'
  missingPermissions: '{emoji} आप इस कार्रवाई को करने के लिए निम्नलिखित अनुमतियों को याद कर रहे हैं: **{permissions}**'
  botMissingPermissions: '{emoji} कृपया मुझे जारी रखने के लिए निम्नलिखित अनुमतियाँ प्रदान करें: **{permissions}**'
  unknown: '{emoji} एक अज्ञात त्रुटि हुई। कृपया बाद में पुनः प्रयास करें या हमारे [समर्थन सर्वर]({support_invite}) में शामिल होकर हमसे संपर्क करें।'
  notUsable: '{emoji} यह अब उपयोग करने योग्य नहीं है।'
  cooldown: '{emoji} आप कोल्डाउन पर हैं। कृपया फिर से प्रयास करने से पहले **{time}** तक प्रतीक्षा करें।'
  serverNameInappropriate: '{emoji} आपके सर्वर के नाम में अनुचित शब्द हैं। कृपया हब में शामिल होने से पहले इसे बदलें।'
  banned: |
    {emoji} आपको InterChat का उपयोग करने से प्रतिबंधित किया गया है क्योंकि आपने हमारे [Guidelines](https://interchat.tech/guidelines) का उल्लंघन किया है।  
    अगर आपको लगता है कि यह गलत है तो आप [सपोर्ट सर्वर]({support_invite}) में टिकट खोलकर अपील कर सकते हैं।
  commandLoadingError: 'कमांड लोड करने में त्रुटि हुई। कृपया थोड़ी देर बाद फिर से प्रयास करें।'
  errorLoadingHubs: 'हब लोड करने में त्रुटि'
  errorShowingHubSelection: 'हब चयन स्क्रीन दिखाने में एक त्रुटि हुई। कृपया फिर से प्रयास करें।'
  connectNotFound: 'कनेक्ट कमांड नहीं मिला। कृपया फिर से प्रयास करें।'
config:
  setInvite:
    success: |
      ### {emoji} निमंत्रण लिंक सेट किया गया
      - जब लोग `/joinserver` उपयोग करेंगे तो आपके सर्वर का निमंत्रण उपयोग किया जाएगा।
      - यह `/leaderboard server` में प्रदर्शित होगा।
    removed: '{emoji} निमंत्रण लिंक सफलतापूर्वक हटाया गया!'
    invalid: '{emoji} अमान्य निमंत्रण। कृपया सुनिश्चित करें कि आपने एक मान्य निमंत्रण लिंक दर्ज किया है। जैसे: `https://discord.gg/discord`'
    notFromServer: '{emoji} यह निमंत्रण इस सर्वर से नहीं है।'
badges:
  shown: '{emoji} अब आपके बैज संदेशों में दिखाई देंगे।'
  hidden: '{emoji} अब आपके बैज संदेशों में छिपे रहेंगे।'
  command:
    description: '🏅 अपने बैज प्रदर्शन वरीयताएँ कॉन्फ़िगर करें'
    options:
      show:
        name: 'दिखाएँ'
        description: 'बैज को संदेशों में दिखाने या छिपाने का विकल्प'
  list:
    developer: 'InterChat का मूल डेवलपर'
    staff: 'InterChat का स्टाफ सदस्य'
    translator: 'InterChat का अनुवादक'
    voter: 'पिछले 12 घंटों में InterChat के लिए वोट किया'
global:
  webhookNoLongerExists: '{emoji} इस चैनल का वेबहुक अब मौजूद नहीं है। InterChat का उपयोग जारी रखने के लिए कृपया `/connection unpause` कमांड से वेबहुक फिर से बनाएँ।'
  noReason: कोई कारण प्रदान नहीं किया गया।
  noDesc: कोई विवरण नहीं है।
  version: InterChat v{version}
  loading: '{emoji} कृपया प्रतीक्षा करें, मैं आपका अनुरोध प्रोसेस कर रहा हूं…'
  reportOptionMoved: '{emoji} यह विकल्प स्थानांतरित हो गया है! अब हब मॉडरेटरों को संदेश रिपोर्ट करने के लिए `Apps > Message Info/Report` कमांड का उपयोग करें। InterChat स्टाफ़ को सीधी रिपोर्ट करने के लिए [सपोर्ट सर्वर]({support_invite}) में जाएँ और प्रमाण के साथ एक टिकट खोलें।'
  private: 'प्राइवेट'
  public: 'पब्लिक'
  yes: 'हाँ'
  no: 'नहीं'
  cancelled: '{emoji} रद्द किया गया। कोई परिवर्तन नहीं किया गया।'
  #Common button labels
  buttons:
    openInbox: 'इनबॉक्स खोलें'
    modPanel: 'मॉड पैनल'
    joinServer: 'सर्वर ज्वाइन करें'
    disconnect: 'डिसकनेक्ट करें'
    reconnect: 'फिर से कनेक्ट करें'
    editRule: 'नियम संपादित करें'
    #Setup buttons
    joinPopularHub: 'पॉपुलर हब से जुड़ें'
    createNewHub: 'नया हब बनाएँ'
    finishSetup: 'सेटअप पूरा करें'
    findMoreHubs: 'और हब खोजें'
    supportServer: 'सपोर्ट सर्वर'
    viewChannel: 'चैनल देखें'
    hubDirectory: 'हब निर्देशिका'
    learnMore: 'और जानें'
    connectToHub: 'हब से कनेक्ट करें'
    #Common buttons for calls and other features
    createYourHub: 'अपना हब बनाएँ'
    #Call buttons
    cancelCall: 'कॉल रद्द करें'
    newCall: 'नया कॉल'
    #Leaderboard
    userLeaderboard: 'उपयोगकर्ता लीडरबोर्ड'
    serverLeaderboard: 'सर्वर लीडरबोर्ड'
  #Common modal titles and labels
  modals:
    editConnection:
      title: 'कनेक्शन संपादित करें'
      channelName:
        label: 'चैनल का नाम'
        placeholder: 'चैनल के लिए कस्टम नाम दर्ज करें'
      profanityFilter:
        label: 'अपवित्र वचनों का फिल्टर'
      compact:
        label: 'कॉम्पैक्ट मोड'
    hubCreation:
      title: 'नया हब बनाएं'
      name:
        label: 'हब का नाम'
        placeholder: 'जैसे: गेमिंग कम्युनिटी, Art Gallery'
      description:
        label: 'विवरण '
        placeholder: 'यह हब किस बारे में है?'
    messageInfo:
      title: 'संदेश जानकारी'
    editRule:
      title: 'नियम संपादित करें'
      content:
        label: 'नियम सामग्री '
        placeholder: 'नियम का सामग्री दर्ज करें...'
  #Common messages and responses
  messages:
    selectChannel: 'चैनल चुनें'
    selectHub: 'हब चुनें'
    noHubsAvailable: 'कोई हब उपलब्ध नहीं'
    hubNotFound: 'हब नहीं मिला'
    channelNotFound: 'चैनल नहीं मिला'
    connectionNotFound: 'कनेक्शन नहीं मिला'
    invalidSelection: 'अमान्य चयन'
    operationCancelled: 'ऑपरेशन रद्द किया गया'
    setupComplete: 'सेटअप पूरा हुआ'
    connectionEstablished: 'कनेक्शन स्थापित'
    connectionRemoved: 'कनेक्शन हट गया'
    settingsUpdated: 'सेटिंग अपडेट किया'
    ruleAdded: 'नियम जोड़ा गया'
    ruleUpdated: 'नियम अपडेट किया गया'
    ruleDeleted: 'नियम हटाया गया'
    #Leaderboard messages
    noDataAvailable: 'कोई डेटा उपलब्ध नहीं'
    loadingData: 'डेटा लोड हो रहा है...'
    #Connection status
    connected: 'कनेक्टेड'
    disconnected: 'डिसकनेक्टेड'
    paused: 'रुका हुआ'
    #Hub visibility
    publicHub: 'पब्लिक हब'
    privateHub: 'प्राइवेट हब'
#Leaderboard
leaderboard:
  title: 'वैश्विक संदेश लीडरबोर्ड'
  description: 'हर महीने रीसेट होता है। इस पर आने के लिए किसी भी हब में संदेश भेजें!'
warn:
  description: 'अपने हब में किसी उपयोगकर्ता को चेतावनी (warning) दें'
  options:
    user:
      description: 'जिस उपयोगकर्ता को चेतावनी देनी है'
    hub:
      description: 'जिस हब में चेतावनी देनी है'
    reason:
      description: 'चेतावनी देने का कारण'
  errors:
    cannotWarnSelf: '{emoji} आप अपने आप को चेतावनी नहीं दे सकते।'
  modal:
    title: उपयोगकर्ता को चेतावनी दें
    reason:
      label: कारण
      placeholder: इस उपयोगकर्ता को चेतावनी देने का कारण दर्ज करें...
  success: |
    {emoji} सफलतापूर्वक **{name}** को चेतावनी दी गई।

    -# अगली बार जब वे हब में संदेश भेजेंगे, तो उन्हें नवीनतम चेतावनी दिखाई देगी। एक साथ कई चेतावनियाँ देने से बचें।
  dm:
    title: '{emoji} चेतावनी सूचना'
    description: 'आपको हब **{hubName}** में चेतावनी दी गई है'
  log:
    title: '{emoji} उपयोगकर्ता को चेतावनी दी गई'
    description: |
      {arrow} **उपयोगकर्ता:** {user} ({userId})  
      {arrow} **मॉडरेटर:** {moderator} ({modId})  
      {arrow} **कारण:** {reason}
    footer: 'चेतावनी देने वाला: {moderator}'
calls:
  connected:
    title: "आप कनेक्ट हो चुके हैं! 🎉"
    description: "आपको एक अन्य शानदार सर्वर से मिलाया गया है! हैलो कहें और चैट शुरू करें — यहीं से असली जादू शुरू होता है! ✨"
    instructions: 'कॉल समाप्त करने के लिए `/hangup` उपयोग करें • किसी अन्य सर्वर से मिलाने के लिए `/skip` उपयोग करें'
    serverInfo: '**कनेक्ट किया गया:** {serverName} ({memberCount} सदस्य)'
    duration: '**कॉल अवधि:** {duration}'
    messages: '**एक्सचेंज किए गए संदेश:** {count}'
  waiting:
    title: 'आपका सही मिलान खोज रहे हैं'
    description: 'कॉल कतार में जोड़ा गया। अन्य सर्वर के शामिल होने की प्रतीक्षा कर रहे हैं...'
  failed:
    title: 'कॉल असफल'
    description: "जांचें कि आप पहले से कॉल में नहीं हैं और कुछ क्षण बाद पुनः प्रयास करें।"
    reasons:
      alreadyInCall: 'इस चैनल पर पहले से ही एक कॉल है!'
      alreadyInQueue: 'इस चैनल को पहले से कॉल कतार में जोड़ा गया है!'
      webhookFailed: 'वेबहुक बनाने में विफल। कृपया कुछ देर बाद पुनः प्रयास करें।'
      channelInvalid: 'कॉल को छोड़ नहीं सकते - अमान्य चैनल'
  cancelled:
    title: 'कॉल रद्द'
    description: 'कॉल कतार से बाहर निकल गए। नई कॉल शुरू करने के लिए `/call` उपयोग करें।'
    queueExit: 'आपको कॉल कतार से हटा दिया गया है'
  ended:
    title: 'कॉल समाप्त'
    description: 'चैट करने के लिए धन्यवाद! आशा है कि आपने कुछ नए दोस्त बनाए होंगे! 🌟'
    stats: |
      **कॉल सारांश:**
      • अवधि: {duration}  
      • संदेश: {messages}  
      • सर्वर: {serverName}
    ratePrompt: 'आपका कॉल अनुभव कैसा रहा?'
  skip:
    title: 'नई कॉल खोज रहे हैं'
    description: 'पिछली कॉल समाप्त • अन्य सर्वर की प्रतीक्षा • रद्द करने के लिए `/hangup` उपयोग करें'
    newConnected:
      title: "नई कॉल कनेक्ट हुई!\n"
      description: "आप एक अन्य सर्वर से जुड़े हैं • कॉल समाप्त करने के लिए `/hangup` उपयोग करें"
    error: 'कॉल छोड़ना असमर्थ। कृपया पुनः प्रयास करें।'
  hangup:
    confirm: 'क्या आप वास्तव में इस कॉल को समाप्त करना चाहते हैं?'
    success: 'कॉल सफलतापूर्वक समाप्त। चैट करने के लिए धन्यवाद!'
    queueOnly: 'आपको कॉल कतार से हटा दिया गया है।'
  buttons:
    endCall: 'कॉल समाप्त करें'
    skipServer: 'सर्वर छोड़ें'
    skipAgain: 'फिर से छोड़ें'
    cancelCall: 'कॉल रद्द करें'
    newCall: 'नई कॉल'
    exploreHubs: 'Explore Hubs'
    browseAllHubs: 'Browse All Hubs'
    ratePositive: 'Good Call 👍'
    rateNegative: 'Poor Call 👎'
    reportCall: 'Report Call'
  hubs:
    promotion:
      title: '🌟 Discover InterChat Hubs!'
      description: 'Calls are in beta. For a more reliable experience, try InterChat Hubs - our main feature for connecting servers!'
    benefits:
      title: 'Why Choose Hubs?'
      description: 'Hubs offer a more reliable and feature-rich experience than calls:'
      list: |
        • **Persistent Connections** - Messages stay even when you're offline
        • **Multiple Communities** - Join various themed hubs or create your own
        • **Advanced Moderation** - Content filtering, anti-spam, and more
        • **Rich Features** - Custom welcome messages, rules, and settings
        • **Active Communities** - Thousands of servers already connected
    main:
      title: 'InterChat Hubs'
      description: 'Hubs are the main feature of InterChat, connecting servers in persistent chat communities'
  system:
    callStart: |
      {emoji} **Your Call is Connected!** Say hello! 🎉
      > - Say hello and start chatting with the other server!
      > - Use `/hangup` when you're ready to end the call
      > - Remember to keep things friendly and follow our [community guidelines]({guidelines})
  rating:
    success: 'Thanks for rating! Your **{type}** feedback has been recorded for {count} participant{plural}.'
    alreadyRated: '{emoji} You have already rated this call.'
    invalidButton: '{emoji} Invalid rating button. Please try again.'
    noCallData: '{emoji} Unable to find call data. The call might have ended too long ago.'
    noParticipants: '{emoji} Unable to find participants from the other channel.'
  report:
    prompt: '{emoji} Please select a reason for your report:'
    invalidButton: '{emoji} Invalid report button. Please try again.'
  leaderboard:
    title: 'Global Calls Leaderboard'
    description: 'Shows data from this month'
    noData: 'No data available.'
    userTab: 'User Leaderboard'
    serverTab: 'Server Leaderboard'
  errors:
    guildOnly: 'This command can only be used in a server text channel.'
#Command descriptions and help text
commands:
  about:
    title: 'About InterChat'
    description: 'Learn more about InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    support_text: 'Need help? Join our support server for assistance!'
    features:
      title: 'Features'
      list: |
        - Connect with other servers for active cross-server discussions
        - Messages flow naturally between servers in real-time
        - Build engaged topic-focused communities
        - Moderation tools to keep discussions healthy
        - Visual dashboard to manage your hubs, servers, and settings
    buttons:
      vote: 'Vote on top.gg'
      invite: 'Invite InterChat'
      dashboard: 'Visit Dashboard'
      support: 'Join Support Server'
      credits: 'View Credits'
    credits:
      title: 'Credits'
      developers: 'Developers'
      staff: 'Staff'
      translators: 'Translators'
      mentions: 'Special Mentions'
      mascot: 'Mascot'
      top_voter: 'Top Voter'
      footer: 'Version {version}'
    sections:
      invite: 'Invite InterChat to your server'
      dashboard: 'Visit the InterChat dashboard'
      support: 'Join our support server'
      credits: 'View the InterChat credits'
    errors:
      serverOnly: 'This command can only be used in a server.'
  help:
    description: '📚 Explore InterChat commands with our new help system'
    options:
      command:
        description: 'The command to get info about'
      category:
        description: 'View commands by category'
    errors:
      categoryNotFound: '{emoji} Category not found.'
      commandNotFound: '{emoji} Command `{command}` not found.'
      showingCategory: '{emoji} An error occurred while showing the category.'
      showingCommand: '{emoji} An error occurred while showing the command help.'
      showingMenu: '{emoji} An error occurred while showing the help menu.'
      showingSearch: '{emoji} An error occurred while showing the search interface.'
  setup:
    description: 'Setup InterChat in your server'
    errors:
      serverOnly: 'This command can only be used in a server.'
      missingPermissions: |
        I need the following permissions to work properly:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please give me these permissions and try again!
        Need help? [Join our support server]({supportInvite})
      setupError: 'There was an error starting the setup process. Please try again later.'
      completionError: 'There was an error completing the setup. Please try again or contact support if the issue persists.'
      channelNotSelected: 'No channel was selected. Please try again.'
      invalidChannelType: 'Please select a text or thread channel. Voice channels, forums, and other channel types are not supported.'
      missingChannelPermissions: |
        I need the following permissions in {channel}:
        - Manage Webhooks
        - Send Messages
        - Manage Messages
        - Embed Links

        Please update the channel permissions and try again!
      channelAlreadyConnected: 'This channel is already connected to the hub "{hubName}". Please select a different channel.'
      channelNotFound: 'Selected channel no longer exists. Please run the setup command again.'
      hubNotFound: 'This hub no longer exists. Please choose another one.'
      commandLoadingError: 'Failed to load commands. Please try again or join our support server for help.'
      interactionError: '{emoji} Oops! Something went a bit wonky there. No worries though - just give it another try! If this keeps happening, our friendly support team is always here to help.'
      userMismatch: 'This setup is for another user.'
      serverRequired: 'You must be in a server to use this.'
      timeout: "No worries! The setup just timed out while waiting for your response. When you're ready to continue your InterChat journey, just run `/setup` again and we'll pick up right where we left off!"
      noAvailableHubs: 'Your server is already connected to all available popular hubs! Try creating a new hub instead.'
      hubCreationFailed: 'Failed to create hub. Please try again.'
      validationError: 'Invalid hub data provided. Please try again.'
    welcome:
      title: '🎉 Welcome to InterChat Setup!'
      description: |
        Hey! Let's get your server connected to InterChat.

        This setup will guide you through everything you need:

        📍 Select a channel for the chat

        🏠 Join or create a hub (your community space)

        ⚙️ Finish setup to start chatting

        What's a Hub? It's a shared space where servers connect and chat together. Simple as that.

        Let's get this set up and running. 🚀
    channelSelection:
      title: '📍 Step 1: Choose Your Perfect Channel'
      description: "Let's pick the channel where all the exciting InterChat conversations will happen! This can be any text channel in your server - maybe create a special one just for this?"
      placeholder: 'Select a channel'
      tips:
        title: '💡 Helpful Tips for Success'
        content: |
          - **Create something special:** Try naming it `#interchat`, `#global-chat`, or `#world-chat`
          - **Think about visibility:** Make sure members who want to join the fun can see this channel
          - **Room to grow:** You can always connect more channels to different communities later
          - **Keep it organized:** A dedicated channel helps keep conversations flowing smoothly
    hubChoice:
      title: 'InterChat Setup (2/4)'
      description: "Great! Messages will appear in {channel}. Now, let's connect to a hub!"
      whatIsHub:
        title: 'What is a Hub?'
        description: "A hub is InterChat's main feature - a shared chat space where multiple servers can talk together. Hubs are persistent communities that stay connected 24/7, unlike temporary calls."
      popularHubs:
        title: 'Popular Hubs'
        description: |
          - Join thriving active communities with thousands of users
          - Start chatting immediately with other servers
          - Perfect for new users to experience InterChat
          - No additional setup required - just connect and chat!
      createHub:
        title: 'Create Your Own Hub'
        description: |
          - Start your own community themed around your interests
          - Full control over settings, moderation, and features
          - Invite specific servers to create a private network
          - Set custom rules, welcome messages, and more
      note: 'You can always join more hubs later with the `/connect` command!'
    hubSelection:
      title: 'InterChat Setup (2/4)'
      description: 'Choose a hub to join from our most active communities:'
      placeholder: 'Choose a hub to join'
      tip: '**Tip:** You can always join more hubs later using `/connect` and [the hub list](https://interchat.app/hubs).'
    hubCreation:
      modal:
        title: 'Create New Hub'
        name:
          label: 'Hub Name'
          placeholder: 'e.g., Gaming Community, Art Gallery'
        description:
          label: 'Description'
          placeholder: 'What is this hub about?'
    nextSteps:
      created:
        title: '✨ Almost Done!'
        description: "Your Hub \"{hubName}\" is Ready!\nClick Finish Setup to complete the process. After that, follow these steps:"
        inviteLink:
          title: '1️⃣ Create an Invite Link'
          description: "{hubInviteCommand} `hub:{hubName}`\nThis will generate an invite link you can share with other servers"
        shareHub:
          title: '2️⃣ Share Your Hub'
          description: |
            Share the invite link with at least one other server to start chatting!
            {dot} Send to your friends & servers
            {dot} Share in our [support server]({supportInvite})
        configuration:
          title: '3️⃣ Essential Configuration'
          description: |
            {hubRulesCommand}
            Create hub rules and guidelines

            {hubLoggingCommand}
            Set up logging channels for hub events

            {hubAntiSwearCommand}
            Configure word filters and auto-moderation

            {hubSettingsCommand}
            Manage message types and notifications
        proTips:
          title: '💡 Pro Tips'
          description: |
            {dot} Your hub is private by default - only servers with invites can join
            {dot} Vote for InterChat to unlock custom welcome messages and colors
            {dot} You can publish your hub to the [hub directory]({website}/hubs) using {hubVisibilityCommand}
            {dot} Join our [support server]({supportInvite}) for hub management tips!
        copyCommand: "`/hub invite create hub:{hubName}`\n✨ Command copied! Run this to create an invite link."
      joined:
        title: '✨ Ready to Join?'
        description: "Ready to Join \"{hubName}\"?\nClick Finish Setup to join the hub. After joining, you can use these commands:"
        commands: |
          {connectionEditCommand}
          Customize how you receive/send messages to the hub

          {connectionListCommand}
          View all your connected hubs

          {website}/hubs (New :sparkles:)
          Join more hubs
        help: 'Join our [support server]({supportInvite}) if you have questions!'
    completion:
      title: 'Setup Complete!'
      description: 'Your server has been successfully connected to the hub in {channel}. You can now start chatting!'
    buttons:
      supportServer: 'Support Server'
      documentation: 'Documentation'
      goBack: 'Go Back'
      finishSetup: 'Finish Setup'
      hubDirectory: 'Hub Directory'
      learnMore: 'Learn More'
      viewChannel: 'View Channel'
      joinPopularHub: 'Join Popular Hub'
      createNewHub: 'Create New Hub'
      copyInviteCommand: 'Copy Invite Command'
      findMoreHubs: 'Find More Hubs'
    existingConnections:
      title: 'Existing Connections'
      description: |
        Your server is already connected to the following hubs:

        {connectionList}

        You can continue to add more connections if you'd like.
  language:
    description: '🈂️ Set the language in which I should respond to you'
    options:
      lang:
        description: 'The language to set'
  badges:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  tutorial:
    description: '📚 Learn how to use InterChat with interactive tutorials'
    subcommands:
      start:
        description: 'Start a specific tutorial'
        options:
          tutorial:
            description: 'The tutorial to start'
      setup:
        description: 'Start the server setup tutorial (for admins)'
  rules:
    description: '📋 Sends the network rules for InterChat.'
    options:
      hub:
        description: 'View rules for a specific hub'
    hubRules:
      title: '{hubName} Rules'
      description: 'The following rules apply to this hub'
    botRules:
      title: 'InterChat Rules'
      description: 'Bot-wide rules for all users'
#Tutorial system
tutorial:
  errors:
    notFound: '{emoji} Tutorial not found.'
    noSteps: '{emoji} This tutorial has no steps.'
    prerequisitesRequired: '{emoji} You need to complete the prerequisite tutorials first.'
    noInProgress: '{emoji} You have no tutorials in progress.'
  completion:
    completed: '{emoji} Tutorial completed! Great job!'
    nextRecommendation: 'Next recommended tutorial: {tutorialName}'
  categories:
    newUser: 'New User Tutorials'
    admin: 'Server Admin Tutorials'
    moderator: 'Moderator Tutorials'
    all: 'General Tutorials'
  list:
    title: 'Available Tutorials'
    noTutorials: 'No tutorials available at the moment.'
    description: 'Choose a tutorial to get started with InterChat'
  progress:
    completed: '✅ Completed'
    inProgress: '▶️ In Progress'
    notStarted: '⭕ Not Started'
  buttons:
    start: 'Start Tutorial'
    resume: 'Resume'
    review: 'Review'
    next: 'Next'
    previous: 'Previous'
    skip: 'Skip'
    finish: 'Finish'
  about:
    description: '🚀 Learn how InterChat helps grow Discord communities'
    title: 'About InterChat'
    description_text: 'InterChat connects Discord communities through active cross-server discussions. Messages flow naturally between servers in real-time, helping you build engaged topic-focused communities.'
    features:
      title: 'What makes InterChat different:'
      list: |
        - Built for real communities - Designed with Discord server owners' needs in mind
        - Active hubs - Find and join thriving communities around shared interests
        - Privacy first - Full control over your hub's connections and settings
        - Smart moderation - AI-powered image filtering and advanced content filtering keeps discussions healthy
        - Visual dashboard - Manage your hubs, servers, and settings through our web interface
    sections:
      invite: 'Invite InterChat to your server:'
      dashboard: 'Visit the InterChat dashboard:'
      support: 'Join our support server:'
      credits: 'Check out the InterChat team!'
    buttons:
      invite: 'Invite'
      dashboard: 'Dashboard'
      support: 'Support Server'
      credits: 'Credits & Team'
      vote: 'Vote!'
    support_text: 'InterChat is completely free to use. If you like InterChat, consider supporting us on Ko-fi! Or even a vote on top.gg helps us a lot!'
    credits:
      title: 'CREDITS'
      developers: 'Developers:'
      staff: 'Staff: ([Check Applications!]({website}/apply))'
      translators: 'Translators:'
      mentions: 'Deserving Mentions:'
      mascot: '(maker of our cute mascot chipi {emoji})'
      top_voter: '([top voter]({vote_url}) of all time {emoji})'
      footer: 'InterChat v{version} • Made with ❤️ by the InterChat Team'
#Hub configuration and management
hubConfig:
  antiSwear:
    title: 'Anti-Swear Configuration'
    description: 'Configure word filters and auto-moderation for this hub'
    noRules: "Let's set up some anti-swear rules!\nUse the `Add Rule` button to create one."
    selectRule: "Select a rule to edit it's words/actions"
    placeholder: 'Select a log type to configure'
    validating: '{emoji} Validating anti-swear rule...'
    buttons:
      addRule: 'Add Rule'
      editRule: 'Edit Rule'
      deleteRule: 'Delete Rule'
      back: 'Back'
    modal:
      addRule: 'Add Anti-Swear Rule'
      editRule: 'Editing Anti-Swear Rule'
      ruleName: 'Rule Name'
      words: 'Words'
      wordsPlaceholder: 'Words seperated by comma. (Use * for wildcard). Eg. word1, *word2*, *word3, word4*'
  logging:
    title: 'Logs Configuration'
    description: 'Configure logging channels and notifications for hub events'
    placeholder: 'Select a log type to configure'
    channelSelect: '#️⃣ Select a channel to send the logs'
    roleSelect: '🏓 Select the role to mention when a log is triggered.'
    config:
      title: 'Configure `{type}` Logs'
      description: |
        {arrow} Select a log channel and/or role to be pinged from the dropdown below.
        {arrow} You can also disable logging by using the button below.
      fields:
        channel: 'Channel'
        role: 'Role Mention'
    types:
      reports:
        label: 'Reports'
        description: 'Receive reports from users.'
      modLogs:
        label: 'Mod Logs'
        description: 'Log Moderation actions. (eg. blacklist, message deletes, etc.)'
      joinLeaves:
        label: 'Join/Leave'
        description: 'Log when a server joins or leaves this hub.'
      appeals:
        label: 'Appeals'
        description: 'Recieve appeals from blacklisted users/servers.'
      networkAlerts:
        label: 'Network Alerts'
        description: 'Recieve alerts about automatically blocked messages.'
      messageModeration:
        label: 'Message Moderation'
        description: 'Log message deletions and edits by moderators.'
  rules:
    title: 'Hub Rules Configuration'
    description: 'Manage rules and guidelines for your hub'
    noRules: "This hub has no rules configured yet. Let's add some!"
    maxRulesReached: 'Maximum number of rules ({max}) reached.'
    ruleExists: 'This rule already exists.'
    placeholder: 'Select a rule to edit or remove'
    modal:
      add:
        title: 'Add Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the rule text (max 1000 characters)'
      edit:
        title: 'Edit Hub Rule'
        label: 'Rule Text'
        placeholder: 'Enter the new rule text (max 1000 characters)'
    buttons:
      add: 'Add Rule'
      edit: 'Edit Rule'
      delete: 'Delete Rule'
      back: 'Back'
    view:
      title: 'Rule {number}'
      select: 'Select an action for this rule'
  appealCooldown:
    errors:
      invalidCooldown: 'Please provide a valid cooldown duration.'
      tooShort: 'Cooldown must be atleast **1 hour** long.'
      tooLong: 'Cooldown cannot be longer than **1 year**.'
    success: '{emoji} Appeal cooldown has been set to **{hours}** hour(s).'
#Interaction and modal text
interactions:
  modals:
    warn:
      title: 'Warn User'
      reason:
        label: 'Reason'
        placeholder: 'Enter the reason for warning this user...'
  buttons:
    refresh: 'Refresh'
    cancel: 'Cancel'
    confirm: 'Confirm'
    back: 'Back'
    next: 'Next'
    finish: 'Finish'
    save: 'Save'
    delete: 'Delete'
    edit: 'Edit'
    add: 'Add'
    remove: 'Remove'
    view: 'View'
    close: 'Close'
  placeholders:
    selectOption: 'Select an option'
    selectChannel: 'Select a channel'
    selectRole: 'Select a role'
    selectUser: 'Select a user'
    selectServer: 'Select a server'
    enterText: 'Enter text here...'
    enterReason: 'Enter a reason...'
    enterDescription: 'Enter a description...'
#Moderation panel
modPanel:
  buttons:
    serverBanned: 'Server Banned'
    banServer: 'Ban Server'
  modals:
    blacklistUser: 'Blacklist User'
    blacklistServer: 'Blacklist Server'
#General UI text
ui:
  titles:
    error: 'Error'
    warning: 'Warning'
    success: 'Success'
    info: 'Information'
    confirmation: 'Confirmation'
  messages:
    loading: 'Loading...'
    processing: 'Processing your request...'
    pleaseWait: 'Please wait...'
    tryAgain: 'Please try again.'
    contactSupport: 'Please contact support if this issue persists.'
    operationCancelled: 'Operation cancelled.'
    operationCompleted: 'Operation completed successfully.'
    noDataAvailable: 'No data available.'
    permissionDenied: 'Permission denied.'
    invalidInput: 'Invalid input provided.'
    timeout: 'Operation timed out.'
    notFound: 'Not found.'
    alreadyExists: 'Already exists.'
    unavailable: 'Currently unavailable.'
#Message management commands
deleteMsg:
  description: 'Delete a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to delete'
  contextMenu:
    name: 'Delete Message'
  processing: '{emoji} Your request has been queued. Messages will be deleted shortly...'
  alreadyDeleted: '{emoji} This message is already deleted or is being deleted by another moderator.'
editMsg:
  description: 'Edit a message you sent using interchat.'
  options:
    message:
      description: 'The message ID or message link of the message to edit'
    newContent:
      description: 'The new content for the message'
  contextMenu:
    name: 'Edit Message'
  modal:
    title: 'Edit Message'
    content:
      label: 'New Content'
      placeholder: 'Enter the new message content...'
  processing: '{emoji} Your request has been queued. Messages will be edited shortly...'
  alreadyEdited: '{emoji} This message is already being edited by another moderator.'
inbox:
  description: 'Check your inbox for latest important updates & announcements'
  title: '📬 InterChat Inbox'
  subtitle:
    new: 'Latest announcements and updates'
    older: 'Viewing older announcements'
  empty:
    title: '📬 All caught up!'
    description: "I'll let you know when there's more. But for now, there's only Chipi here: {emoji}"
  buttons:
    viewOlder: 'View Older'
    previous: 'Previous'
    next: 'Next'
  postedOn: 'Posted on {date}'
joinserver:
  description: 'Join a server or send a request to join a server through InterChat.'
  options:
    servername:
      description: 'The name of the server you want to join'
    messageorserverid:
      description: 'The message ID or server ID'
  errors:
    channelOnly: 'This command can only be used in a channel.'
    missingTarget: 'You must provide a message ID or server ID'
  success:
    inviteSent: "{emoji} I have DM'd you the invite link to the server!"
  request:
    title: 'Join Request'
    description: 'You requested to join the server `{serverName}` through InterChat. Here is the invite link:'
    broadcast: 'User `{username}` from `{guildName}` has requested to join this server. Do you want to accept them?'
  buttons:
    accept: 'Accept'
    reject: 'Reject'
  response:
    sent: "{emoji} Your request has been sent to the server. You will be DM'd the invite link if accepted."
    creating: '{emoji} This server does not have an invite link yet. Creating one...'
    dmSent: '{emoji} The invite link has been sent to the user.'
    dmFailed: '{emoji} The invite link could not be sent to the user. They may have DMs disabled.'
  status:
    accepted: 'Accepted by {username}'
    rejected: 'Rejected by {username}'
messageInfo:
  description: 'Get information about a message.'
  options:
    message:
      description: 'The message to get information about.'
  contextMenu:
    name: 'Message Info'
  errors:
    profileFetch: 'Failed to fetch user profile.'
connect:
  description: '🔗 Connect your channel to an InterChat hub'
  options:
    channel:
      description: 'The channel you want to connect to a hub'
    invite:
      description: 'The invite code of the private hub you want to join'
  errors:
    invalidIds: '{emoji} Invalid hub or channel ID.'
    channelNotFound: '{emoji} Channel not found or not a text channel.'
disconnect:
  description: '👋 Disconnect a channel from a hub'
#Staff commands
ban:
  description: '🔨 Ban users or servers from InterChat with comprehensive options'
  options:
    duration:
      description: 'Ban duration'
    reason:
      description: 'Reason for the ban (required)'
    user:
      description: 'User to ban (required for user bans)'
    serverId:
      description: 'Server ID to ban (required for server bans)'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to ban.'
unban:
  description: '🔓 Unban users or servers from InterChat'
  options:
    user:
      description: 'User to unban'
    serverId:
      description: 'Server ID to unban'
  errors:
    bothSpecified: '{emoji} Please specify either a user or a server, not both.'
    noneSpecified: '{emoji} Please specify either a user or a server to unban.'
    invalidTarget: '{emoji} Invalid ban target. Please use the autocomplete to select a valid ban.'
    banNotFound: '{emoji} Ban not found.'
    serverBanNotFound: '{emoji} Server ban not found.'
    loadFailed: '{emoji} Failed to load ban information.'
achievements:
  description: "🏆 View your achievements or another user's achievements"
  options:
    user:
      description: 'The user to view achievements for (defaults to yourself)'
    view:
      description: 'Choose which achievements to view'
  title: "🏆 {username}'s Achievements"
  progress: '**Progress:** {unlocked}/{total} achievements unlocked'
  errors:
    userNotFound: 'User not found.'
achievement:
  settings:
    enabled: 'Achievement notifications are now **enabled**. You will receive notifications when you unlock new achievements!'
    disabled: 'Achievement notifications are now **disabled**. You will no longer receive notifications when you unlock achievements.'
profile:
  description: "View your profile or someone else's InterChat profile."
  options:
    user:
      description: 'The user to view the profile of.'
  errors:
    userNotFound: 'User not found.'
rank:
  description: 'Display user rank and statistics'
  options:
    user:
      description: 'The user to get the rank of'
  errors:
    createFailed: 'Failed to create rank card. Please try again later.'
#Userphone commands
call:
  description: '📞 [BETA] Start a call with another server'
  errors:
    skipFailed: 'Skip Failed'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
hangup:
  description: '📞 End the current call'
  callEnded: '{user} ended the call.'
  errors:
    error: 'Error'
    callFailed: 'Call Failed'
    guildOnly: '{emoji} This command can only be used in a server text channel.'
    connectNotFound: '{emoji} Could not find the connect command. Please use `/connect` manually.'
skip:
  description: '[BETA] Skip the current call and find a new match'
  errors:
    error: 'Error'
    skipFailed: 'Skip Failed'
voteCommand:
  description: '✨ Voting perks and vote link.'
#Welcome message system for new servers
welcome:
  buttons:
    back: 'Back'
  calls:
    title: 'Setup Calls'
    description: 'Learn about InterChat calls - instant server-to-server connections!'
    commands: |
      ### Available Call Commands

      **{callCommand}** - Start a call with another server
      **{skipCommand}** - Skip current call and find a new match
      **{hangupCommand}** - End the current call
      **{leaderboardCommand}** - View call leaderboards
    examples:
      title: 'How to Use Calls'
      content: |
        1. Run `/call` in any text channel to start
        2. Wait to be matched with another server
        3. Chat with the other server in real-time
        4. Use `/skip` to find a different server
        5. Use `/hangup` when you're done chatting

        **Note:** Calls are in beta - for a more reliable experience, try InterChat Hubs!
  setup:
    title: '🏠 Setup Cross-Server Chat'
    description: 'Connect to hubs for persistent cross-server communities!'
    instructions: |
      ### हब्स के साथ शुरुआत करें

      **{setupCommand}** - अपना पहला हब जॉइन करने के लिए गाइडेड सेटअप  
      **{connectCommand}** - एक विशेष हब से कनेक्ट करें  

      **हब्स क्या हैं?**  
      हब्स वे स्थायी चैट समुदाय हैं जहां कई सर्वर आपस में कनेक्ट होकर 24/7 चैट करते रहते हैं। कॉल के विपरीत, हब संदेश तब भी रहते हैं जब आप ऑनलाइन न हों!

      **हब्स क्यों चुनें?**  
      - स्थायी कनेक्शन जो हमेशा सक्रिय रहते हैं  
      - कई विषय-आधारित समुदायों में शामिल हों  
      - उन्नत मॉडरेशन और फिल्टरिंग  
      - कस्टम स्वागत संदेश और नियम  
      - पहले से ही हजारों सर्वर जुड़े हुए हैं  

    buttons:
      runSetup: "अभी सेटअप चलाएँ\n"
    errors:
      commandNotFound: '{emoji} सेटअप कमांड नहीं मिला। कृपया इसे मैन्युअली `/setup` कमांड से चलाने का प्रयास करें।'
