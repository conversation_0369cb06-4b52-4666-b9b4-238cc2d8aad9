# Flexible Command Architecture

## Overview

The new command architecture supports flexible response patterns, Discord components integration, and organized subcommand file structure while maintaining backward compatibility.

## Key Features

✅ **Flexible Response Patterns**: Commands can choose how to handle responses  
✅ **Discord Components Support**: Native integration with buttons, select menus, modals  
✅ **Subcommand File Organization**: Separate files for subcommands like legacy system  
✅ **Type Safety**: Full TypeScript support throughout  
✅ **Backward Compatibility**: Existing commands continue to work  
✅ **DI Integration**: Seamless dependency injection support  

## Response Patterns

### 1. DIRECT Pattern
Command handles response directly via `interaction.reply()` and returns `void`.

```typescript
async execute(ctx: Context): Promise<void> {
  await ctx.interaction.reply({
    content: 'Response handled directly!',
    ephemeral: true
  });
  return; // Registry does nothing
}
```

**Use Cases:**
- Simple responses
- Commands that need immediate feedback
- Complex interaction flows
- When you need full control over the response

### 2. STRUCTURED Pattern
Command returns `CommandResult` for registry to process.

```typescript
async execute(ctx: Context): Promise<CommandResult> {
  return {
    success: true,
    embed: this.createSuccessEmbed('Title', 'Description'),
    ephemeral: true,
    followUp: 'Optional follow-up message'
  };
}
```

**Use Cases:**
- Standard command responses
- Error handling with consistent formatting
- Commands that need follow-up messages
- Backward compatibility with existing commands

### 3. ENHANCED Pattern
Command returns `EnhancedCommandResult` with Discord components and advanced features.

```typescript
async execute(ctx: Context): Promise<EnhancedCommandResult> {
  const button = new ButtonBuilder()
    .setCustomId('my_button')
    .setLabel('Click Me!')
    .setStyle(ButtonStyle.Primary);

  const actionRow = new ActionRowBuilder<ButtonBuilder>()
    .addComponents(button);

  return {
    success: true,
    embed: this.createInfoEmbed('Title', 'Description'),
    ephemeral: true,
    components: [actionRow],
    allowedMentions: { parse: [] },
    flags: []
  };
}
```

**Use Cases:**
- Interactive commands with buttons/menus
- Confirmation dialogs
- Multi-step workflows
- Rich user interfaces

## Subcommand File Structure

### Legacy vs New Structure

**Legacy System:**
```
src/commands/Staff/badge/
├── index.ts          # Main command
├── add.ts           # Add subcommand
└── remove.ts        # Remove subcommand
```

**New System:**
```
packages/bot/src/presentation/commands/staff/badge/
├── BadgeCommandHandler.ts      # Main command
├── AddBadgeSubcommand.ts      # Add subcommand
└── RemoveBadgeSubcommand.ts   # Remove subcommand
```

### Creating Subcommands

1. **Create subcommand file:**
```typescript
// AddBadgeSubcommand.ts
@injectable()
export class AddBadgeSubcommand extends BaseSubcommandHandler {
  readonly name = 'add';
  readonly description = 'Add a badge to a user';

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Implementation
  }
}
```

2. **Register in main command:**
```typescript
// BadgeCommandHandler.ts
constructor(
  @inject(TYPES.UserRepository)
  private readonly userRepository: IUserRepository
) {
  super();
  
  this.registerSubcommand(new AddBadgeSubcommand(this.userRepository));
  this.registerSubcommand(new RemoveBadgeSubcommand(this.userRepository));
}

async execute(ctx: Context): Promise<FlexibleCommandResponse> {
  const subcommand = ctx.options.getSubcommand();
  return await this.executeSubcommand(subcommand, ctx);
}
```

## Discord Components Integration

### Button Interactions

```typescript
// In command
const button = new ButtonBuilder()
  .setCustomId('confirm_action_userId_data')
  .setLabel('Confirm')
  .setStyle(ButtonStyle.Danger);

return {
  success: true,
  embed: this.createInfoEmbed('Confirm Action', 'Are you sure?'),
  components: [new ActionRowBuilder<ButtonBuilder>().addComponents(button)]
};
```

### Component Handlers

```typescript
// BadgeRemovalInteractionHandler.ts
@injectable()
export class BadgeRemovalInteractionHandler extends BaseInteractionHandler {
  readonly handlerId = 'badge_remove';

  async handleButton(context: Context, interaction: ButtonInteraction): Promise<void> {
    // Handle button click
    const [action, userId, badgeType] = interaction.customId.split('_').slice(2);
    
    if (action === 'confirm') {
      // Process confirmation
    }
  }
}
```

## Migration Guide

### From Legacy Commands

1. **Update imports:**
```typescript
// Old
import { BaseCommandHandler, CommandResult } from '../BaseCommandHandler.js';

// New
import { BaseCommandHandler, FlexibleCommandResponse } from '../BaseCommandHandler.js';
```

2. **Update execute method:**
```typescript
// Old
async execute(ctx: Context): Promise<CommandResult> {
  return { success: true, embed: ... };
}

// New - Choose your pattern
async execute(ctx: Context): Promise<FlexibleCommandResponse> {
  // Option 1: Direct
  await ctx.interaction.reply({ content: '...' });
  return;
  
  // Option 2: Structured (same as before)
  return { success: true, embed: ... };
  
  // Option 3: Enhanced
  return { success: true, embed: ..., components: [...] };
}
```

3. **Extract subcommands (optional):**
```typescript
// Create separate files for each subcommand
// Register them in the main command constructor
```

### Backward Compatibility

- Existing commands returning `CommandResult` continue to work
- No breaking changes to the public API
- Gradual migration is supported
- Legacy and new patterns can coexist

## Best Practices

### When to Use Each Pattern

- **DIRECT**: Simple responses, immediate feedback, complex flows
- **STRUCTURED**: Standard responses, error handling, follow-ups
- **ENHANCED**: Interactive UIs, confirmations, rich experiences

### Error Handling

```typescript
try {
  // Command logic
  return { success: true, ... };
} catch (error) {
  console.error('Command error:', error);
  
  return {
    success: false,
    embed: this.createErrorEmbed('Error', 'Something went wrong'),
    ephemeral: true
  };
}
```

### Component Best Practices

- Use descriptive custom IDs: `action_context_data`
- Include necessary data in custom ID for stateless handling
- Always handle component expiration
- Provide clear user feedback

## Testing

Run type checking to ensure everything works:

```bash
cd packages/bot
bun type-check
```

Test the flexible response patterns:

```bash
# Use the example command
/response-example direct
/response-example structured
/response-example enhanced
/response-example deferred
```
