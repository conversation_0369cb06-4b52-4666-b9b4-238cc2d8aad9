# Dynamic Command System

## Overview

The new command system uses dynamic loading to automatically discover and register command handlers. This eliminates the need to manually import and register each command in the DI container.

## How It Works

### 1. File System Discovery
The `DynamicCommandLoader` scans the `packages/bot/src/presentation/commands/` directory recursively and finds all `.js` files that export command handlers.

### 2. Automatic Registration
For each discovered command handler:
- Creates an instance
- Validates it extends `BaseCommandHandler`
- Registers it in the DI container with both individual and multi-inject symbols
- Logs the registration for debugging

### 3. Symbol Generation
Command symbols are automatically generated from the command name:
- `stats` → `StatsCommandHandler`
- `ping` → `PingCommandHandler`
- `user-info` → `UserInfoCommandHandler`

## Benefits

✅ **No Manual Imports**: Add a new command file, and it's automatically loaded
✅ **No TYPES Updates**: Symbols are generated automatically
✅ **No Container Updates**: Commands are registered dynamically
✅ **Supports Subcommands**: Works with nested directory structures
✅ **Clean Architecture**: Maintains separation of concerns

## Usage

### Creating a New Command

1. Create a new file in `packages/bot/src/presentation/commands/[category]/`
2. Export a class that extends `BaseCommandHandler`
3. That's it! The command is automatically loaded

```typescript
// packages/bot/src/presentation/commands/information/HelpCommandHandler.ts
import { injectable } from 'inversify';
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommandHandler, CommandResult, CommandCategory } from '../BaseCommandHandler.js';
import type { Context } from '../../../shared/context/index.js';

@injectable()
export class HelpCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'help',
    description: 'Get help with commands',
    category: CommandCategory.INFORMATION,
    cooldown: 3000,
    ownerOnly: false,
    guildOnly: false,
    permissions: [],
  };

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<CommandResult> {
    await ctx.reply({
      content: 'Help information here...',
      ephemeral: true
    });

    return {
      success: true,
      message: 'Help displayed',
      ephemeral: true
    };
  }
}
```

### Directory Structure

```
packages/bot/src/presentation/commands/
├── information/
│   ├── StatsCommandHandler.ts
│   ├── PingCommandHandler.ts
│   ├── AboutCommandHandler.ts
│   └── HelpCommandHandler.ts       ← Automatically loaded!
├── moderation/
│   ├── BanCommandHandler.ts        ← Automatically loaded!
│   └── KickCommandHandler.ts       ← Automatically loaded!
└── utility/
    └── SetupCommandHandler.ts       ← Automatically loaded!
```

## Migration from Legacy

The system is backward compatible through the `HybridCommandHandler`:

1. **New commands** are handled by the `CommandRegistry`
2. **Legacy commands** fall back to the old system
3. **Gradual migration** is supported

## Testing

Run the dynamic loading test:

```bash
cd packages/bot
npm run test:dynamic
```

This will show all discovered commands and their metadata.

## Debugging

### Available Commands
```typescript
import { DynamicCommandLoader } from '../infrastructure/loaders/DynamicCommandLoader.js';

const available = await DynamicCommandLoader.getAvailableCommands();
console.log('Available command files:', available);
```

### Container Diagnostics
```typescript
import { getContainer } from '../infrastructure/di/Container.js';
import { TYPES } from '../shared/types/TYPES.js';

const container = getContainer();
const commandRegistry = container.get(TYPES.CommandRegistry);
const commands = commandRegistry.getAllCommands();

console.log('Loaded commands:', commands.map(c => c.metadata.name));
```

## Performance

- **Startup Time**: Minimal impact, commands are loaded once during initialization
- **Memory Usage**: Only active command instances are kept in memory
- **Runtime**: No performance impact, commands are resolved from DI container

## Future Enhancements

- **Hot Reloading**: Reload commands without restarting
- **Plugin System**: Load commands from external packages
- **Command Validation**: Automatic validation of command metadata
- **Dependency Analysis**: Show command dependencies and relationships
