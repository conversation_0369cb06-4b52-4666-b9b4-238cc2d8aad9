/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

# Modern Command System Architecture

This document outlines the architecture of the new modern command system in `packages/bot/`, which is completely independent from the legacy `src/` system.

## Architecture Overview

```
🏗️ Modern Architecture (packages/bot/)
├── 📋 Domain Layer
│   ├── Events (ClientReadyEvent, GuildJoinedEvent, etc.)
│   ├── Services (DonationService, PremiumService, etc.)
│   └── Value Objects (Money, UserId, etc.)
├── 🎯 Application Layer
│   ├── Use Cases (CreateDonation, GetStatistics, etc.)
│   └── Interfaces
├── 🏛️ Infrastructure Layer
│   ├── DI Container (Automatic service registration)
│   ├── Event Bus (Domain events with Redis support)
│   ├── Repositories (Database access)
│   ├── Command Bridge (Modern command execution)
│   └── Modern Client (Discord.js with DI integration)
└── 🎨 Presentation Layer
    ├── Command Handlers (Stats, Ping, About, etc.)
    ├── Command Registry (Dynamic discovery)
    └── Event Handlers (Client events)
```

## Key Components

### 1. **ModernInterChatClient**
- Replaces legacy BaseClient
- Integrates DI container
- Publishes domain events
- No dependencies on `src/`

### 2. **Dynamic Command Loading**
- Automatically discovers command handlers
- No manual registration needed
- Type-safe command execution

### 3. **Event Bus with Domain Events**
- Cluster-aware event distribution
- Redis support for cross-cluster events
- Automatic event handler subscription

### 4. **Unified Context System**
- Single interface for slash commands and prefix commands
- Consistent API across all command types
- Better error handling and logging

### 5. **Clean Dependency Injection**
- Inversify container
- Automatic service resolution
- Proper scope management

## Migration Strategy

### ✅ **What's Complete**
- Modern Discord.js client (`ModernClient.ts`)
- Event bus with domain events and handlers
- Command system with dynamic loading
- Three migrated commands: `/stats`, `/ping`, `/about`
- DI container with service registration
- Modern entry points (`index.ts`, `client.ts`)

### 🎯 **What's Next**
- Migrate utility commands (`/help`, `/invite`)
- Migrate configuration commands
- Migrate hub management commands
- Add more event handlers for business logic

### 🚫 **What We Don't Use**
- **No imports from `src/`** - Complete separation
- **No legacy BaseCommand** - New BaseCommandHandler
- **No legacy Context classes** - New unified Context
- **No legacy command loading** - Dynamic discovery instead

## Benefits of This Approach

1. **🎯 Clean Architecture**: Clear separation of concerns
2. **🧪 Testable**: Dependency injection makes testing easy
3. **📈 Scalable**: Event-driven architecture supports growth
4. **🔧 Maintainable**: Modern patterns and TypeScript
5. **🚀 Fast**: No legacy debt slowing us down
6. **🔒 Type Safe**: Full TypeScript coverage
7. **📊 Observable**: Built-in logging and monitoring

## Running the Modern System

```bash
# Start the modern client
cd packages/bot
bun run src/index.ts

# Test the event bus
bun run src/test/test-event-bus.ts

# Test dynamic command loading
bun run src/test/test-dynamic-loading.ts
```

The modern system is completely self-contained and ready for production!
