# API Integration Plan - Flexible Command Architecture

## Overview

This document outlines the comprehensive API integration plan for the completed flexible command architecture in InterChat. The new architecture supports multiple response patterns, Discord components integration, and organized subcommand file structure while maintaining full backward compatibility.

## Architecture Summary

### ✅ Completed Components

#### 1. **Flexible Response System**
- **DIRECT Pattern**: Commands handle responses directly via `interaction.reply()` - returns `void`
- **STRUCTURED Pattern**: Commands return `CommandResult` objects for registry processing
- **ENHANCED Pattern**: Commands return `EnhancedCommandResult` with Discord components support

#### 2. **Subcommand File Organization**
- Separate files for subcommands (e.g., `AddBadgeSubcommand.ts`, `RemoveBadgeSubcommand.ts`)
- Type-safe registration and execution through `BaseSubcommandHandler`
- Maintains DI container integration

#### 3. **Discord Components Integration**
- Native support for buttons, select menus, modals
- Components v2 system compatibility
- Interaction handlers for component follow-ups

#### 4. **Unified Command Registry**
- Supports both slash commands and prefix commands
- Flexible response handling for both command types
- Cooldown and permission management

#### 5. **Prefix Command Support**
- `PrefixContext` class for message-based commands
- Same flexible response patterns as slash commands
- Argument parsing and validation

## API Reference

### Core Interfaces

```typescript
// Flexible response types
type FlexibleCommandResponse = void | CommandResult | EnhancedCommandResult;

interface CommandResult {
  readonly success: boolean;
  readonly message?: string;
  readonly embed?: EmbedBuilder;
  readonly ephemeral?: boolean;
  readonly followUp?: string;
}

interface EnhancedCommandResult extends CommandResult {
  readonly components?: any[];
  readonly files?: any[];
  readonly allowedMentions?: any;
  readonly flags?: MessageFlags[];
}

// Subcommand interface
interface ISubcommandHandler {
  readonly name: string;
  readonly description: string;
  execute(ctx: Context): Promise<FlexibleCommandResponse>;
}
```

### Command Handler Patterns

#### Pattern 1: Direct Response
```typescript
async execute(ctx: Context): Promise<void> {
  await ctx.interaction.reply({
    content: 'Response handled directly!',
    ephemeral: true
  });
  return; // Registry does nothing
}
```

#### Pattern 2: Structured Response
```typescript
async execute(ctx: Context): Promise<CommandResult> {
  return {
    success: true,
    embed: this.createSuccessEmbed('Title', 'Description'),
    ephemeral: true,
    followUp: 'Optional follow-up message'
  };
}
```

#### Pattern 3: Enhanced Response with Components
```typescript
async execute(ctx: Context): Promise<EnhancedCommandResult> {
  const button = new ButtonBuilder()
    .setCustomId('my_button')
    .setLabel('Click Me!')
    .setStyle(ButtonStyle.Primary);

  return {
    success: true,
    embed: this.createInfoEmbed('Title', 'Description'),
    components: [new ActionRowBuilder<ButtonBuilder>().addComponents(button)],
    ephemeral: true
  };
}
```

### Subcommand Organization

#### Main Command Handler
```typescript
@injectable()
export class BadgeCommandHandler extends BaseCommandHandler {
  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository
  ) {
    super();
    
    // Register subcommands
    this.registerSubcommand(new AddBadgeSubcommand(this.userRepository));
    this.registerSubcommand(new RemoveBadgeSubcommand(this.userRepository));
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const subcommand = ctx.options.getSubcommand();
    return await this.executeSubcommand(subcommand, ctx);
  }
}
```

#### Subcommand Handler
```typescript
@injectable()
export class AddBadgeSubcommand extends BaseSubcommandHandler {
  readonly name = 'add';
  readonly description = 'Add a badge to a user';

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    // Implementation using any of the three response patterns
  }
}
```

## Integration Points

### 1. **Command Registry Integration**
- **Slash Commands**: `CommandRegistry.executeCommand(interaction)`
- **Prefix Commands**: `CommandRegistry.executePrefixCommand(message, commandName, args)`
- **Unified Response Handling**: Both command types use the same flexible response system

### 2. **Context System Integration**
- **InteractionContext**: For slash commands and context menus
- **PrefixContext**: For message-based prefix commands
- **Unified Interface**: Both contexts implement the same `Context` interface

### 3. **Component Interaction Integration**
- **InteractionRegistry**: Routes component interactions to handlers
- **BaseInteractionHandler**: Foundation for button/modal/select handlers
- **Custom ID Parsing**: Structured custom IDs for stateless handling

### 4. **DI Container Integration**
- **Dynamic Loading**: Commands automatically discovered and registered
- **Service Injection**: Full dependency injection support
- **Type Safety**: Complete TypeScript coverage

## Migration Guidelines

### From Legacy Commands

1. **Update Imports**:
```typescript
// Old
import { BaseCommandHandler, CommandResult } from '../BaseCommandHandler.js';

// New
import { BaseCommandHandler, FlexibleCommandResponse } from '../BaseCommandHandler.js';
```

2. **Update Execute Method**:
```typescript
// Old
async execute(ctx: Context): Promise<CommandResult> {
  return { success: true, embed: ... };
}

// New - Choose your pattern
async execute(ctx: Context): Promise<FlexibleCommandResponse> {
  // Option 1: Direct
  await ctx.interaction.reply({ content: '...' });
  return;
  
  // Option 2: Structured (same as before)
  return { success: true, embed: ... };
  
  // Option 3: Enhanced
  return { success: true, embed: ..., components: [...] };
}
```

3. **Extract Subcommands** (Optional):
```typescript
// Create separate files for each subcommand
// Register them in the main command constructor
```

### Backward Compatibility

- ✅ Existing commands returning `CommandResult` continue to work
- ✅ No breaking changes to the public API
- ✅ Gradual migration is supported
- ✅ Legacy and new patterns can coexist

## Testing Strategy

### Type Safety Validation
```bash
cd packages/bot
bun type-check
```

### Response Pattern Testing
```bash
# Test all three response patterns
/response-example direct
/response-example structured
/response-example enhanced
/response-example deferred
```

### Component Integration Testing
```bash
# Test Discord components
/badge remove @user CHAMPION  # Shows confirmation buttons
# Click buttons to test component interactions
```

## Future Enhancements

### Planned Features
- [ ] **Modal Support**: Enhanced modal handling for complex forms
- [ ] **Select Menu Integration**: Advanced select menu patterns
- [ ] **Autocomplete Support**: Dynamic autocomplete for command options
- [ ] **Command Permissions**: Enhanced permission system integration
- [ ] **Rate Limiting**: Advanced rate limiting and cooldown management

### Dashboard Integration (Deferred)
- Dashboard integration has been deferred from this phase
- The flexible command architecture provides a solid foundation for future dashboard integration
- API endpoints can be added later to expose command functionality to web interfaces

## Conclusion

The flexible command architecture provides a robust, scalable foundation for InterChat's command system. It successfully addresses all the original requirements:

✅ **Flexible Response Patterns**: Three distinct patterns for different use cases  
✅ **Discord Components Support**: Full integration with Discord's component system  
✅ **Subcommand Organization**: Clean file structure like the legacy system  
✅ **Type Safety**: Complete TypeScript coverage  
✅ **Backward Compatibility**: Existing commands continue to work  
✅ **Prefix Command Support**: Unified system for both slash and prefix commands  

The architecture is production-ready and provides a solid foundation for future enhancements and integrations.
