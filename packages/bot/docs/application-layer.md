# Application Layer Architecture

The Application Layer in our Clean Architecture implementation contains the **business workflows** and **coordination logic**. It sits between the Domain (business rules) and Infrastructure/Presentation (technical concerns).

## 📁 Directory Structure

```
application/
├── commands/           # CQRS Commands (write operations)
├── events/            # Application-level events (coordination)
├── use-cases/         # Business workflows (main application logic)
├── dto/               # Data Transfer Objects (contracts)
├── services/          # Application services (coordination)
├── middleware/        # Cross-cutting concerns
├── command-handlers/  # Command handlers (if using CQRS)
└── event-handlers/    # Application event handlers
```

## 🎯 **Use Cases** (Primary Pattern We Use)

**Purpose**: Implement business workflows by orchestrating domain services and repositories.

**Example**: `CreateDonationUseCase`
```typescript
@injectable()
export class CreateDonationUseCase {
  async execute(command: CreateDonationCommand): Promise<CreateDonationResult> {
    // 1. Validate input
    // 2. Call domain services
    // 3. Persist changes via repositories
    // 4. Publish domain events
    // 5. Return result
  }
}
```

**Benefits**:
- ✅ Single responsibility per workflow
- ✅ Easy to test and maintain
- ✅ Clear business intent
- ✅ Transactional boundaries

## 📋 **Commands** (Optional CQRS Pattern)

**Purpose**: Represent write operations with all required data.

**Example**: `CreateHubCommand`
```typescript
export interface CreateHubCommand {
  readonly guildId: string;
  readonly channelId: string;
  readonly name: string;
  readonly description?: string;
  readonly isPrivate: boolean;
  readonly createdBy: string;
}
```

**When to Use**:
- Complex applications with many write operations
- Need for command validation/authorization
- Audit trails and command sourcing
- Separation of read/write models

## 🎭 **Application Events** vs **Domain Events**

| **Domain Events** | **Application Events** |
|------------------|------------------------|
| Business state changes | Workflow coordination |
| `DonationCreated` | `PaymentProcessed` |
| `UserPremiumGranted` | `UserRegistered` |
| Pure business logic | Cross-cutting concerns |

**Application Events Example**:
```typescript
export interface PaymentProcessedEvent {
  readonly paymentId: string;
  readonly userId: string;
  readonly amount: number;
  // ... coordinates multiple actions
}
```

## 📦 **DTOs** (Data Transfer Objects)

**Purpose**: Define contracts between layers and external systems.

**Types**:
- **Commands**: Input for write operations
- **Queries**: Input for read operations
- **Results**: Output from use cases
- **View Models**: Data for presentation

**Example**:
```typescript
export interface CreateDonationCommand {
  userId: string;
  amount: number;
  currency: string;
  source: DonationSource;
}

export interface CreateDonationResult {
  donationId: string;
  premiumTier?: string;
  expiresAt?: Date;
}
```

## 🔄 **Current vs Future State**

### **Current (Simplified)**
- ✅ **Use Cases**: Main business workflows
- ✅ **DTOs**: Input/output contracts
- ✅ **Domain Events**: Business state changes

### **Future (Advanced CQRS)**
- 🔮 **Commands**: Explicit write operations
- 🔮 **Application Events**: Workflow coordination
- 🔮 **Command Handlers**: Separate command processing
- 🔮 **Event Handlers**: Cross-cutting concerns

## 🎯 **Why This Structure?**

1. **🏗️ Separation of Concerns**: Business logic separate from technical details
2. **🧪 Testability**: Use cases can be tested in isolation
3. **📈 Scalability**: Easy to add new workflows
4. **🔄 Flexibility**: Can evolve to CQRS when needed
5. **🎨 Clean**: Clear dependencies and responsibilities

## 🚀 **Best Practices**

1. **Use Cases should be thin** - delegate to domain services
2. **One Use Case per business operation** - single responsibility
3. **DTOs should be immutable** - readonly properties
4. **Events should be past tense** - represent what happened
5. **Commands should be verbs** - represent what to do

This structure gives us a clean, maintainable, and scalable application architecture! 🎉
