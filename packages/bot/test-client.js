#!/usr/bin/env -S bun run
/**
 * Simple Bot Start Test - using the actual client.ts but with logging only
 */

console.log('🚀 Starting InterChat Bot through client.ts...');

// Mock cluster environment to bypass the clustering system
process.env.CLUSTER_MANAGER_MODE = 'worker';

// Mock worker data to satisfy the cluster client
const mockWorkerData = {
  SHARD_LIST: [0],
  TOTAL_SHARDS: 1,
  CLUSTER_ID: 0,
  CLUSTER_COUNT: 1
};

// Patch worker_threads to provide our mock data
const Module = require('module');
const originalRequire = Module.prototype.require;

Module.prototype.require = function(id) {
  if (id === 'worker_threads') {
    return {
      workerData: mockWorkerData,
      parentPort: {
        on: () => {},
        postMessage: () => {}
      }
    };
  }
  return originalRequire.apply(this, arguments);
};

async function testClientStart() {
  try {
    console.log('🔧 Loading client.ts...');

    // Import the client file
    await import('./src/client.js');

    console.log('✅ Client loaded successfully!');
    console.log('🎯 Modern system initialized - bot is ready for commands!');

    // Give it a moment to fully initialize
    setTimeout(() => {
      console.log('🏁 Test completed - modern bot system is functional');
      process.exit(0);
    }, 2000);

  } catch (error) {
    console.error('❌ Error during client test:', error);
    if (error instanceof Error) {
      console.error('Details:', error.message);
      console.error('Stack:', error.stack);
    }
    process.exit(1);
  }
}

testClientStart();
