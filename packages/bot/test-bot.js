#!/usr/bin/env -S bun run
/**
 * Quick Bot Test - bypassing TypeScript compilation
 * This tests if the core modern bot system can initialize and run
 */

import { ModernInterChatClient } from './src/core/ModernClient.js';

console.log('🚀 Starting InterChat Bot Test...');

async function testBot() {
  try {
    // Set cluster mode for testing
    process.env.CLUSTER_MANAGER_MODE = 'worker';

    // Check if essential environment variables exist
    const requiredEnvVars = ['DISCORD_TOKEN', 'DATABASE_URL'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      console.log('❌ Missing environment variables:', missingVars);
      console.log('💡 Please ensure .env file contains:', requiredEnvVars.join(', '));
      return;
    }

    console.log('✅ Environment variables found');

    // Try to create the client
    console.log('🔧 Initializing ModernInterChatClient...');
    const client = new ModernInterChatClient({
      intents: ['Guilds', 'GuildMessages', 'MessageContent'],
    });

    console.log('✅ Client created successfully');
    console.log('🎯 Modern system is ready to run!');

    // Don't actually connect to Discord in this test
    console.log('🔍 Test completed - bot is ready for deployment');

  } catch (error) {
    console.error('❌ Error during bot test:', error);
    if (error instanceof Error) {
      console.error('Details:', error.message);
      console.error('Stack:', error.stack);
    }
  }
}

testBot();
