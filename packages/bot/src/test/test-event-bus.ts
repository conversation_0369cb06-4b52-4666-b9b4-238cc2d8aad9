/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { initializeContainer } from '../infrastructure/di/Container.js';
import { TYPES } from '../shared/types/TYPES.js';
import type { IEventBus } from '../infrastructure/events/TestInterfaces.js';
import { ClientReadyEvent } from '../domain/events/ClientEvents.js';

/**
 * Test Event Bus Integration
 *
 * This test verifies that:
 * 1. The event bus is properly configured in the DI container
 * 2. Event handlers are automatically subscribed
 * 3. Domain events are properly published and handled
 */
async function testEventBus() {
  console.log('🧪 Testing Event Bus Integration...\n');

  try {
    // Initialize container
    console.log('🔧 Initializing DI container...');
    const container = await initializeContainer('test-cluster');

    // Get event bus
    console.log('📡 Getting event bus from container...');
    const eventBus = container.get<IEventBus>(TYPES.EventBus);

    // Create a test event
    console.log('🎭 Creating test event...');
    const testEvent = new ClientReadyEvent(
      'test-cluster',
      42,
      1337,
      [0, 1, 2],
    );

    // Publish the event
    console.log('📤 Publishing test event...');
    await eventBus.publish(testEvent);

    // Wait a moment for the event to be processed
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Check event history
    console.log('📜 Checking event history...');
    const history = eventBus.getLocalEventHistory();
    console.log(`✅ Event history contains ${history.length} events`);

    if (history.length > 0) {
      const lastEvent = history[history.length - 1];
      console.log(`📋 Last event: ${lastEvent.type} from cluster ${lastEvent.clusterId}`);
    }

    console.log('\n✅ Event Bus test completed successfully!');
    console.log('🎉 The event bus is working correctly with:');
    console.log('   • Automatic event handler registration');
    console.log('   • Domain event publishing');
    console.log('   • Event handler execution');
    console.log('   • Event history tracking');

  }
  catch (error) {
    console.error('\n❌ Event Bus test failed:', error);
    throw error;
  }
}

// Run the test
if (import.meta.url === new URL(process.argv[1], 'file://').href) {
  testEventBus()
    .then(() => {
      console.log('\n🎊 All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

export { testEventBus };
