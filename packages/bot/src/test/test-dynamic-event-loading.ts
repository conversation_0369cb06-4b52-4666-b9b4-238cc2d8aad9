/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { initializeContainer } from '../infrastructure/di/Container.js';
import { DynamicEventLoader } from '../infrastructure/loaders/DynamicEventLoader.js';
import { Logger } from '../shared/utils/Logger.js';

/**
 * Test Dynamic Event Loading System
 *
 * This test verifies that the dynamic event loading system works correctly
 * and automatically discovers event handlers.
 */
async function testDynamicEventLoading() {
  console.log('🧪 Testing Dynamic Event Loading System...\n');

  try {
    // Initialize container
    console.log('🔧 Initializing DI container...');
    const container = await initializeContainer('test-cluster');

    // Test dynamic event loading
    console.log('📡 Loading event handlers dynamically...');
    const eventHandlers = await DynamicEventLoader.loadEventHandlers(container);

    console.log(`✅ Loaded ${eventHandlers.length} event handlers:`);

    // Display loaded event handlers
    for (const { eventName, handler, once } of eventHandlers) {
      const handlerName = handler.constructor.name;
      const onceText = once ? ' (once)' : '';
      console.log(`   • ${eventName}: ${handlerName}${onceText}`);
    }

    // Verify expected event handlers are loaded
    const expectedEvents = ['messageCreate', 'interactionCreate'];
    const loadedEvents = eventHandlers.map((h) => h.eventName);

    console.log('\n🔍 Verifying expected event handlers...');
    for (const expectedEvent of expectedEvents) {
      if (loadedEvents.includes(expectedEvent)) {
        console.log(`   ✅ ${expectedEvent} handler loaded`);
      }
      else {
        console.log(`   ❌ ${expectedEvent} handler missing`);
      }
    }

    // Test event handler interface compliance
    console.log('\n🧪 Testing event handler interface compliance...');
    for (const { eventName, handler } of eventHandlers) {
      const hasEventName = typeof handler.eventName === 'string';
      const hasExecute = typeof handler.execute === 'function';
      const hasOnce = handler.once !== undefined;

      if (hasEventName && hasExecute && hasOnce) {
        console.log(`   ✅ ${eventName}: Interface compliant`);
      }
      else {
        console.log(`   ❌ ${eventName}: Interface non-compliant`);
        console.log(`      - eventName: ${hasEventName}`);
        console.log(`      - execute: ${hasExecute}`);
        console.log(`      - once: ${hasOnce}`);
      }
    }

    console.log('\n✅ Dynamic Event Loading test completed successfully!');
    console.log('🎉 The event loading system is working correctly with:');
    console.log('   • Automatic event handler discovery');
    console.log('   • Interface validation');
    console.log('   • Dependency injection integration');
    console.log('   • Proper event registration');
    console.log('\n🎊 All tests passed!');

  }
  catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testDynamicEventLoading();
