/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { HybridCommandHandler } from '../infrastructure/handlers/HybridCommandHandler.js';

/**
 * Simple test to verify the modern command system is working
 */
async function testModernCommandSystem() {
  console.log('🧪 Testing Modern Command System...\n');

  try {
    // Create command handler
    console.log('🔧 Creating modern command handler...');
    const commandHandler = new HybridCommandHandler();

    // Get diagnostics
    console.log('📊 Getting system diagnostics...');
    const diagnostics = commandHandler.getDiagnostics();

    console.log('📋 System Status:');
    console.log(`   • System Available: ${diagnostics.systemAvailable}`);
    console.log(`   • Bridge Status: ${diagnostics.bridgeStatus}`);
    console.log(`   • Command Count: ${diagnostics.commandCount}`);
    console.log(`   • Available Commands: [${diagnostics.availableCommands.join(', ')}]`);

    // Test command checking
    console.log('\n🔍 Testing command availability...');
    const testCommands = ['stats', 'ping', 'about', 'nonexistent'];

    for (const cmd of testCommands) {
      const hasCommand = commandHandler.hasCommand(cmd);
      const status = hasCommand ? '✅' : '❌';
      console.log(`   ${status} Command '${cmd}': ${hasCommand ? 'Available' : 'Not found'}`);
    }

    console.log('\n✅ Modern Command System test completed successfully!');
    console.log('🎉 The system is working with:');
    console.log('   • Modern command handler');
    console.log('   • Command bridge integration');
    console.log('   • Dynamic command discovery');
    console.log('   • Clean architecture separation');

  }
  catch (error) {
    console.error('\n❌ Modern Command System test failed:', error);
    throw error;
  }
}

// Run the test
if (import.meta.url === new URL(process.argv[1], 'file://').href) {
  testModernCommandSystem()
    .then(() => {
      console.log('\n🎊 Test passed! Modern system is ready!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

export { testModernCommandSystem };
