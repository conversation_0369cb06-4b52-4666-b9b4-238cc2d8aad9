/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  APIModalInteractionResponseCallbackData,
  ButtonInteraction,
  ChannelSelectMenuInteraction,
  InteractionEditReplyOptions,
  InteractionReplyOptions,
  InteractionResponse,
  JSONEncodable,
  MentionableSelectMenuInteraction,
  Message,
  MessageComponentInteraction,
  MessageEditOptions,
  ModalComponentData,
  ModalSubmitInteraction,
  RoleSelectMenuInteraction,
  StringSelectMenuInteraction,
  UserSelectMenuInteraction,
} from 'discord.js';
import { Logger } from '../utils/Logger.js';
import { Context } from './Context.js';

export type ComponentInteraction = MessageComponentInteraction | ModalSubmitInteraction;

/**
 * Context class for component interactions (buttons, select menus, modals)
 */
export class ComponentContext extends Context<{
  interaction: ComponentInteraction;
  responseType: Message | InteractionResponse;
}> {
  private readonly _customId: { action: string; id: string; params: Record<string, string> };

  /**
   * Create a new ComponentContext
   * @param interaction The component interaction
   */
  constructor(interaction: ComponentInteraction) {
    super(interaction);
    this._customId = this.parseCustomId(interaction.customId);
  }

  /**
   * Parse the custom ID into its component parts
   * @param customId The custom ID to parse
   * @returns An object with action, id, and params properties
   */
  private parseCustomId(customId: string) {
    try {
      // Format should be action:id:param1=value1:param2=value2
      const parts = customId.split(':');
      const action = parts[0] || '';
      const id = parts[1] || '';

      const params: Record<string, string> = {};
      for (let i = 2; i < parts.length; i++) {
        const paramParts = parts[i].split('=');
        if (paramParts.length === 2) {
          params[paramParts[0]] = paramParts[1];
        }
      }

      return { action, id, params };
    }
    catch (error) {
      Logger.error('Error parsing custom ID:', error);
      return { action: '', id: '', params: {} };
    }
  }

  /**
   * Get the parsed custom ID for this interaction
   */
  public get customId() {
    return this._customId;
  }

  /**
   * Whether the interaction has been deferred
   */
  public get deferred() {
    return this.interaction.deferred;
  }

  /**
   * Whether the interaction has been replied to
   */
  public get replied() {
    return this.interaction.replied;
  }

  /**
   * Defer the reply to this interaction
   * @param opts Options for deferring
   */
  public async deferReply(opts?: { flags?: ['Ephemeral'] }): Promise<InteractionResponse> {
    return await this.interaction.deferReply({ flags: opts?.flags });
  }

  /**
   * Defer the update to this interaction
   */
  public async deferUpdate(): Promise<InteractionResponse> {
    try {
      return await this.interaction.deferUpdate();
    }
    catch (error) {
      Logger.error('Error deferring update:', error);
      throw new Error('Failed to defer update');
    }
  }

  /**
   * Reply to this interaction
   * @param data The data for the reply
   */
  public async reply(
    data: string | InteractionReplyOptions,
  ): Promise<Message | InteractionResponse> {
    try {
      if (this.interaction.replied || this.interaction.deferred) {
        return await this.interaction.followUp(data);
      }
      return await this.interaction.reply(data);
    }
    catch (error) {
      Logger.error('Error replying to interaction:', error);
      throw new Error('Failed to reply to interaction');
    }
  }

  /**
   * Delete the reply to this interaction
   */
  public async deleteReply(): Promise<void> {
    try {
      await this.interaction.deleteReply();
    }
    catch (error) {
      Logger.error('Error deleting reply:', error);
    }
  }

  /**
   * Edit the reply to this interaction
   * @param data The new data for the reply
   */
  public async editReply(data: string | MessageEditOptions | InteractionEditReplyOptions) {
    try {
      if (
        this.interaction.isMessageComponent() &&
        !this.interaction.deferred &&
        !this.interaction.replied
      ) {
        return await this.interaction.update(data);
      }

      return await this.interaction.editReply(data);
    }
    catch (error) {
      Logger.error('Error editing reply:', error);
      return null;
    }
  }

  /**
   * Show a modal for this interaction
   * @param data The modal data
   */
  public async showModal(
    data:
      | JSONEncodable<APIModalInteractionResponseCallbackData>
      | ModalComponentData
      | APIModalInteractionResponseCallbackData,
  ): Promise<void> {
    try {
      if (this.isModalSubmit()) {
        throw new Error('Cannot show modal on a modal submit interaction');
      }
      else if (this.interaction.isMessageComponent()) {
        await this.interaction.showModal(data);
      }
    }
    catch (error) {
      Logger.error('Error showing modal:', error);
      throw error;
    }
  }

  /**
   * Get a value from a modal field
   * @param fieldId The ID of the field to get the value from
   * @returns The value of the field, or null if this isn't a modal interaction
   */
  public getModalFieldValue(fieldId: string): string | null {
    return this.isModalSubmit() ? this.interaction.fields.getTextInputValue(fieldId) : null;
  }

  /**
   * Get all values from modal fields
   * @returns An object with field IDs as keys and field values as values, or null if this isn't a modal interaction
   */
  public getModalFieldValues(): Record<string, string> | null {
    try {
      if (this.isModalSubmit()) {
        const result: Record<string, string> = {};
        for (const [id, value] of this.interaction.fields.fields) {
          result[id] = value.value;
        }
        return result;
      }
      return null;
    }
    catch (error) {
      Logger.error('Error getting modal field values:', error);
      return null;
    }
  }

  /**
   * Check if this interaction is a modal submit interaction
   */
  public isModalSubmit(): this is this & {
    interaction: ModalSubmitInteraction;
  } {
    return this.interaction.isModalSubmit();
  }

  /**
   * Check if this interaction is a button interaction
   */
  public isButton(): this is this & {
    interaction: ButtonInteraction;
  } {
    return this.interaction.isButton();
  }

  /**
   * Check if this interaction is a string select menu interaction
   */
  public isStringSelectMenu(): this is this & {
    interaction: StringSelectMenuInteraction;
  } {
    return this.interaction.isStringSelectMenu();
  }

  /**
   * Check if this interaction is a user select menu interaction
   */
  public isUserSelectMenu(): this is this & {
    interaction: UserSelectMenuInteraction;
  } {
    return this.interaction.isUserSelectMenu();
  }

  /**
   * Check if this interaction is a role select menu interaction
   */
  public isRoleSelectMenu(): this is this & {
    interaction: RoleSelectMenuInteraction;
  } {
    return this.interaction.isRoleSelectMenu();
  }

  /**
   * Check if this interaction is a channel select menu interaction
   */
  public isChannelSelectMenu(): this is this & {
    interaction: ChannelSelectMenuInteraction;
  } {
    return this.interaction.isChannelSelectMenu();
  }

  /**
   * Check if this interaction is a mentionable select menu interaction
   */
  public isMentionableSelectMenu(): this is this & {
    interaction: MentionableSelectMenuInteraction;
  } {
    return this.interaction.isMentionableSelectMenu();
  }

  /**
   * Get the selected values from a string select menu
   */
  public get values() {
    return this.isStringSelectMenu() ? this.interaction.values : null;
  }

  /**
   * Get the selected users from a user select menu
   */
  public get users() {
    return this.isUserSelectMenu() ? this.interaction.users : null;
  }

  /**
   * Get the selected roles from a role select menu
   */
  public get roles() {
    return this.isRoleSelectMenu() ? this.interaction.roles : null;
  }

  /**
   * Get the selected channels from a channel select menu
   */
  public get channels() {
    return this.isChannelSelectMenu() ? this.interaction.channels : null;
  }

  /**
   * Get the selected members from a mentionable select menu
   */
  public get members() {
    return this.isMentionableSelectMenu() ? this.interaction.members : null;
  }
}
