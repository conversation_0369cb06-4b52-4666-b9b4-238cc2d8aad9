/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Improved Command Context Architecture
 *
 * This file provides a cleaner, more maintainable approach to command contexts
 * that eliminates the need for UnifiedContext adapters while maintaining
 * type safety and supporting both slash commands and prefix commands.
 */

import type { InteractionContext } from './InteractionContext.js';
import type { PrefixContext } from './PrefixContext.js';

/**
 * Union type for all supported context types
 * This replaces the UnifiedContext interface with a cleaner union approach
 */
export type CommandContext = InteractionContext | PrefixContext;

/**
 * Type guard to check if context is an InteractionContext
 */
export function isInteractionContext(ctx: CommandContext): ctx is InteractionContext {
  return 'interaction' in ctx && !('args' in ctx);
}

/**
 * Type guard to check if context is a PrefixContext
 */
export function isPrefixContext(ctx: CommandContext): ctx is PrefixContext {
  return 'args' in ctx;
}
