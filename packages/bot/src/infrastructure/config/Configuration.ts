/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { z } from 'zod';
import { injectable } from 'inversify';

// Helper function to parse environment variables
const env = {
  string: (key: string, defaultValue?: string) => process.env[key] || defaultValue,
  number: (key: string, defaultValue?: number) => {
    const value = process.env[key];
    return value ? parseInt(value) : defaultValue;
  },
  boolean: (key: string, defaultValue?: boolean) => {
    const value = process.env[key];
    return value ? value === 'true' : defaultValue;
  },
  url: (key: string, defaultValue?: string) => process.env[key] || defaultValue,
};

// Simplified schema with fewer nested objects
const ConfigSchema = z.object({
  // Bot configuration
  botToken: z.string().min(1, 'Bot token is required'),
  clientId: z.string().min(1, 'Client ID is required'),
  devGuildId: z.string().optional(),
  botStatus: z.enum(['online', 'idle', 'dnd', 'invisible']).default('online'),
  botActivity: z.string().default('InterChat'),

  // Database
  databaseUrl: z.string().url('Invalid database URL'),
  dbPoolMin: z.number().min(1).default(2),
  dbPoolMax: z.number().min(1).default(10),

  // Redis
  redisUrl: z.string().url('Invalid Redis URL'),
  redisKeyPrefix: z.string().default('interchat:'),
  redisTTL: z.number().default(300),

  // API
  apiPort: z.number().min(1).max(65535).default(3000),
  corsOrigin: z.string().default('*'),
  rateLimitWindow: z.number().default(15 * 60 * 1000),
  rateLimitMax: z.number().default(100),

  // Logging
  logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  logFormat: z.enum(['json', 'simple']).default('json'),
  logFile: z.string().default('logs/app.log'),

  // Features
  premiumEnabled: z.boolean().default(true),
  analyticsEnabled: z.boolean().default(false),
  monitoringEnabled: z.boolean().default(false),

  // Optional configurations
  kofiWebhookSecret: z.string().optional(),
  sentryDsn: z.string().optional(),
  prometheusPort: z.number().default(9090),
});

export type Config = z.infer<typeof ConfigSchema>;

/**
 * Simplified Configuration Service
 *
 * Loads and validates configuration from environment variables
 * with a flatter structure and reduced complexity.
 */
@injectable()
export class Configuration {
  private readonly config: Config;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): Config {
    const rawConfig = {
      // Bot
      botToken: env.string('BOT_TOKEN'),
      clientId: env.string('CLIENT_ID'),
      devGuildId: env.string('DEV_GUILD_ID'),
      botStatus: env.string('BOT_STATUS', 'online'),
      botActivity: env.string('BOT_ACTIVITY_NAME', 'InterChat'),

      // Database
      databaseUrl: env.string('DATABASE_URL'),
      dbPoolMin: env.number('DB_POOL_MIN', 2),
      dbPoolMax: env.number('DB_POOL_MAX', 10),

      // Redis
      redisUrl: env.string('REDIS_URL'),
      redisKeyPrefix: env.string('REDIS_KEY_PREFIX', 'interchat:'),
      redisTTL: env.number('REDIS_DEFAULT_TTL', 300),

      // API
      apiPort: env.number('API_PORT', 3000),
      corsOrigin: env.string('CORS_ORIGIN', '*'),
      rateLimitWindow: env.number('RATE_LIMIT_WINDOW', 15 * 60 * 1000),
      rateLimitMax: env.number('RATE_LIMIT_MAX', 100),

      // Logging
      logLevel: env.string('LOG_LEVEL', 'info'),
      logFormat: env.string('LOG_FORMAT', 'json'),
      logFile: env.string('LOG_FILE_NAME', 'logs/app.log'),

      // Features
      premiumEnabled: env.boolean('PREMIUM_ENABLED', true),
      analyticsEnabled: env.boolean('ANALYTICS_ENABLED', false),
      monitoringEnabled: env.boolean('MONITORING_ENABLED', false),

      // Optional
      kofiWebhookSecret: env.string('KOFI_WEBHOOK_SECRET'),
      sentryDsn: env.string('SENTRY_DSN'),
      prometheusPort: env.number('PROMETHEUS_PORT', 9090),
    };

    try {
      return ConfigSchema.parse(rawConfig);
    }
    catch (error) {
      if (error instanceof z.ZodError) {
        const messages = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`);
        throw new Error(`Configuration validation failed:\n${messages.join('\n')}`);
      }
      throw error;
    }
  }

  // Simple getters instead of generic get method
  get bot() {
    return {
      token: this.config.botToken,
      clientId: this.config.clientId,
      devGuildId: this.config.devGuildId,
      status: this.config.botStatus,
      activity: this.config.botActivity,
    };
  }

  get database() {
    return {
      url: this.config.databaseUrl,
      poolMin: this.config.dbPoolMin,
      poolMax: this.config.dbPoolMax,
    };
  }

  get redis() {
    return {
      url: this.config.redisUrl,
      keyPrefix: this.config.redisKeyPrefix,
      ttl: this.config.redisTTL,
    };
  }

  get api() {
    return {
      port: this.config.apiPort,
      corsOrigin: this.config.corsOrigin,
      rateLimitWindow: this.config.rateLimitWindow,
      rateLimitMax: this.config.rateLimitMax,
    };
  }

  get logging() {
    return {
      level: this.config.logLevel,
      format: this.config.logFormat,
      file: this.config.logFile,
    };
  }

  // Feature flags
  get features() {
    return {
      premium: this.config.premiumEnabled,
      analytics: this.config.analyticsEnabled,
      monitoring: this.config.monitoringEnabled,
    };
  }

  // Utility methods
  isFeatureEnabled(feature: keyof typeof this.features): boolean {
    return this.features[feature];
  }

  get environment(): string {
    return process.env.NODE_ENV || 'development';
  }

  get isDevelopment(): boolean {
    return this.environment === 'development';
  }

  get isProduction(): boolean {
    return this.environment === 'production';
  }

  // Get raw config if needed
  getAll(): Config {
    return { ...this.config };
  }
}
