/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { Container } from 'inversify';
import { readdir, stat } from 'node:fs/promises';
import { join } from 'node:path';
import { BaseCommandHandler } from '../../presentation/commands/BaseCommandHandler.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { Logger } from '../../shared/utils/Logger.js';

const __dirname = new URL('.', import.meta.url).pathname;

/**
 * Dynamic Command Loader for New Architecture
 *
 * Automatically discovers and registers command handlers from the file system.
 * This eliminates the need to manually import and register each command.
 */
export class DynamicCommandLoader {
  /**
   * Load all command handlers from the presentation/commands directory
   */
  static async loadCommandHandlers(container: Container): Promise<BaseCommandHandler[]> {
    const commandHandlers: BaseCommandHandler[] = [];
    const commandsPath = join(__dirname, '../../presentation/commands');

    Logger.info('🔍 Loading command handlers dynamically...');

    await this.loadCommandsFromDirectory(commandsPath, commandHandlers, container);

    Logger.info(`✅ Loaded ${commandHandlers.length} command handlers`);
    return commandHandlers;
  }

  /**
   * Recursively load commands from a directory
   */
  private static async loadCommandsFromDirectory(
    dirPath: string,
    commandHandlers: BaseCommandHandler[],
    container: Container,
    depth = 0,
  ): Promise<void> {
    try {
      const files = await readdir(dirPath);

      for (const file of files) {
        const fullPath = join(dirPath, file);
        const fileStat = await stat(fullPath);

        if (fileStat.isDirectory()) {
          // Recursively load from subdirectories
          await this.loadCommandsFromDirectory(fullPath, commandHandlers, container, depth + 1);
        }
        else if (this.isValidCommandFile(file)) {
          // Try to load the command handler
          await this.loadCommandHandler(fullPath, commandHandlers, container);
        }
      }
    }
    catch (error) {
      Logger.warn(`Could not load commands from ${dirPath}:`, error);
    }
  }

  /**
   * Load a single command handler file
   */
  private static async loadCommandHandler(
    filePath: string,
    commandHandlers: BaseCommandHandler[],
    container: Container,
  ): Promise<void> {
    try {
      // Convert file path to import path
      const importPath = `file://${filePath}`;

      // For TypeScript files in development, we need to import them as-is
      // The runtime (tsx, ts-node, etc.) will handle the compilation
      if (!filePath.endsWith('.js') && !filePath.endsWith('.ts')) {
        Logger.warn(`Unsupported file type: ${filePath}`);
        return;
      }

      // Dynamic import
      const module = await import(importPath);
      const CommandHandlerClass = module.default || module[Object.keys(module)[0]];

      if (!CommandHandlerClass) {
        Logger.warn(`No export found in ${filePath}`);
        return;
      }

      // Check if it extends BaseCommandHandler
      if (!this.isCommandHandler(CommandHandlerClass)) {
        return; // Skip non-command files
      }

      // Bind the class to the container
      const tempSymbol = Symbol.for(`temp_${Date.now()}`);
      container.bind(tempSymbol).to(CommandHandlerClass);

      // Create instance through the container to ensure dependency injection
      const instance = container.get<BaseCommandHandler>(tempSymbol);
      container.unbind(tempSymbol);

      if (instance instanceof BaseCommandHandler) {
        commandHandlers.push(instance);

        // Dynamically bind to container
        const commandName = instance.metadata.name;
        const symbolName = this.createSymbolName(commandName);
        const symbol = Symbol.for(symbolName);

        // Bind both individual and multi-inject symbols
        container.bind(symbol).toConstantValue(instance);
        container.bind(TYPES.PresentationCommandHandler).toConstantValue(instance);

        Logger.debug(`  ✓ Loaded: ${commandName} (${symbolName})`);
      }
    }
    catch (error) {
      Logger.warn(`Failed to load command handler from ${filePath}:`, error);
    }
  }

  /**
   * Check if a file is a valid command file to load
   */
  private static isValidCommandFile(filename: string): boolean {
    // Must be a TypeScript or JavaScript file
    if (!filename.endsWith('.ts') && !filename.endsWith('.js')) {
      return false;
    }

    // Exclude declaration files
    if (filename.endsWith('.d.ts')) {
      return false;
    }

    // Exclude base command handler files
    if (filename.includes('BaseCommandHandler')) {
      return false;
    }

    // Exclude test files
    if (filename.includes('.test.') || filename.includes('.spec.')) {
      return false;
    }

    return true;
  }

  /**
   * Check if a class is a command handler
   */
  private static isCommandHandler(cls: any): boolean {
    try {
      // Check if the class has the expected structure
      if (typeof cls !== 'function') return false;

      const instance = new cls();
      return (
        instance instanceof BaseCommandHandler &&
        instance.metadata &&
        typeof instance.metadata.name === 'string'
      );
    }
    catch {
      return false;
    }
  }

  /**
   * Create a symbol name for a command
   */
  private static createSymbolName(commandName: string): string {
    // Convert command name to PascalCase + CommandHandler
    const pascalCase = commandName
      .split('-')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    return `${pascalCase}CommandHandler`;
  }

  /**
   * Get all available command files for debugging
   */
  static async getAvailableCommands(): Promise<string[]> {
    const commandsPath = join(__dirname, '../../presentation/commands');
    const commands: string[] = [];

    await this.findCommandFiles(commandsPath, commands);
    return commands;
  }

  /**
   * Recursively find all command files
   */
  private static async findCommandFiles(dirPath: string, commands: string[]): Promise<void> {
    try {
      const files = await readdir(dirPath);

      for (const file of files) {
        const fullPath = join(dirPath, file);
        const fileStat = await stat(fullPath);

        if (fileStat.isDirectory()) {
          await this.findCommandFiles(fullPath, commands);
        }
        else if (this.isValidCommandFile(file)) {
          commands.push(fullPath);
        }
      }
    }
    catch (error) {
      Logger.warn(`Could not scan ${dirPath}:`, error);
    }
  }
}
