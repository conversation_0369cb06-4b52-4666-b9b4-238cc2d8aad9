/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { DomainEvent } from '../events/EventBus.js';

/**
 * Event Handler Interface for tests
 */
export interface IEventHandler<T extends DomainEvent = DomainEvent> {
  handle(event: T): Promise<void>;
  readonly eventType: string;
  readonly handlerId: string;
}

/**
 * Event Bus Interface for tests
 */
export interface IEventBus {
  // Publishing events
  publish<T extends DomainEvent>(event: T): Promise<void>;
  publishMany<T extends DomainEvent>(events: T[]): Promise<void>;

  // Local cluster subscriptions
  subscribe<T extends DomainEvent>(eventType: string, handler: IEventHandler<T>): void;
  unsubscribe<T extends DomainEvent>(eventType: string, handler: IEventHandler<T>): void;

  // Cluster management
  getClusterId(): string;
  isHealthy(): Promise<boolean>;

  // Event history and debugging
  getLocalEventHistory(): DomainEvent[];
  clearLocalHistory(): void;
}
