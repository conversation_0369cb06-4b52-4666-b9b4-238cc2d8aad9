/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import {
  InteractionContextType,
  REST,
  Routes,
  SlashCommandBuilder,
  SlashCommandOptionsOnlyBuilder,
  SlashCommandSubcommandsOnlyBuilder,
} from 'discord.js';
import { createHash } from 'crypto';
import { Redis } from 'ioredis';
import { TYPES } from '../shared/types/TYPES.js';
import { CommandRegistry } from '../presentation/commands/CommandRegistry.js';
import { Configuration } from '../infrastructure/config/Configuration.js';
import { Logger } from '../shared/utils/Logger.js';

/**
 * Command Deployment Configuration
 */
export interface CommandDeploymentConfig {
  /** Whether to deploy commands globally or to specific guild */
  deployGlobally: boolean;
  /** Guild ID for guild-specific deployment (if not global) */
  guildId?: string;
  /** Redis TTL for command cache in seconds */
  cacheTTL: number;
  /** Whether to force deployment regardless of cache */
  forceDeployment: boolean;
  /** Whether to skip deployment entirely (for testing) */
  skipDeployment: boolean;
}

/**
 * Command Deployment Result
 */
export interface CommandDeploymentResult {
  /** Whether deployment was successful */
  success: boolean;
  /** Whether commands were actually deployed or skipped due to cache */
  deployed: boolean;
  /** Number of commands deployed */
  commandCount: number;
  /** Reason for deployment decision */
  reason: string;
  /** Error message if deployment failed */
  error?: string;
  /** Time taken for deployment in milliseconds */
  duration: number;
}

/**
 * Command Deployment Service
 *
 * Handles intelligent deployment of Discord slash commands with Redis-based caching
 * to prevent unnecessary re-deployments. Uses SHA-256 hashing to detect changes
 * in command definitions.
 */
@injectable()
export class CommandDeploymentService {
  private readonly REDIS_KEY = 'interchat:commands:hash';
  private readonly DEFAULT_CACHE_TTL = 300; // 5 minutes

  constructor(
    @inject(TYPES.CommandRegistry)
    private readonly commandRegistry: CommandRegistry,
    @inject(TYPES.Configuration)
    private readonly config: Configuration,
    @inject(TYPES.RedisClient)
    private readonly redis: Redis,
  ) {}

  /**
   * Deploy commands with intelligent caching
   */
  async deployCommands(
    options: Partial<CommandDeploymentConfig> = {},
  ): Promise<CommandDeploymentResult> {
    const startTime = Date.now();
    const config = this.buildDeploymentConfig(options);

    Logger.info('🚀 Starting command deployment...');
    Logger.debug(`Deployment config: ${JSON.stringify(config, null, 2)}`);

    try {
      // Skip deployment if configured
      if (config.skipDeployment) {
        return this.createResult(true, false, 0, 'Deployment skipped by configuration', startTime);
      }

      // Get command data from registry
      const commands = this.commandRegistry.getCommandData();
      const commandHash = this.calculateCommandHash(commands);

      Logger.debug(`📊 Found ${commands.length} commands, hash: ${commandHash.substring(0, 8)}...`);

      // Check if deployment is needed
      if (!config.forceDeployment) {
        const deploymentNeeded = await this.isDeploymentNeeded(commandHash);
        if (!deploymentNeeded) {
          return this.createResult(
            true,
            false,
            commands.length,
            'Commands unchanged, skipping deployment',
            startTime,
          );
        }
      }

      // Deploy to Discord API
      const deployed = await this.deployToDiscord(commands, config);
      if (!deployed) {
        return this.createResult(
          false,
          false,
          commands.length,
          'Discord API deployment failed',
          startTime,
        );
      }

      // Update cache
      await this.updateCommandCache(commandHash, config.cacheTTL);

      const reason = config.forceDeployment
        ? 'Forced deployment'
        : 'Commands changed, deployed successfully';
      return this.createResult(true, true, commands.length, reason, startTime);
    }
    catch (error) {
      Logger.error('❌ Command deployment failed:', error);
      return this.createResult(
        false,
        false,
        0,
        'Deployment error',
        startTime,
        error instanceof Error ? error.message : 'Unknown error',
      );
    }
  }

  /**
   * Build deployment configuration with defaults
   */
  private buildDeploymentConfig(
    options: Partial<CommandDeploymentConfig>,
  ): CommandDeploymentConfig {
    const botConfig = this.config['bot'];

    return {
      deployGlobally: options.deployGlobally ?? !botConfig.devGuildId,
      guildId: options.guildId ?? botConfig.devGuildId,
      cacheTTL: options.cacheTTL ?? this.DEFAULT_CACHE_TTL,
      forceDeployment: options.forceDeployment ?? false,
      skipDeployment: options.skipDeployment ?? false,
    };
  }

  /**
   * Calculate SHA-256 hash of command definitions
   */
  private calculateCommandHash(
    commands: (
      | SlashCommandBuilder
      | SlashCommandOptionsOnlyBuilder
      | SlashCommandSubcommandsOnlyBuilder
    )[],
  ): string {
    // Serialize command data for hashing
    const commandData = commands.map((command) => ({
      name: command.name,
      description: command.description,
      options: command.options,
      // Include any other relevant properties that affect command behavior
      defaultMemberPermissions: command.default_member_permissions,
      dmPermission: command.contexts?.includes(InteractionContextType.BotDM) ?? true,
    }));

    const serialized = JSON.stringify(commandData, null, 0);
    return createHash('sha256').update(serialized).digest('hex');
  }

  /**
   * Check if deployment is needed based on cache
   */
  private async isDeploymentNeeded(currentHash: string): Promise<boolean> {
    try {
      const cachedHash = await this.redis.get(this.REDIS_KEY);

      if (!cachedHash) {
        Logger.debug('📝 No cached hash found, deployment needed');
        return true;
      }

      if (cachedHash !== currentHash) {
        Logger.debug(
          `🔄 Hash mismatch (cached: ${cachedHash.substring(0, 8)}..., current: ${currentHash.substring(0, 8)}...), deployment needed`,
        );
        return true;
      }

      // Check TTL
      const ttl = await this.redis.ttl(this.REDIS_KEY);
      if (ttl <= 0) {
        Logger.debug('⏰ Cache expired, deployment needed');
        return true;
      }

      Logger.debug(`✔ Commands unchanged and cache valid (TTL: ${ttl}s), skipping deployment`);
      return false;
    }
    catch (error) {
      Logger.warn('Redis cache check failed, proceeding with deployment:', error);
      return true; // Fallback to deployment if Redis fails
    }
  }

  /**
   * Deploy commands to Discord API
   */
  private async deployToDiscord(
    commands: (
      | SlashCommandBuilder
      | SlashCommandOptionsOnlyBuilder
      | SlashCommandSubcommandsOnlyBuilder
    )[],
    config: CommandDeploymentConfig,
  ): Promise<boolean> {
    try {
      const botConfig = this.config['bot'];
      const rest = new REST().setToken(botConfig.token);

      const route = config.deployGlobally
        ? Routes.applicationCommands(botConfig.clientId)
        : Routes.applicationGuildCommands(botConfig.clientId, config.guildId!);

      const deploymentType = config.deployGlobally ? 'globally' : `to guild ${config.guildId}`;
      Logger.info(`📡 Deploying ${commands.length} commands ${deploymentType}...`);

      const result = await rest.put(route, { body: commands.map((cmd) => cmd.toJSON()) });

      if (Array.isArray(result) && result.length === commands.length) {
        return true;
      }
      else {
        Logger.error(
          `Deployment mismatch: expected ${commands.length}, got ${Array.isArray(result) ? result.length : 'unknown'}`,
        );
        return false;
      }
    }
    catch (error) {
      Logger.error('❌ Discord API deployment failed:', error);
      return false;
    }
  }

  /**
   * Update command cache with new hash
   */
  private async updateCommandCache(hash: string, ttl: number): Promise<void> {
    try {
      await this.redis.setex(this.REDIS_KEY, ttl, hash);
      Logger.debug(`💾 Updated command cache with hash ${hash.substring(0, 8)}... (TTL: ${ttl}s)`);
    }
    catch (error) {
      Logger.warn('⚠️ Failed to update command cache:', error);
      // Don't throw - deployment was successful even if cache update failed
    }
  }

  /**
   * Create deployment result object
   */
  private createResult(
    success: boolean,
    deployed: boolean,
    commandCount: number,
    reason: string,
    startTime: number,
    error?: string,
  ): CommandDeploymentResult {
    const duration = Date.now() - startTime;

    const result: CommandDeploymentResult = {
      success,
      deployed,
      commandCount,
      reason,
      duration,
    };

    if (error) {
      result.error = error;
    }

    Logger.info(`🏁 Deployment completed: ${success ? '✅' : '❌'} ${reason} (${duration}ms)`);
    return result;
  }

  /**
   * Clear command cache (useful for testing or forced refresh)
   */
  async clearCache(): Promise<void> {
    try {
      await this.redis.del(this.REDIS_KEY);
      Logger.info('🗑️ Command cache cleared');
    }
    catch (error) {
      Logger.warn('⚠️ Failed to clear command cache:', error);
    }
  }

  /**
   * Get current cache status
   */
  async getCacheStatus(): Promise<{ hash?: string; ttl: number; exists: boolean }> {
    try {
      const hash = await this.redis.get(this.REDIS_KEY);
      const ttl = hash ? await this.redis.ttl(this.REDIS_KEY) : -2;

      return {
        hash: hash || undefined,
        ttl,
        exists: !!hash,
      };
    }
    catch (error) {
      Logger.warn('⚠️ Failed to get cache status:', error);
      return { ttl: -2, exists: false };
    }
  }
}
