/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { Redis } from 'ioredis';
import { inject, injectable } from 'inversify';
import { TYPES } from '../shared/types/TYPES.js';
import { Logger } from '../shared/utils/Logger.js';

/**
 * Redis-based Cooldown Service for Clustered Environment
 * 
 * This service provides distributed cooldown tracking that works across
 * all bot cluster processes. It replaces the in-memory cooldown system
 * with Redis-based storage for proper cluster compatibility.
 */
@injectable()
export class RedisCooldownService {
  private readonly COOLDOWN_PREFIX = 'interchat:cooldown';

  constructor(
    @inject(TYPES.RedisClient)
    private readonly redis: Redis,
  ) {}

  /**
   * Generate Redis key for cooldown
   */
  private getCooldownKey(commandName: string, userId: string): string {
    return `${this.COOLDOWN_PREFIX}:${commandName}:${userId}`;
  }

  /**
   * Check if user is on cooldown for a specific command
   * @param commandName The name of the command
   * @param userId The user ID
   * @returns Promise<boolean> true if user can execute command, false if on cooldown
   */
  async checkCooldown(commandName: string, userId: string): Promise<boolean> {
    try {
      const key = this.getCooldownKey(commandName, userId);
      const cooldownEnd = await this.redis.get(key);
      
      if (!cooldownEnd) {
        return true; // No cooldown exists
      }

      const now = Date.now();
      const endTime = parseInt(cooldownEnd, 10);
      
      return now >= endTime; // Can execute if current time >= cooldown end time
    }
    catch (error) {
      Logger.error('Error checking cooldown:', error);
      // On Redis error, allow command execution to prevent blocking
      return true;
    }
  }

  /**
   * Set cooldown for a user and command
   * @param commandName The name of the command
   * @param userId The user ID
   * @param cooldownSeconds The cooldown duration in seconds
   */
  async setCooldown(commandName: string, userId: string, cooldownSeconds: number): Promise<void> {
    try {
      const key = this.getCooldownKey(commandName, userId);
      const cooldownEnd = Date.now() + (cooldownSeconds * 1000);
      
      // Set the key with TTL for automatic cleanup
      await this.redis.set(key, cooldownEnd.toString(), 'EX', cooldownSeconds);
    }
    catch (error) {
      Logger.error('Error setting cooldown:', error);
      // Don't throw error to prevent command execution failure
    }
  }

  /**
   * Get remaining cooldown time for a user and command
   * @param commandName The name of the command
   * @param userId The user ID
   * @returns Promise<number> remaining cooldown time in seconds (0 if no cooldown)
   */
  async getRemainingCooldown(commandName: string, userId: string): Promise<number> {
    try {
      const key = this.getCooldownKey(commandName, userId);
      const cooldownEnd = await this.redis.get(key);
      
      if (!cooldownEnd) {
        return 0; // No cooldown exists
      }

      const now = Date.now();
      const endTime = parseInt(cooldownEnd, 10);
      const remaining = Math.max(0, endTime - now);
      
      return Math.ceil(remaining / 1000); // Return seconds
    }
    catch (error) {
      Logger.error('Error getting remaining cooldown:', error);
      return 0; // On error, return no cooldown
    }
  }

  /**
   * Remove cooldown for a user and command (admin override)
   * @param commandName The name of the command
   * @param userId The user ID
   */
  async removeCooldown(commandName: string, userId: string): Promise<void> {
    try {
      const key = this.getCooldownKey(commandName, userId);
      await this.redis.del(key);
    }
    catch (error) {
      Logger.error('Error removing cooldown:', error);
    }
  }

  /**
   * Clear all cooldowns for a specific command (admin utility)
   * @param commandName The name of the command
   */
  async clearCommandCooldowns(commandName: string): Promise<void> {
    try {
      const pattern = `${this.COOLDOWN_PREFIX}:${commandName}:*`;
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
        Logger.info(`Cleared ${keys.length} cooldowns for command: ${commandName}`);
      }
    }
    catch (error) {
      Logger.error('Error clearing command cooldowns:', error);
    }
  }

  /**
   * Clear all cooldowns for a specific user (admin utility)
   * @param userId The user ID
   */
  async clearUserCooldowns(userId: string): Promise<void> {
    try {
      const pattern = `${this.COOLDOWN_PREFIX}:*:${userId}`;
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
        Logger.info(`Cleared ${keys.length} cooldowns for user: ${userId}`);
      }
    }
    catch (error) {
      Logger.error('Error clearing user cooldowns:', error);
    }
  }

  /**
   * Get cooldown statistics (for monitoring/debugging)
   */
  async getCooldownStats(): Promise<{
    totalCooldowns: number;
    commands: Record<string, number>;
  }> {
    try {
      const pattern = `${this.COOLDOWN_PREFIX}:*`;
      const keys = await this.redis.keys(pattern);
      
      const commands: Record<string, number> = {};
      
      for (const key of keys) {
        const parts = key.split(':');
        if (parts.length >= 3) {
          const commandName = parts[2];
          commands[commandName] = (commands[commandName] || 0) + 1;
        }
      }
      
      return {
        totalCooldowns: keys.length,
        commands,
      };
    }
    catch (error) {
      Logger.error('Error getting cooldown stats:', error);
      return {
        totalCooldowns: 0,
        commands: {},
      };
    }
  }

  /**
   * Health check for Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    }
    catch (error) {
      Logger.error('Redis health check failed:', error);
      return false;
    }
  }
}
