/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { EmbedBuilder, SlashCommandBuilder, SlashCommandOptionsOnlyBuilder } from 'discord.js';
import { injectable } from 'inversify';
import type { CommandContext } from '../../shared/context/CommandContext.js';
import { CommandCategory, CommandMetadata, CommandResult } from './BaseCommandHandler.js';
import { Context } from '../../shared/context/Context.js';

/**
 * Base Context Command Handler
 *
 * Provides common functionality for commands that use the Context system.
 * This allows commands to work as both slash commands and prefix commands.
 */
@injectable()
export abstract class BaseContextCommandHandler {
  abstract readonly metadata: CommandMetadata;

  /**
   * Build the slash command data
   */
  abstract buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder;

  /**
   * Execute the command using the Context system
   */
  abstract execute(ctx: CommandContext): Promise<CommandResult>;

  /**
   * Check if the user has permission to use this command
   */
  async checkPermissions(ctx: Context): Promise<boolean> {
    const { metadata } = this;

    // Check if command is owner only
    if (metadata.ownerOnly) {
      const ownerId = process.env.OWNER_ID;
      if (ctx.user.id !== ownerId) {
        return false;
      }
    }

    // Check if command is guild only
    if (metadata.guildOnly && !ctx.guild) {
      return false;
    }

    // Check specific permissions
    if (metadata.permissions && metadata.permissions.length > 0) {
      if (!ctx.guild || !ctx.member) {
        return false;
      }

      // This would need proper Discord.js permission checking
      // For now, return true (implement permission checking later)
    }

    return true;
  }

  /**
   * Create a standard error embed
   */
  protected createErrorEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor(0xff0000)
      .setTitle(`❌ ${title}`)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Create a standard success embed
   */
  protected createSuccessEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor(0x00ff00)
      .setTitle(`✅ ${title}`)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Create a standard info embed
   */
  protected createInfoEmbed(title: string, description: string): EmbedBuilder {
    return new EmbedBuilder()
      .setColor(0x3498db)
      .setTitle(`ℹ️ ${title}`)
      .setDescription(description)
      .setTimestamp();
  }

  /**
   * Format uptime in a human-readable way
   */
  protected formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    else {
      return `${seconds}s`;
    }
  }
}

export { CommandCategory };
