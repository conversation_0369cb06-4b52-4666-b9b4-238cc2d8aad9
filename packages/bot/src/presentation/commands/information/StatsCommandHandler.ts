/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  SlashCommandBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import { platform, totalmem } from 'node:os';
import { version } from 'node:process';
import type { Context } from '../../../shared/context/index.js';
import { BaseCommandHandler, CommandCategory, CommandResult } from '../BaseCommandHandler.js';

@injectable()
export class StatsCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'stats',
    description: "📊 View InterChat's statistics",
    category: CommandCategory.INFORMATION,
    cooldown: 5, // 5 seconds
    ownerOnly: false,
    guildOnly: false,
    permissions: [],
  };

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<CommandResult> {
    try {
      // Get bot statistics directly (no need for complex architecture here)
      const client = ctx.client;
      const uptime = this.formatUptime(process.uptime() * 1000);

      // System stats
      const memUsed = Math.round(process.memoryUsage().heapUsed / 1024 / 1024);
      const memTotal = Math.round(totalmem() / 1024 / 1024 / 1024);

      // Bot stats
      const serverCount = client.guilds.cache.size;
      const userCount = client.guilds.cache.reduce(
        (acc: number, guild: any) => acc + guild.memberCount,
        0,
      );

      // Create main stats embed
      const embed = new EmbedBuilder()
        .setColor(0x3498db) // Blue color
        .setTitle('🔥 InterChat Statistics')
        .setDescription('Real-time statistics for the InterChat bot')
        .addFields([
          {
            name: '🤖 Bot Stats',
            value: [
              `Servers: **${serverCount.toLocaleString()}**`,
              `Users: **${userCount.toLocaleString()}**`,
              `Uptime: **${uptime}**`,
              `Ping: **${client.ws.ping}ms**`,
            ].join('\n'),
            inline: true,
          },
          {
            name: '⚙️ System Stats',
            value: [
              `Platform: **${platform()}**`,
              `Memory: **${memUsed}MB / ${memTotal}MB**`,
              `Node.js: **${version}**`,
            ].join('\n'),
            inline: true,
          },
          {
            name: '🌐 Cluster Stats',
            value: [
              `Clusters: **${client.cluster?.count || 1}**`,
              `Current Cluster: **${client.cluster?.id || 0}**`,
              `Shards: **${client.cluster?.shardList?.length || 0}**`,
              'Status: **Online**',
            ].join('\n'),
            inline: true,
          },
        ])
        .setFooter({ text: 'Generated' })
        .setTimestamp();

      // Create action buttons
      const buttonsRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setLabel('Invite Bot')
          .setEmoji('➕')
          .setStyle(ButtonStyle.Link)
          .setURL('https://discord.com/application-directory/769921109209907241'),
        new ButtonBuilder()
          .setLabel('Support Server')
          .setEmoji('🆘')
          .setStyle(ButtonStyle.Link)
          .setURL('https://discord.gg/interchat'),
        new ButtonBuilder()
          .setLabel('Dashboard')
          .setEmoji('🎛️')
          .setStyle(ButtonStyle.Link)
          .setURL('https://interchat.fun/dashboard'),
      );

      return {
        success: true,
        embed,
        ephemeral: false,
      };
    }
    catch (error) {
      console.error('Error in stats command:', error);
      await ctx.editReply({
        content: '❌ An error occurred while fetching statistics.',
      });
      return {
        success: false,
        message: 'Error retrieving statistics',
        ephemeral: true,
      };
    }
  }

  /**
   * Format uptime in a human-readable way
   */
  private formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    else {
      return `${seconds}s`;
    }
  }
}

export default StatsCommandHandler;
