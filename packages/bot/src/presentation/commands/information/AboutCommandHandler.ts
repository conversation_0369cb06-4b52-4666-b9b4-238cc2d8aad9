/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  SlashCommandBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import type { Context } from '../../../shared/context/index.js';
import {
  BaseCommandHandler,
  CommandCategory,
  FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

@injectable()
export class About<PERSON>ommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'about',
    description: 'ℹ️ Learn about InterChat and its features',
    category: CommandCategory.INFORMATION,
    cooldown: 5, // 5 seconds
    ownerOnly: false,
    guildOnly: false,
    permissions: [],
  };

  buildCommand(): SlashCommandBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description);
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    try {
      // Get bot information
      const client = ctx.client;
      const uptime = this.formatUptime(client.uptime || 0);

      // Create main about embed
      const embed = new EmbedBuilder()
        .setColor(0x3498db) // Blue color
        .setTitle('🌐 About InterChat')
        .setDescription('Connect Discord servers worldwide through cross-server chat!')
        .setThumbnail(client.user?.displayAvatarURL() || null)
        .addFields([
          {
            name: '🚀 What is InterChat?',
            value: [
              'InterChat is a powerful Discord bot that enables **real-time communication**',
              'between multiple Discord servers through **hubs** and **direct calls**.',
              '',
              '• **Hubs** - Join persistent communities of multiple servers',
              '• **Calls** - Start temporary connections with other servers',
              '• **Moderation** - Advanced safety tools for cross-server chat',
            ].join('\n'),
            inline: false,
          },
          {
            name: '✨ Key Features',
            value: [
              '- 🌐 **Cross-server messaging** with real-time delivery',
              '- 🛡️ **Advanced moderation** and safety filters',
              '- 🏆 **Achievements & leaderboards** for community engagement',
              '- 💎 **Premium features** for enhanced customization',
              '- 📱 **Dashboard** for easy management',
              '- 🌍 **Multi-language support** for global communities',
            ].join('\n'),
            inline: false,
          },
          {
            name: '📊 Current Stats',
            value: [
              `Servers: **${client.guilds.cache.size.toLocaleString()}**`,
              `Uptime: **${uptime}**`,
              `Clusters: **${client.cluster?.count || 1}**`,
              'Version: **5.0.0** (Rewrite)',
            ].join('\n'),
            inline: true,
          },
          {
            name: '🔗 Quick Start',
            value: [
              '1. Use `/setup` for guided setup',
              '2. Browse hubs at [interchat.tech/hubs](https://interchat.tech/hubs)',
              '3. Connect with `/connect hub:HubName`',
              '4. Start chatting across servers!',
            ].join('\n'),
            inline: true,
          },
        ])
        .setFooter({
          text: 'InterChat • Connecting Discord servers worldwide',
          iconURL: client.user?.displayAvatarURL(),
        })
        .setTimestamp();

      // Create action buttons
      const buttonsRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
        new ButtonBuilder()
          .setLabel('Add to Server')
          .setEmoji('➕')
          .setStyle(ButtonStyle.Link)
          .setURL('https://discord.com/application-directory/769921109209907241'),
        new ButtonBuilder()
          .setLabel('Support Server')
          .setEmoji('🆘')
          .setStyle(ButtonStyle.Link)
          .setURL('https://discord.gg/interchat'),
        new ButtonBuilder()
          .setLabel('Dashboard')
          .setEmoji('🎛️')
          .setStyle(ButtonStyle.Link)
          .setURL('https://interchat.fun/dashboard'),
        new ButtonBuilder()
          .setLabel('Vote')
          .setEmoji('⭐')
          .setStyle(ButtonStyle.Link)
          .setURL('https://top.gg/bot/769921109209907241/vote'),
      );

      await ctx.editOrReply({ embeds: [embed], components: [buttonsRow] });
    }
    catch (error) {
      console.error('Error in about command:', error);
      await ctx.editReply({
        content: '❌ An error occurred while displaying information.',
      });
      return {
        success: false,
        message: 'Error displaying about information',
        ephemeral: true,
      };
    }
  }

  /**
   * Format uptime in a human-readable way
   */
  private formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    else {
      return `${seconds}s`;
    }
  }
}

export default AboutCommandHandler;
