/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { EmbedBuilder, SlashCommandBuilder, SlashCommandOptionsOnlyBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { GetHubUseCase } from '../../../application/use-cases/hub/GetHubUseCase.js';
import type { Hub } from '../../../domain/entities/Hub.js';
import {
  type CommandContext,
  isInteractionContext,
} from '../../../shared/context/CommandContext.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { Logger } from '../../../shared/utils/Logger.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

/**
 * Interface for organizing hub info fields
 */
interface HubInfoField {
  name: string;
  value: string;
  inline?: boolean;
}

@injectable()
export default class HubInfoCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'hub-info',
    description: '📊 Show detailed information about a hub',
    category: CommandCategory.HUB,
    cooldown: 5,
    permissions: [],
    guildOnly: false,
    ownerOnly: false,
  };

  constructor(@inject(TYPES.GetHubUseCase) private getHubUseCase: GetHubUseCase) {
    super();

    // Validate that the dependency was injected properly
    if (!this.getHubUseCase) {
      throw new Error('GetHubUseCase dependency was not injected properly');
    }
  }

  buildCommand(): SlashCommandBuilder | SlashCommandOptionsOnlyBuilder {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addStringOption((option) =>
        option
          .setName('name')
          .setDescription('The name of the hub to get information about')
          .setRequired(true),
      );
  }

  async execute(ctx: CommandContext): Promise<CommandResult> {
    try {
      const hubName = this.validateInput(ctx);
      if (!hubName) {
        return this.createErrorResult('❌ **Hub name is required.**');
      }

      // Defer the reply since this operation might take a moment
      await ctx.deferReply({ flags: ['Ephemeral'] });

      const hub = await this.fetchHub(hubName, ctx.user.id);
      if (!hub) {
        return this.createErrorResult("❌ **Hub not found or you don't have access to it.**");
      }

      const embed = this.buildHubInfoEmbed(hub, ctx.user.id);
      return {
        success: true,
        embed,
        ephemeral: true,
      };
    }
    catch (error) {
      Logger.error('Error in hub info command:', error);
      return this.createErrorResult('❌ **An unexpected error occurred.**');
    }
  }

  /**
   * Validate input parameters
   */
  private validateInput(ctx: CommandContext): string | null {
    if (isInteractionContext(ctx)) {
      return ctx.options.getString('name', true);
    }
    else {
      // For prefix commands, get the first argument as the hub name
      return ctx.getString(0);
    }
  }

  /**
   * Create a standardized error result
   */
  private createErrorResult(message: string): CommandResult {
    return {
      success: false,
      message,
      ephemeral: true,
    };
  }

  /**
   * Fetch hub information with proper error handling
   */
  private async fetchHub(hubName: string, userId: string): Promise<Hub | null> {
    try {
      const result = await this.getHubUseCase.execute({ hubName, userId });
      return result.success && result.hub ? result.hub : null;
    }
    catch (error) {
      Logger.error('Error executing GetHubUseCase:', error);
      return null;
    }
  }

  /**
   * Build the hub information embed
   */
  private buildHubInfoEmbed(hub: Hub, userId: string): EmbedBuilder {
    // Create and configure the embed
    const embed = new EmbedBuilder()
      .setTitle(`📊 ${hub.name}`)
      .setDescription(hub.description)
      .setColor(hub.isPrivate ? 0xff9500 : 0x0099ff);

    // Set media elements
    if (hub.iconUrl) embed.setThumbnail(hub.iconUrl);
    if (hub.bannerUrl) embed.setImage(hub.bannerUrl);

    // Organize fields by category for better maintainability
    const basicFields = this.getBasicInfoFields(hub);
    const membershipFields = this.getMembershipFields(hub);
    const securityFields = this.getSecurityFields(hub);
    const ownerFields = this.getOwnerFields(hub, userId);

    // Add all fields to embed
    [...basicFields, ...membershipFields, ...securityFields, ...ownerFields].forEach((field) =>
      embed.addFields(field),
    );

    // Add timestamp and footer
    embed.setFooter({ text: 'Hub Information' }).setTimestamp();

    return embed;
  }

  /**
   * Get basic hub information fields
   */
  private getBasicInfoFields(hub: Hub): HubInfoField[] {
    const fields: HubInfoField[] = [
      {
        name: '🔍 Visibility',
        value: hub.isPrivate ? 'Private' : 'Public',
        inline: true,
      },
      {
        name: '🔞 NSFW',
        value: hub.isNsfw ? 'Yes' : 'No',
        inline: true,
      },
    ];

    return fields;
  }

  /**
   * Get membership-related fields
   */
  private getMembershipFields(hub: Hub): HubInfoField[] {
    const fields: HubInfoField[] = [];

    // Add creation date
    fields.push({
      name: '📅 Created',
      value: this.formatTimestamp(hub.createdAt),
      inline: true,
    });

    return fields;
  }

  /**
   * Get security-related fields
   */
  private getSecurityFields(hub: Hub): HubInfoField[] {
    const fields: HubInfoField[] = [];

    // Appeal cooldown (if set)
    if (hub.appealCooldownHours > 0) {
      fields.push({
        name: '⏳ Appeal Cooldown',
        value: `${hub.appealCooldownHours} hours`,
        inline: true,
      });
    }

    return fields;
  }

  /**
   * Get owner-specific fields
   */
  private getOwnerFields(hub: Hub, userId: string): HubInfoField[] {
    const fields: HubInfoField[] = [];

    // Add owner info
    fields.push({
      name: '👑 Owner',
      value: hub.isOwner(userId) ? 'You' : `<@${hub.ownerId}>`,
      inline: true,
    });

    return fields;
  }

  /**
   * Format timestamps consistently
   */
  formatTimestamp(date: Date): string {
    return `<t:${Math.floor(date.getTime() / 1000)}:R>`;
  }
}
