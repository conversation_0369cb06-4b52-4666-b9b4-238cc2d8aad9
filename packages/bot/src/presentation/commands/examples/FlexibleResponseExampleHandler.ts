/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Flexible Response Example Handler
 *
 * Demonstrates all three response patterns supported by the new architecture:
 * 1. DIRECT: Command handles response directly via ctx.reply() - returns void
 * 2. STRUCTURED: Command returns CommandResult for registry to process
 * 3. ENHANCED: Command returns EnhancedCommandResult with Discord components
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  SlashCommandBuilder,
} from 'discord.js';
import { injectable } from 'inversify';
import type { Context } from '../../../shared/context/Context.js';
import {
  BaseCommandHandler,
  CommandCategory,
  CommandMetadata,
  CommandResult,
  EnhancedCommandResult,
  FlexibleCommandResponse,
} from '../BaseCommandHandler.js';

/**
 * Example Command Handler demonstrating flexible response patterns
 */
@injectable()
export class FlexibleResponseExampleHandler extends BaseCommandHandler {
  readonly metadata: CommandMetadata = {
    name: 'response-example',
    description: 'Demonstrates flexible response patterns',
    category: CommandCategory.UTILITIES,
    cooldown: 5,
    permissions: ['DEVELOPER'],
    guildOnly: false,
    ownerOnly: false,
    premiumOnly: false,
  };

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addSubcommand((subcommand) =>
        subcommand
          .setName('direct')
          .setDescription('Example of DIRECT response pattern'),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('structured')
          .setDescription('Example of STRUCTURED response pattern'),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('enhanced')
          .setDescription('Example of ENHANCED response pattern with components'),
      )
      .addSubcommand((subcommand) =>
        subcommand
          .setName('deferred')
          .setDescription('Example of handling deferred responses'),
      );
  }

  async execute(ctx: Context): Promise<FlexibleCommandResponse> {
    const subcommand = ctx.options.getSubcommand();

    switch (subcommand) {
      case 'direct':
        return await this.handleDirectResponse(ctx);

      case 'structured':
        return await this.handleStructuredResponse(ctx);

      case 'enhanced':
        return await this.handleEnhancedResponse(ctx);

      case 'deferred':
        return await this.handleDeferredResponse(ctx);

      default:
        return {
          success: false,
          embed: this.createErrorEmbed('Invalid Subcommand', 'Unknown subcommand specified.'),
          ephemeral: true,
        };
    }
  }

  /**
   * DIRECT Response Pattern
   * Command handles response directly - returns void
   */
  private async handleDirectResponse(ctx: Context): Promise<void> {
    // Command handles the response directly
    await ctx.reply({
      content: '✅ **DIRECT Response Pattern**\n\n' +
               'This response was handled directly by the command using `ctx.reply()`.\n' +
               'The CommandRegistry will not process any return value.',
      ephemeral: true,
    });

    // Return void - CommandRegistry does nothing
    return;
  }

  /**
   * STRUCTURED Response Pattern
   * Returns CommandResult for registry to process
   */
  private async handleStructuredResponse(_ctx: Context): Promise<CommandResult> {
    // Return structured result for registry to process
    return {
      success: true,
      embed: this.createSuccessEmbed(
        'STRUCTURED Response Pattern',
        'This response was returned as a `CommandResult` object.\n' +
        'The CommandRegistry processed this result and called `interaction.reply()`.',
      ),
      ephemeral: true,
      followUp: 'This is a follow-up message sent after the main response!',
    };
  }

  /**
   * ENHANCED Response Pattern
   * Returns EnhancedCommandResult with Discord components
   */
  private async handleEnhancedResponse(_ctx: Context): Promise<EnhancedCommandResult> {
    // Create Discord components
    const button1 = new ButtonBuilder()
      .setCustomId('example_button_1')
      .setLabel('Click Me!')
      .setStyle(ButtonStyle.Primary)
      .setEmoji('👆');

    const button2 = new ButtonBuilder()
      .setCustomId('example_button_2')
      .setLabel('Or Me!')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji('👈');

    const actionRow = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(button1, button2);

    // Return enhanced result with components
    return {
      success: true,
      embed: this.createInfoEmbed(
        'ENHANCED Response Pattern',
        'This response was returned as an `EnhancedCommandResult` object.\n' +
        'It includes Discord components (buttons) and other advanced features.\n\n' +
        '**Features demonstrated:**\n' +
        '• Discord components (buttons)\n' +
        '• Custom embed styling\n' +
        '• Ephemeral responses\n' +
        '• Component interaction handling',
      ),
      ephemeral: true,
      components: [actionRow],
      allowedMentions: { parse: [] }, // Prevent mentions
      flags: [], // Could include MessageFlags if needed
    };
  }

  /**
   * DEFERRED Response Pattern
   * Shows how to handle deferred responses with flexible patterns
   */
  private async handleDeferredResponse(ctx: Context): Promise<CommandResult> {
    // Defer the response first
    await ctx.deferReply({ flags: ['Ephemeral'] });

    // Simulate some processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Return result - registry will use editReply since interaction is deferred
    return {
      success: true,
      embed: this.createSuccessEmbed(
        'DEFERRED Response Pattern',
        'This response was deferred first, then processed.\n\n' +
        '**Process:**\n' +
        '1. Command called `ctx.deferReply()`\n' +
        '2. Command performed processing (2 second delay)\n' +
        '3. Command returned `CommandResult`\n' +
        '4. Registry detected deferred state and used `editReply()`',
      ),
      ephemeral: true,
    };
  }
}
