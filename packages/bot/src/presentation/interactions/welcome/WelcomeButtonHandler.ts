/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import { ButtonInteraction, EmbedBuilder } from 'discord.js';
import { BaseInteractionHandler } from '../BaseInteractionHandler.js';
import type { Context } from '../../../shared/context/Context.js';

/**
 * Handler for welcome button interactions
 * <PERSON>les getting started buttons, tutorial access, etc.
 */
@injectable()
export default class WelcomeButtonHandler extends BaseInteractionHandler {
  readonly handlerId = 'welcome_btn';

  /**
   * Handle welcome button interactions
   */
  async handleButton(context: Context, interaction: ButtonInteraction): Promise<void> {
    const params = this.extractParams(interaction.customId);
    const action = params[0];

    switch (action) {
      case 'tutorial':
        await this.showTutorial(context, interaction);
        break;
      case 'setup':
        await this.showSetup(context, interaction);
        break;
      case 'support':
        await this.showSupport(context, interaction);
        break;
      default:
        throw new Error(`Unknown welcome action: ${action}`);
    }
  }

  /**
   * Show tutorial information
   */
  private async showTutorial(_context: Context, interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('📚 InterChat Tutorial')
      .setDescription('Welcome to InterChat! Here\'s how to get started...')
      .addFields(
        {
          name: '1️⃣ Create a Hub',
          value: 'Use `/setup` to create your first hub connection.',
          inline: false,
        },
        {
          name: '2️⃣ Connect Channels',
          value: 'Link channels from different servers to create a network.',
          inline: false,
        },
        {
          name: '3️⃣ Start Chatting',
          value: 'Messages sent in connected channels will appear across all servers!',
          inline: false,
        },
      )
      .setColor('#5865F2')
      .setFooter({ text: 'InterChat Tutorial' });

    await interaction.reply({
      embeds: [embed],
      ephemeral: true,
    });
  }

  /**
   * Show setup guidance
   */
  private async showSetup(_context: Context, interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('⚙️ Quick Setup')
      .setDescription('Let\'s set up InterChat for your server!')
      .addFields(
        {
          name: 'Next Steps',
          value: '• Use `/setup` command\n• Follow the setup wizard\n• Configure your first hub',
          inline: false,
        },
      )
      .setColor('#00FF00')
      .setFooter({ text: 'InterChat Setup' });

    await interaction.reply({
      embeds: [embed],
      ephemeral: true,
    });
  }

  /**
   * Show support information
   */
  private async showSupport(_context: Context, interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('🛠️ Need Help?')
      .setDescription('Get support for InterChat')
      .addFields(
        {
          name: 'Support Server',
          value: '[Join our Discord](https://discord.gg/interchat)',
          inline: true,
        },
        {
          name: 'Documentation',
          value: '[Read the Docs](https://docs.interchat.bot)',
          inline: true,
        },
        {
          name: 'Report Issues',
          value: '[GitHub Issues](https://github.com/dev-737/InterChat)',
          inline: true,
        },
      )
      .setColor('#FF6B6B')
      .setFooter({ text: 'InterChat Support' });

    await interaction.reply({
      embeds: [embed],
      ephemeral: true,
    });
  }
}
