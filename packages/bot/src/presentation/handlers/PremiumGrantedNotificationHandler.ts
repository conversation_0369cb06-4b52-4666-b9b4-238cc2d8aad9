/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import type { IEventHandler } from '../../infrastructure/events/TestInterfaces.js';
import { PremiumGrantedEvent } from '../../domain/events/DomainEvents.js';
import { TYPES } from '../../shared/types/TYPES.js';

/**
 * Handles notifications when premium status is granted to a user
 */
@injectable()
export class PremiumGrantedNotificationHandler implements IEventHandler<PremiumGrantedEvent> {
  readonly eventType = 'premium.granted';
  readonly handlerId = 'PremiumGrantedNotificationHandler';

  async handle(event: PremiumGrantedEvent): Promise<void> {
    try {
      // Log the premium grant for debugging
      console.log(`[${this.handlerId}] Processing premium granted event:`, {
        userId: event.userId,
        tier: event.tier,
        expiresAt: event.expiresAt,
        grantedBy: event.grantedBy,
        reason: event.reason,
        clusterId: event.clusterId,
        occurredAt: event.occurredAt,
      });

      // In a real implementation, this would:
      // 1. Send a welcome message to the user
      // 2. Grant Discord roles for premium members
      // 3. Update user's premium status in cache
      // 4. Send premium benefits explanation
      // 5. Log for analytics and monitoring

      const expiryText = event.expiresAt
        ? `until ${event.expiresAt.toISOString()}`
        : 'permanently';

      console.log(`🎉 Premium tier ${event.tier} granted to user ${event.userId} ${expiryText} by ${event.grantedBy}`);
    }
    catch (error) {
      console.error('❌ Error handling premium granted event:', error);
      throw error; // Re-throw to allow for retry mechanisms
    }
  }
}
