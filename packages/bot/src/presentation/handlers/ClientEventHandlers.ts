/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable } from 'inversify';
import type { IEventHandler } from '../../infrastructure/events/TestInterfaces.js';
import {
  ClientReadyEvent,
  ClientShutdownEvent,
  GuildJoinedEvent,
  GuildLeftEvent,
} from '../../domain/events/ClientEvents.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Client Ready Event Handler
 *
 * Handles actions when the Discord client becomes ready
 */
@injectable()
export class ClientReadyEventHandler implements IEventHandler<ClientReadyEvent> {
  readonly eventType = 'client.ready';
  readonly handlerId = 'ClientReadyEventHandler';

  async handle(event: ClientReadyEvent): Promise<void> {
    Logger.info(`🎉 Client ready event received for cluster ${event.clusterId}`);
    Logger.info(`📊 Cluster stats: ${event.guilds} guilds, ${event.users} users, shards: [${event.shards.join(', ')}]`);

    // Here you could:
    // - Update metrics/monitoring
    // - Send notifications to other services
    // - Initialize periodic tasks
    // - Update database with cluster status
  }
}

/**
 * Client Shutdown Event Handler
 *
 * Handles cleanup when the Discord client shuts down
 */
@injectable()
export class ClientShutdownEventHandler implements IEventHandler<ClientShutdownEvent> {
  readonly eventType = 'client.shutdown';
  readonly handlerId = 'ClientShutdownEventHandler';

  async handle(event: ClientShutdownEvent): Promise<void> {
    Logger.info(`🛑 Client shutdown event received for cluster ${event.clusterId}`);
    Logger.info(`📋 Shutdown reason: ${event.reason || 'No reason provided'}`);

    // Here you could:
    // - Clean up resources
    // - Save state to database
    // - Notify monitoring systems
    // - Send alerts if unexpected shutdown
  }
}

/**
 * Guild Joined Event Handler
 *
 * Handles actions when the bot joins a new guild
 */
@injectable()
export class GuildJoinedEventHandler implements IEventHandler<GuildJoinedEvent> {
  readonly eventType = 'guild.joined';
  readonly handlerId = 'GuildJoinedEventHandler';

  async handle(event: GuildJoinedEvent): Promise<void> {
    Logger.info(`🎊 Guild joined: ${event.guildName} (${event.guildId}) with ${event.memberCount} members`);

    // Here you could:
    // - Update guild count metrics
    // - Send welcome message to guild
    // - Log to analytics
    // - Check for premium features
    // - Initialize guild-specific settings
  }
}

/**
 * Guild Left Event Handler
 *
 * Handles cleanup when the bot leaves a guild
 */
@injectable()
export class GuildLeftEventHandler implements IEventHandler<GuildLeftEvent> {
  readonly eventType = 'guild.left';
  readonly handlerId = 'GuildLeftEventHandler';

  async handle(event: GuildLeftEvent): Promise<void> {
    Logger.info(`👋 Guild left: ${event.guildName} (${event.guildId})`);

    // Here you could:
    // - Clean up guild data
    // - Update metrics
    // - Remove guild-specific configurations
    // - Log for analytics
    // - Cancel premium subscriptions if applicable
  }
}
