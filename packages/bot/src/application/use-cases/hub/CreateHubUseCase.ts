/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { inject, injectable } from 'inversify';
import { Hub, type HubCreationData } from '../../../domain/entities/Hub.js';
import type { IHubRepository } from '../../../domain/repositories/HubRepositories.js';
import type { IEventBus } from '../../../infrastructure/events/EventBus.js';
import { TYPES } from '../../../shared/types/TYPES.js';

export interface CreateHubRequest {
  name: string;
  description: string;
  ownerId: string;
  iconUrl?: string;
  bannerUrl?: string;
}

export interface CreateHubResponse {
  success: boolean;
  hub?: Hub;
  error?: string;
}

@injectable()
export class CreateHubUseCase {
  constructor(
    @inject(TYPES.HubRepository) private hubRepository: IHubRepository,
    @inject(TYPES.EventBus) private eventBus: IEventBus,
  ) {}

  async execute(request: CreateHubRequest): Promise<CreateHubResponse> {
    try {
      // Validate request
      const validation = this.validateRequest(request);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // Check if hub name already exists
      const existingHub = await this.hubRepository.findByName(request.name);
      if (existingHub) {
        return {
          success: false,
          error: 'A hub with this name already exists',
        };
      }

      // Check user's hub limit (basic business logic)
      const userHubCount = await this.hubRepository.countByOwnerId(request.ownerId);
      if (userHubCount >= 5) {
        // Configurable limit
        return {
          success: false,
          error: 'You have reached the maximum number of hubs you can create',
        };
      }

      // Create the hub
      const hubCreationData: HubCreationData = {
        name: request.name,
        description: request.description,
        ownerId: request.ownerId,
        iconUrl: request.iconUrl,
        bannerUrl: request.bannerUrl ?? undefined,
      };

      const hub = Hub.create(hubCreationData);

      // Save to repository
      await this.hubRepository.save(hub);

      // Publish domain events
      const events = hub.getDomainEvents();
      for (const event of events) {
        await this.eventBus.publish(event);
      }
      hub.clearDomainEvents();

      return { success: true, hub };
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }

  private validateRequest(request: CreateHubRequest): { isValid: boolean; error?: string } {
    if (!request.name || request.name.trim().length === 0) {
      return { isValid: false, error: 'Hub name is required' };
    }

    if (request.name.length < 3 || request.name.length > 32) {
      return { isValid: false, error: 'Hub name must be between 3 and 32 characters' };
    }

    if (!request.description || request.description.trim().length === 0) {
      return { isValid: false, error: 'Hub description is required' };
    }

    if (request.description.length > 500) {
      return { isValid: false, error: 'Hub description cannot exceed 500 characters' };
    }

    if (!request.ownerId || request.ownerId.trim().length === 0) {
      return { isValid: false, error: 'Owner ID is required' };
    }

    // Validate hub name format (alphanumeric, spaces, hyphens, underscores)
    if (!/^[a-zA-Z0-9\s\-_]+$/.test(request.name)) {
      return {
        isValid: false,
        error: 'Hub name can only contain letters, numbers, spaces, hyphens, and underscores',
      };
    }

    return { isValid: true };
  }
}
