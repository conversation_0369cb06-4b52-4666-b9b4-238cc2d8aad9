/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { IDonationRepository, IUserPremiumRepository } from '../../../domain/repositories/DonationRepositories.js';
import type { DonationDomainService } from '../../../domain/services/DonationDomainService.js';
import type { IEventBus } from '../../../infrastructure/events/TestInterfaces.js';
import { Donation, DonationSource } from '../../../domain/entities/Donation.js';
import { Money } from '../../../domain/value-objects/DonationValueObjects.js';
import { DonationCreatedEvent } from '../../../domain/events/DomainEvents.js';
import type {
  CreateDonationCommand,
  CreateDonationResult,
} from '../../dto/DonationDto.js';

/**
 * Use Case: Create Donation
 *
 * Handles the creation of a new donation, including validation,
 * premium tier calculation, and event publishing.
 */
@injectable()
export class CreateDonationUseCase {
  constructor(
    @inject(TYPES.DonationRepository)
    private readonly donationRepository: IDonationRepository,

    @inject(TYPES.UserPremiumRepository)
    private readonly userPremiumRepository: IUserPremiumRepository,

    @inject(TYPES.DonationDomainService)
    private readonly donationDomainService: DonationDomainService,

    @inject(TYPES.EventBus)
    private readonly eventBus: IEventBus,
  ) {}

  async execute(command: CreateDonationCommand): Promise<CreateDonationResult> {
    try {
      // Validate input
      this.validateCommand(command);

      // Create value objects
      const amount = Money.create(command.amount, command.currency);

      // Create donation entity
      const donation = Donation.create(
        crypto.randomUUID(),
        command.donorId,
        amount,
        DonationSource.OTHER, // or determine appropriate source
        command.kofiTransactionId || crypto.randomUUID(),
        new Date(),
        undefined, // message
        undefined, // donorName
        undefined, // donorEmail
        command.metadata,
      );

      // Calculate premium eligibility using domain service
      const premiumCalculation = await this.donationDomainService.calculatePremiumEligibility(
        command.donorId,
        amount,
      );

      // Save donation
      await this.donationRepository.save(donation);

      // Grant premium if eligible
      let premiumGranted = false;
      let premiumExpiresAt: Date | undefined;

      if (premiumCalculation.eligible) {
        const existingPremium = await this.userPremiumRepository.findByUserId(command.donorId);

        const updatedPremium = await this.donationDomainService.grantOrExtendPremium(
          command.donorId,
          premiumCalculation.tier,
          premiumCalculation.durationMonths,
          existingPremium || undefined,
        );

        await this.userPremiumRepository.save(updatedPremium);
        premiumGranted = true;
        premiumExpiresAt = updatedPremium.expiresAt || undefined;
      }

      // Publish domain event
      const domainEvent = new DonationCreatedEvent(
        donation.id,
        command.donorId,
        amount.amount,
        amount.currency,
        command.tier,
        premiumGranted,
      );

      await this.eventBus.publish(domainEvent);

      return {
        donationId: donation.id,
        success: true,
        premiumGranted,
        premiumExpiresAt,
        message: premiumGranted
          ? `Thank you for your donation! Premium access granted until ${premiumExpiresAt?.toDateString()}`
          : 'Thank you for your donation!',
      };

    }
    catch (error) {
      return {
        donationId: '',
        success: false,
        premiumGranted: false,
        message: `Failed to process donation: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private validateCommand(command: CreateDonationCommand): void {
    if (!command.donorId) {
      throw new Error('Donor ID is required');
    }

    if (!command.amount || command.amount <= 0) {
      throw new Error('Amount must be greater than 0');
    }

    if (!command.currency) {
      throw new Error('Currency is required');
    }

    if (!command.tier || command.tier < 1) {
      throw new Error('Tier must be 1 or greater');
    }

    // Validate currency
    const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
    if (!validCurrencies.includes(command.currency)) {
      throw new Error(`Currency must be one of: ${validCurrencies.join(', ')}`);
    }

    // Validate tier (assuming tiers 1-4)
    if (command.tier > 4) {
      throw new Error('Tier cannot be greater than 4');
    }
  }
}
