/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { IUserPremiumRepository } from '../../../domain/repositories/DonationRepositories.js';
import { DonationTier } from '../../../domain/value-objects/DonationValueObjects.js';
import type {
  GetUserPremiumQuery,
  GetUserPremiumResult,
} from '../../dto/DonationDto.js';

/**
 * Use Case: Get User Premium Status
 *
 * Retrieves the premium status and details for a specific user.
 */
@injectable()
export class GetUserPremiumUseCase {
  constructor(
    @inject(TYPES.UserPremiumRepository)
    private readonly userPremiumRepository: IUserPremiumRepository,
  ) {}

  async execute(query: GetUserPremiumQuery): Promise<GetUserPremiumResult> {
    try {
      const premiumStatus = await this.userPremiumRepository.findByUserId(query.userId);

      if (!premiumStatus) {
        return {
          userId: query.userId,
          isPremium: false,
          tier: 0,
          totalDonated: 0,
          donationCount: 0,
        };
      }

      return {
        userId: query.userId,
        isPremium: premiumStatus.isActive,
        tier: this.getTierNumber(premiumStatus.tier),
        expiresAt: premiumStatus.expiresAt || undefined,
        totalDonated: premiumStatus.totalDonated.amount,
        donationCount: 0, // TODO: Calculate from donations
      };
    }
    catch (error) {
      // Return default non-premium status on error
      return {
        userId: query.userId,
        isPremium: false,
        tier: 0,
        totalDonated: 0,
        donationCount: 0,
      };
    }
  }

  /**
   * Convert DonationTier to a number for API compatibility
   */
  private getTierNumber(tier: DonationTier | null): number {
    if (!tier) return 0;

    // Map tier IDs to numbers
    switch (tier.id) {
      case 'tier-1': return 1;
      case 'tier-2': return 2;
      case 'tier-3': return 3;
      case 'tier-4': return 4;
      case 'kofi-supporter': return 1; // Map Ko-fi supporter to tier 1
      default: return 0;
    }
  }
}
