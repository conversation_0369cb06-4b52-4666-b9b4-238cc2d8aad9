/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Application Layer DTOs for Donation System
 *
 * These DTOs define the data structures used by the application layer
 * to communicate with the presentation layer (Discord commands/interactions).
 */

export interface CreateDonationCommand {
  readonly donorId: string;
  readonly amount: number;
  readonly currency: string;
  readonly tier: number;
  readonly kofiTransactionId?: string;
  readonly metadata?: Record<string, any>;
}

export interface CreateDonationResult {
  readonly donationId: string;
  readonly success: boolean;
  readonly premiumGranted: boolean;
  readonly premiumExpiresAt?: Date;
  readonly message: string;
}

export interface ProcessDonationCommand {
  readonly donationId: string;
  readonly processed: boolean;
  readonly processorId: string;
  readonly notes?: string;
}

export interface ProcessDonationResult {
  readonly success: boolean;
  readonly donation: DonationDto;
  readonly message: string;
}

export interface GetUserPremiumQuery {
  readonly userId: string;
}

export interface GetUserPremiumResult {
  readonly userId: string;
  readonly isPremium: boolean;
  readonly tier: number;
  readonly expiresAt?: Date;
  readonly totalDonated: number;
  readonly donationCount: number;
}

export interface GrantPremiumCommand {
  readonly userId: string;
  readonly tier: number;
  readonly durationMonths: number;
  readonly grantedBy: string;
  readonly reason: string;
}

export interface GrantPremiumResult {
  readonly success: boolean;
  readonly userId: string;
  readonly tier: number;
  readonly expiresAt: Date;
  readonly message: string;
}

export interface DonationDto {
  readonly id: string;
  readonly donorId: string;
  readonly amount: number;
  readonly currency: string;
  readonly tier: number;
  readonly processed: boolean;
  readonly processingDate?: Date;
  readonly processorId?: string;
  readonly kofiTransactionId?: string;
  readonly metadata?: Record<string, any>;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export interface UserPremiumDto {
  readonly userId: string;
  readonly tier: number;
  readonly expiresAt?: Date;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export interface DonationSummaryDto {
  readonly totalAmount: number;
  readonly totalCount: number;
  readonly averageAmount: number;
  readonly topTier: number;
  readonly firstDonation?: Date;
  readonly lastDonation?: Date;
}
