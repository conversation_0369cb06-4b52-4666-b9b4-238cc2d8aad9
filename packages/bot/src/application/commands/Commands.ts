/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Application Commands (CQRS Pattern)
 *
 * Commands represent write operations in the system.
 * They carry all the data needed to perform an operation
 * and are handled by Use Cases.
 */

/**
 * Command to create a new hub
 */
export interface CreateHubCommand {
  readonly guildId: string;
  readonly channelId: string;
  readonly name: string;
  readonly description?: string;
  readonly isPrivate: boolean;
  readonly createdBy: string;
}

/**
 * Command to update guild settings
 */
export interface UpdateGuildSettingsCommand {
  readonly guildId: string;
  readonly settings: {
    readonly prefix?: string;
    readonly language?: string;
    readonly timezone?: string;
    readonly moderationEnabled?: boolean;
  };
  readonly updatedBy: string;
}

/**
 * Command to ban user from hub
 */
export interface BanUserFromHubCommand {
  readonly hubId: string;
  readonly userId: string;
  readonly reason: string;
  readonly bannedBy: string;
  readonly expiresAt?: Date;
}

/**
 * Command to send message to hub
 */
export interface SendMessageToHubCommand {
  readonly hubId: string;
  readonly authorId: string;
  readonly content: string;
  readonly attachments?: string[];
  readonly replyToMessageId?: string;
}
