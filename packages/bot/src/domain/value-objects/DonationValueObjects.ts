/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { BusinessRuleViolationError } from '../../shared/errors/DomainError.js';

/**
 * Money Value Object
 *
 * Represents a monetary amount with currency.
 * Immutable and validates business rules.
 */
export class Money {
  private constructor(
    public readonly amount: number,
    public readonly currency: string,
  ) {
    this.validateAmount(amount);
    this.validateCurrency(currency);
  }

  static create(amount: number, currency: string): Money {
    return new Money(amount, currency);
  }

  static zero(currency: string = 'USD'): Money {
    return new Money(0, currency);
  }

  /**
   * Add another money amount (must be same currency)
   */
  add(other: Money): Money {
    if (this.currency !== other.currency) {
      throw new BusinessRuleViolationError(
        `Cannot add different currencies: ${this.currency} and ${other.currency}`,
      );
    }

    return new Money(this.amount + other.amount, this.currency);
  }

  /**
   * Check if this amount is greater than another
   */
  isGreaterThan(other: Money): boolean {
    this.ensureSameCurrency(other);
    return this.amount > other.amount;
  }

  /**
   * Check if this amount is greater than or equal to another
   */
  isGreaterThanOrEqual(other: Money): boolean {
    this.ensureSameCurrency(other);
    return this.amount >= other.amount;
  }

  /**
   * Check if this amount equals another
   */
  equals(other: Money): boolean {
    return this.amount === other.amount && this.currency === other.currency;
  }

  /**
   * Convert to different currency (requires exchange rate)
   */
  convertTo(targetCurrency: string, exchangeRate: number): Money {
    if (this.currency === targetCurrency) {
      return this;
    }

    const convertedAmount = this.amount * exchangeRate;
    return new Money(convertedAmount, targetCurrency);
  }

  /**
   * Format for display
   */
  format(): string {
    return `${this.amount.toFixed(2)} ${this.currency}`;
  }

  /**
   * Serialize for persistence
   */
  toJSON(): { amount: number; currency: string } {
    return {
      amount: this.amount,
      currency: this.currency,
    };
  }

  /**
   * Deserialize from persistence
   */
  static fromJSON(json: { amount: number; currency: string }): Money {
    return new Money(json.amount, json.currency);
  }

  private validateAmount(amount: number): void {
    if (amount < 0) {
      throw new BusinessRuleViolationError('Amount cannot be negative');
    }

    if (!Number.isFinite(amount)) {
      throw new BusinessRuleViolationError('Amount must be a finite number');
    }

    // Prevent floating point precision issues (limit to 2 decimal places)
    if (Number((amount * 100).toFixed(0)) / 100 !== amount) {
      throw new BusinessRuleViolationError('Amount cannot have more than 2 decimal places');
    }
  }

  private validateCurrency(currency: string): void {
    if (!currency || currency.length !== 3) {
      throw new BusinessRuleViolationError('Currency must be a 3-letter code');
    }

    // Add more currency validation as needed
    const supportedCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];
    if (!supportedCurrencies.includes(currency.toUpperCase())) {
      throw new BusinessRuleViolationError(`Unsupported currency: ${currency}`);
    }
  }

  private ensureSameCurrency(other: Money): void {
    if (this.currency !== other.currency) {
      throw new BusinessRuleViolationError(
        `Cannot compare different currencies: ${this.currency} and ${other.currency}`,
      );
    }
  }
}

/**
 * Donation Tier Value Object
 *
 * Represents a premium subscription tier with its benefits.
 */
export class DonationTier {
  private constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly monthlyAmount: Money,
    public readonly benefits: string[],
    public readonly isActive: boolean = true,
  ) {
    this.validateTier();
  }

  static create(
    id: string,
    name: string,
    monthlyAmount: Money,
    benefits: string[],
    isActive: boolean = true,
  ): DonationTier {
    return new DonationTier(id, name, monthlyAmount, benefits, isActive);
  }

  /**
   * Ko-fi Supporter tier (special predefined tier)
   */
  static createKofiSupporter(): DonationTier {
    return new DonationTier(
      'kofi-supporter',
      'Ko-fi Supporter',
      Money.create(3.00, 'USD'),
      [
        'Premium badge',
        'Early access to features',
        'Priority support',
        'Custom hub names',
      ],
      true,
    );
  }

  /**
   * Check if user qualifies for this tier based on monthly amount
   */
  qualifiesForTier(monthlyAmount: Money): boolean {
    if (!this.isActive) {
      return false;
    }

    return monthlyAmount.isGreaterThanOrEqual(this.monthlyAmount);
  }

  /**
   * Check if this tier has a specific benefit
   */
  hasBenefit(benefit: string): boolean {
    return this.benefits.includes(benefit);
  }

  equals(other: DonationTier): boolean {
    return this.id === other.id;
  }

  private validateTier(): void {
    if (!this.id || this.id.trim().length === 0) {
      throw new BusinessRuleViolationError('Donation tier ID cannot be empty');
    }

    if (!this.name || this.name.trim().length === 0) {
      throw new BusinessRuleViolationError('Donation tier name cannot be empty');
    }

    if (this.benefits.length === 0) {
      throw new BusinessRuleViolationError('Donation tier must have at least one benefit');
    }
  }
}
