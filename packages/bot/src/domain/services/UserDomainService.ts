/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * User Domain Service
 *
 * Handles complex user business logic that doesn't belong in a single entity.
 * Coordinates between User entities and other domain objects.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../shared/types/TYPES.js';
import type { IUserRepository } from '../repositories/UserRepositories.js';
import type { IEventBus } from '../../infrastructure/events/EventBus.js';
import { User, type UserCreationData, type UserUpdateData } from '../entities/User.js';
import { BusinessRuleViolationError } from '../../shared/errors/DomainError.js';
import type { HubActivityLevel } from '../../../../../build/generated/prisma/client/index.js';

export interface UserRegistrationData extends UserCreationData {
  source: 'command' | 'auto' | 'invite';
  guildId?: string;
}

export interface UserBanData {
  userId: string;
  reason: string;
  moderatorId: string;
  duration?: number; // in milliseconds, null for permanent
}

export interface UserReputationChange {
  userId: string;
  change: number;
  reason: string;
  moderatorId?: string;
}

export interface UserEngagementMetrics {
  messageActivity: number;
  hubParticipation: number;
  voteParticipation: number;
  overallScore: number;
}

/**
 * User Domain Service
 *
 * Orchestrates user-related business operations and enforces business rules.
 */
@injectable()
export class UserDomainService {
  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository,
    @inject(TYPES.EventBus)
    private readonly eventBus: IEventBus,
  ) {}

  /**
   * Register a new user in the system
   */
  async registerUser(registrationData: UserRegistrationData): Promise<User> {
    // Check if user already exists
    const existingUser = await this.userRepository.findById(registrationData.id);
    if (existingUser) {
      throw new BusinessRuleViolationError('User already exists');
    }

    // Validate email uniqueness if provided
    if (registrationData.email) {
      const userWithEmail = await this.userRepository.findByEmail(registrationData.email);
      if (userWithEmail) {
        throw new BusinessRuleViolationError('Email already in use');
      }
    }

    // Create new user
    const user = User.create(registrationData);

    // Save user
    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }

    return user;
  }

  /**
   * Update user information
   */
  async updateUser(userId: string, updateData: UserUpdateData): Promise<User> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    // Validate email uniqueness if being changed
    if (updateData.locale && updateData.locale !== user.preferences.locale) {
      // Additional validation for locale changes could go here
    }

    user.update(updateData);

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }

    return user;
  }

  /**
   * Ban a user from the system
   */
  async banUser(banData: UserBanData): Promise<void> {
    const user = await this.userRepository.findById(banData.userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    const moderator = await this.userRepository.findById(banData.moderatorId);
    if (!moderator || !moderator.isStaff) {
      throw new BusinessRuleViolationError('Only staff members can ban users');
    }

    if (user.isStaff) {
      throw new BusinessRuleViolationError('Cannot ban staff members');
    }

    user.ban(banData.reason, banData.moderatorId);

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Unban a user
   */
  async unbanUser(userId: string, moderatorId: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    const moderator = await this.userRepository.findById(moderatorId);
    if (!moderator || !moderator.isStaff) {
      throw new BusinessRuleViolationError('Only staff members can unban users');
    }

    user.unban(moderatorId);

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Update user reputation
   */
  async updateUserReputation(reputationChange: UserReputationChange): Promise<void> {
    const user = await this.userRepository.findById(reputationChange.userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    // Validate moderator if provided
    if (reputationChange.moderatorId) {
      const moderator = await this.userRepository.findById(reputationChange.moderatorId);
      if (!moderator || !moderator.isStaff) {
        throw new BusinessRuleViolationError('Only staff members can manually adjust reputation');
      }
    }

    user.updateReputation(reputationChange.change, reputationChange.reason);

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Record user vote
   */
  async recordUserVote(userId: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    if (user.isBanned) {
      throw new BusinessRuleViolationError('Banned users cannot vote');
    }

    user.recordVote();

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Record user message activity
   */
  async recordUserMessage(userId: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      // Auto-create user if they don't exist
      const newUser = User.create({ id: userId });
      newUser.recordMessageActivity();

      await this.userRepository.save(newUser);

      // Publish domain events
      const events = newUser.getDomainEvents();
      for (const event of events) {
        await this.eventBus.publish(event);
      }
      return;
    }

    user.recordMessageActivity();

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Record user hub join activity
   */
  async recordHubJoin(userId: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    user.recordHubJoin();

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Calculate user engagement metrics
   */
  async calculateUserEngagement(userId: string): Promise<UserEngagementMetrics> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    const activity = user.activity;

    // Calculate engagement metrics
    const messageActivity = Math.min(activity.messageCount / 100, 1.0); // Normalize to 0-1
    const hubParticipation = Math.min(activity.hubJoinCount / 10, 1.0); // Normalize to 0-1
    const voteParticipation = Math.min(activity.voteCount / 30, 1.0); // Normalize to 0-1

    const overallScore = (messageActivity * 0.4) + (hubParticipation * 0.3) + (voteParticipation * 0.3);

    // Update user's engagement score
    user.updateHubEngagementScore(overallScore);
    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }

    return {
      messageActivity,
      hubParticipation,
      voteParticipation,
      overallScore,
    };
  }

  /**
   * Grant staff privileges to user
   */
  async grantStaffPrivileges(userId: string, grantedBy: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    const grantor = await this.userRepository.findById(grantedBy);
    if (!grantor || !grantor.isStaff) {
      throw new BusinessRuleViolationError('Only staff members can grant staff privileges');
    }

    user.grantStaffPrivileges(grantedBy);

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Revoke staff privileges from user
   */
  async revokeStaffPrivileges(userId: string, revokedBy: string): Promise<void> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    const revoker = await this.userRepository.findById(revokedBy);
    if (!revoker || !revoker.isStaff) {
      throw new BusinessRuleViolationError('Only staff members can revoke staff privileges');
    }

    user.revokeStaffPrivileges(revokedBy);

    await this.userRepository.save(user);

    // Publish domain events
    const events = user.getDomainEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }

  /**
   * Get user recommendations based on preferences
   */
  async getUserRecommendations(userId: string): Promise<{
    recommendedLanguages: string[];
    recommendedActivityLevel: HubActivityLevel | null;
    recommendedHubs: string[];
  }> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BusinessRuleViolationError('User not found');
    }

    const preferences = user.preferences;
    const activity = user.activity;

    // Simple recommendation logic based on user preferences and activity
    const recommendedLanguages = preferences.preferredLanguages.length > 0
      ? preferences.preferredLanguages
      : ['en']; // Default to English

    const recommendedActivityLevel = preferences.activityLevel ||
      (activity.hubEngagementScore > 0.7 ? 'HIGH' :
        activity.hubEngagementScore > 0.3 ? 'MEDIUM' : 'LOW') as HubActivityLevel;

    // This would typically involve more complex logic and database queries
    const recommendedHubs: string[] = [];

    return {
      recommendedLanguages,
      recommendedActivityLevel,
      recommendedHubs,
    };
  }
}
