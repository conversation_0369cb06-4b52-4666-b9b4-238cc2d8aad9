/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * User Repository Interfaces
 *
 * Defines the contracts for user persistence operations.
 * Implementations should handle data persistence concerns while
 * the domain layer focuses on business logic.
 */

import { User } from '../entities/User.js';
import type { HubActivityLevel } from '../../../../../build/generated/prisma/client/index.js';

/**
 * User Repository Interface
 *
 * Manages user persistence operations.
 */
export interface IUserRepository {
  /**
   * Save a user to persistent storage
   */
  save(user: User): Promise<void>;

  /**
   * Find a user by ID
   */
  findById(id: string): Promise<User | null>;

  /**
   * Find a user by email
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Find users by staff status
   */
  findByStaffStatus(isStaff: boolean): Promise<User[]>;

  /**
   * Find users by ban status
   */
  findBannedUsers(): Promise<User[]>;

  /**
   * Find users by activity level preference
   */
  findByActivityLevel(activityLevel: HubActivityLevel): Promise<User[]>;

  /**
   * Find users by preferred languages
   */
  findByPreferredLanguages(languages: string[]): Promise<User[]>;

  /**
   * Find users with high engagement scores
   */
  findHighEngagementUsers(minScore: number, limit?: number): Promise<User[]>;

  /**
   * Find users who voted recently
   */
  findRecentVoters(days: number): Promise<User[]>;

  /**
   * Find users by reputation range
   */
  findByReputationRange(minReputation: number, maxReputation?: number): Promise<User[]>;

  /**
   * Find users who joined hubs recently
   */
  findRecentHubJoiners(days: number): Promise<User[]>;

  /**
   * Search users by name
   */
  searchByName(nameQuery: string, limit?: number): Promise<User[]>;

  /**
   * Get user statistics
   */
  getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    staffUsers: number;
    bannedUsers: number;
    premiumUsers: number;
  }>;

  /**
   * Get users with expiring premium status
   */
  findUsersWithExpiringPremium(days: number): Promise<User[]>;

  /**
   * Find users by locale
   */
  findByLocale(locale: string): Promise<User[]>;

  /**
   * Find users who haven't been active recently
   */
  findInactiveUsers(days: number): Promise<User[]>;

  /**
   * Get top users by message count
   */
  getTopMessageSenders(limit?: number): Promise<User[]>;

  /**
   * Get top users by reputation
   */
  getTopReputationUsers(limit?: number): Promise<User[]>;

  /**
   * Get top voters
   */
  getTopVoters(limit?: number): Promise<User[]>;

  /**
   * Check if user exists
   */
  exists(id: string): Promise<boolean>;

  /**
   * Delete a user (for GDPR compliance)
   */
  delete(id: string): Promise<void>;

  /**
   * Get users created within date range
   */
  findByCreationDateRange(startDate: Date, endDate: Date): Promise<User[]>;

  /**
   * Update user's last activity timestamp
   */
  updateLastActivity(userId: string): Promise<void>;

  /**
   * Bulk update users
   */
  bulkUpdate(updates: Array<{ userId: string; data: Partial<any> }>): Promise<void>;

  /**
   * Get user count by date range
   */
  getUserCountByDateRange(startDate: Date, endDate: Date): Promise<number>;

  /**
   * Find users with specific preferences
   */
  findByPreferences(preferences: {
    showBadges?: boolean;
    mentionOnReply?: boolean;
    showNsfwHubs?: boolean;
  }): Promise<User[]>;
}
