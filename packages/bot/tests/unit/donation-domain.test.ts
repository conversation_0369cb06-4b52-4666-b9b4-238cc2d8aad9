/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Money, DonationTier } from '../../src/domain/value-objects/DonationValueObjects.js';
import {
  Donation,
  UserPremiumStatus,
  DonationSource,
  DonationStatus
} from '../../src/domain/entities/Donation.js';
import { BusinessRuleViolationError } from '../../src/shared/errors/DomainError.js';

describe('Donation Domain Tests', () => {
  describe('Money Value Object', () => {
    it('should create money with valid amount and currency', () => {
      const money = Money.create(10.50, 'USD');

      expect(money.amount).toBe(10.50);
      expect(money.currency).toBe('USD');
    });

    it('should reject negative amounts', () => {
      expect(() => Money.create(-5, 'USD')).toThrow(BusinessRuleViolationError);
    });

    it('should reject amounts with more than 2 decimal places', () => {
      expect(() => Money.create(10.123, 'USD')).toThrow(BusinessRuleViolationError);
    });

    it('should add money of same currency', () => {
      const money1 = Money.create(10, 'USD');
      const money2 = Money.create(5, 'USD');

      const result = money1.add(money2);

      expect(result.amount).toBe(15);
      expect(result.currency).toBe('USD');
    });

    it('should reject adding different currencies', () => {
      const usd = Money.create(10, 'USD');
      const eur = Money.create(5, 'EUR');

      expect(() => usd.add(eur)).toThrow(BusinessRuleViolationError);
    });

    it('should compare amounts correctly', () => {
      const money1 = Money.create(10, 'USD');
      const money2 = Money.create(5, 'USD');
      const money3 = Money.create(10, 'USD');

      expect(money1.isGreaterThan(money2)).toBe(true);
      expect(money2.isGreaterThan(money1)).toBe(false);
      expect(money1.equals(money3)).toBe(true);
    });

    it('should format money correctly', () => {
      const money = Money.create(10.5, 'USD');
      expect(money.format()).toBe('10.50 USD');
    });
  });

  describe('DonationTier Value Object', () => {
    it('should create Ko-fi Supporter tier', () => {
      const tier = DonationTier.createKofiSupporter();

      expect(tier.id).toBe('kofi-supporter');
      expect(tier.name).toBe('Ko-fi Supporter');
      expect(tier.monthlyAmount.amount).toBe(3.00);
      expect(tier.benefits.length).toBeGreaterThan(0);
    });

    it('should check tier qualification correctly', () => {
      const tier = DonationTier.createKofiSupporter();
      const qualifyingAmount = Money.create(3.00, 'USD');
      const nonQualifyingAmount = Money.create(2.99, 'USD');

      expect(tier.qualifiesForTier(qualifyingAmount)).toBe(true);
      expect(tier.qualifiesForTier(nonQualifyingAmount)).toBe(false);
    });

    it('should check benefits correctly', () => {
      const tier = DonationTier.createKofiSupporter();

      expect(tier.hasBenefit('Premium badge')).toBe(true);
      expect(tier.hasBenefit('Nonexistent benefit')).toBe(false);
    });
  });

  describe('Donation Entity', () => {
    let donation: Donation;

    beforeEach(() => {
      donation = Donation.create(
        'donation-123',
        'user-456',
        Money.create(5.00, 'USD'),
        DonationSource.KOFI,
        'kofi-txn-789',
        new Date(),
        'Thank you!',
        'John Doe',
        '<EMAIL>'
      );
    });

    it('should create donation with pending status', () => {
      expect(donation.id).toBe('donation-123');
      expect(donation.userId).toBe('user-456');
      expect(donation.amount.amount).toBe(5.00);
      expect(donation.status).toBe(DonationStatus.PENDING);
    });

    it('should emit DonationCreatedEvent on creation', () => {
      const events = donation.getDomainEvents();

      expect(events).toHaveLength(1);
      expect(events[0].type).toBe('donation.created');
    });

    it('should mark as completed and emit event', () => {
      donation.markAsCompleted();

      expect(donation.status).toBe(DonationStatus.COMPLETED);

      const events = donation.getDomainEvents();
      expect(events.some(e => e.type === 'donation.completed')).toBe(true);
    });

    it('should not allow completing already completed donation', () => {
      donation.markAsCompleted();

      expect(() => donation.markAsCompleted()).toThrow(BusinessRuleViolationError);
    });

    it('should allow failing pending donation', () => {
      donation.markAsFailed('Payment failed');

      expect(donation.status).toBe(DonationStatus.FAILED);
    });

    it('should not allow failing completed donation', () => {
      donation.markAsCompleted();

      expect(() => donation.markAsFailed()).toThrow(BusinessRuleViolationError);
    });

    it('should allow refunding completed donation', () => {
      donation.markAsCompleted();
      donation.refund('Requested by user');

      expect(donation.status).toBe(DonationStatus.REFUNDED);
    });

    it('should not allow refunding non-completed donation', () => {
      expect(() => donation.refund()).toThrow(BusinessRuleViolationError);
    });

    it('should check tier qualification correctly', () => {
      const tier = DonationTier.createKofiSupporter();

      // Pending donation doesn't qualify
      expect(donation.qualifiesForTier(tier)).toBe(false);

      // Completed donation qualifies if amount is sufficient
      donation.markAsCompleted();
      expect(donation.qualifiesForTier(tier)).toBe(true);
    });
  });

  describe('UserPremiumStatus Entity', () => {
    let premiumStatus: UserPremiumStatus;
    let donation: Donation;

    beforeEach(() => {
      premiumStatus = UserPremiumStatus.create('user-123');
      donation = Donation.create(
        'donation-456',
        'user-123',
        Money.create(5.00, 'USD'),
        DonationSource.KOFI,
        'kofi-txn-789'
      );
      donation.markAsCompleted();
    });

    it('should create with no premium status', () => {
      expect(premiumStatus.userId).toBe('user-123');
      expect(premiumStatus.isActive).toBe(false);
      expect(premiumStatus.tier).toBeNull();
      expect(premiumStatus.totalDonated.amount).toBe(0);
    });

    it('should process donation and grant premium tier', () => {
      premiumStatus.processDonation(donation);

      expect(premiumStatus.isActive).toBe(true);
      expect(premiumStatus.tier?.name).toBe('Ko-fi Supporter');
      expect(premiumStatus.totalDonated.amount).toBe(5.00);
    });

    it('should emit events when processing donation', () => {
      premiumStatus.processDonation(donation);

      const events = premiumStatus.getDomainEvents();
      expect(events.some(e => e.type === 'user.donation.processed')).toBe(true);
      expect(events.some(e => e.type === 'user.premium.status.changed')).toBe(true);
    });

    it('should revoke premium status', () => {
      premiumStatus.processDonation(donation);

      expect(premiumStatus.isActive).toBe(true);

      premiumStatus.revokePremiumStatus('Test revocation');

      expect(premiumStatus.isActive).toBe(false);
    });

    it('should check benefits correctly', () => {
      premiumStatus.processDonation(donation);

      expect(premiumStatus.hasBenefit('Premium badge')).toBe(true);
      expect(premiumStatus.hasBenefit('Nonexistent benefit')).toBe(false);
    });

    it('should handle expiry dates correctly', () => {
      const tier = DonationTier.createKofiSupporter();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      premiumStatus.grantPremiumTier(tier, futureDate);

      expect(premiumStatus.isActive).toBe(true);
      expect(premiumStatus.expiresAt).toEqual(futureDate);
    });

    it('should not be active after expiry', () => {
      const tier = DonationTier.createKofiSupporter();
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      premiumStatus.grantPremiumTier(tier, pastDate);

      expect(premiumStatus.isActive).toBe(false);
    });
  });
});
