/**
 * Repository Infrastructure Tests
 *
 * Tests for the donation repository implementations
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Container } from 'inversify';
import 'reflect-metadata';
import { TYPES } from '../../src/shared/types/TYPES.js';

// Test the repositories without complex domain dependencies first
describe('Repository Infrastructure', () => {
  let container: Container;

  beforeEach(() => {
    container = new Container();
  });

  it('should create container without crashing', () => {
    expect(container).toBeDefined();
  });

  it('should be able to import repository classes', async () => {
    // Test that repository classes can be imported
    const { DonationRepository } = await import('../../src/infrastructure/database/repositories/DonationRepository.js');
    const { UserPremiumRepository } = await import('../../src/infrastructure/database/repositories/UserPremiumRepository.js');

    expect(DonationRepository).toBeDefined();
    expect(UserPremiumRepository).toBeDefined();
    expect(typeof DonationRepository).toBe('function');
    expect(typeof UserPremiumRepository).toBe('function');
  });
});
