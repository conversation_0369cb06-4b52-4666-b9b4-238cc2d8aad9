/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { describe, it, expect, beforeEach, afterEach, beforeAll } from 'vitest';
import { createClusterContainer } from '../../src/infrastructure/di/Container.js';
import { TYPES } from '../../src/shared/types/TYPES.js';
import { CreateDonationUseCase } from '../../src/application/use-cases/donations/CreateDonationUseCase.js';
import { GetUserPremiumUseCase } from '../../src/application/use-cases/donations/GetUserPremiumUseCase.js';
import type { Container } from 'inversify';

// Set up test environment variables
beforeAll(() => {
  process.env.BOT_TOKEN = 'test_token_123';
  process.env.CLIENT_ID = 'test_client_123';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
  process.env.REDIS_URL = 'redis://localhost:6379';
  process.env.CLUSTER_ID = 'test-cluster';
  process.env.NODE_ENV = 'development';
});

describe('Application Layer Integration Tests', () => {
  let container: Container;
  let createDonationUseCase: CreateDonationUseCase;
  let getUserPremiumUseCase: GetUserPremiumUseCase;

  beforeEach(async () => {
    // Create a fresh container for each test
    container = await createClusterContainer('test-cluster');
    createDonationUseCase = container.get<CreateDonationUseCase>(TYPES.CreateDonationUseCase);
    getUserPremiumUseCase = container.get<GetUserPremiumUseCase>(TYPES.GetUserPremiumUseCase);
  });

  afterEach(() => {
    // Cleanup
    container.unbindAll();
  });

  describe('Donation Use Cases', () => {
    it('should create donation use case from container', () => {
      expect(createDonationUseCase).toBeDefined();
      expect(createDonationUseCase).toBeInstanceOf(CreateDonationUseCase);
    });

    it('should get user premium use case from container', () => {
      expect(getUserPremiumUseCase).toBeDefined();
      expect(getUserPremiumUseCase).toBeInstanceOf(GetUserPremiumUseCase);
    });

    it('should get non-premium user status', async () => {
      const result = await getUserPremiumUseCase.execute({
        userId: 'non-existent-user-123'
      });

      expect(result.userId).toBe('non-existent-user-123');
      expect(result.isPremium).toBe(false);
      expect(result.tier).toBe(0);
      expect(result.totalDonated).toBe(0);
      expect(result.donationCount).toBe(0);
    });

    it('should handle donation creation validation errors', async () => {
      const result = await createDonationUseCase.execute({
        donorId: '', // Invalid: empty donor ID
        amount: 10,
        currency: 'USD',
        tier: 1
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('Donor ID is required');
    });

    it('should handle invalid donation amount', async () => {
      const result = await createDonationUseCase.execute({
        donorId: 'user-123',
        amount: -5, // Invalid: negative amount
        currency: 'USD',
        tier: 1
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('Amount must be greater than 0');
    });

    it('should handle invalid currency', async () => {
      const result = await createDonationUseCase.execute({
        donorId: 'user-123',
        amount: 10,
        currency: 'XYZ', // Invalid currency
        tier: 1
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('Currency must be one of');
    });

    it('should handle invalid tier', async () => {
      const result = await createDonationUseCase.execute({
        donorId: 'user-123',
        amount: 10,
        currency: 'USD',
        tier: 99 // Invalid tier
      });

      expect(result.success).toBe(false);
      expect(result.message).toContain('Tier cannot be greater than 4');
    });
  });

  describe('Cross-Layer Integration', () => {
    it('should have all dependencies wired correctly', () => {
      // Test that all required dependencies are bound
      expect(() => container.get(TYPES.EventBus)).not.toThrow();
      expect(() => container.get(TYPES.Configuration)).not.toThrow();
      expect(() => container.get(TYPES.DonationRepository)).not.toThrow();
      expect(() => container.get(TYPES.UserPremiumRepository)).not.toThrow();
      expect(() => container.get(TYPES.DonationDomainService)).not.toThrow();
      expect(() => container.get(TYPES.CreateDonationUseCase)).not.toThrow();
      expect(() => container.get(TYPES.GetUserPremiumUseCase)).not.toThrow();
    });

    it('should maintain proper dependency hierarchy', () => {
      // Verify that use cases have their dependencies injected
      const useCase = container.get<CreateDonationUseCase>(TYPES.CreateDonationUseCase);
      expect(useCase).toBeDefined();

      // The use case should work (dependencies are injected)
      expect(async () => {
        await useCase.execute({
          donorId: 'test-user',
          amount: 10,
          currency: 'USD',
          tier: 1
        });
      }).not.toThrow();
    });
  });
});
