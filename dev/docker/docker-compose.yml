services:
  safe-content-ai:
    ports:
      - 8000:8000
    image: steelcityamir/safe-content-ai:latest

  # redis with multithread support
  keydb:
    image: eqalpha/keydb
    ports:
      - 127.0.0.1:6379:6379

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ../../prometheus.yml:/etc/prometheus/prometheus.yml:ro  # Updated path to point to file in parent directory
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"

