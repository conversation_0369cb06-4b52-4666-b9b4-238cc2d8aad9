{
  "Define an InterChat Command": {
    "scope": "javascript,typescript",
    "prefix": ["command", "discord command"],
    "body": [
      "import BaseCommand from '#src/core/BaseCommand.js';",
      "import Context from '#src/core/CommandContext/Context.js';",

      "export default class $1 extends BaseCommand {",
      "\tconstructor() {",
      "\t\tsuper({",
      "\t\t\tname: '$2',",
      "\t\t\tdescription: '$3',",
      "\t\t});",
      "\t}",

      "\tasync execute(ctx: Context) {",
      "\t\t$4",
      "\t}",
      "}",
    ],
    "description": "Create a slash command with a name and description.",
  },
}
