import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    include: ['packages/bot/tests/**/*.test.ts'],
    exclude: ['node_modules', 'build', 'dist'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'build/',
        'dist/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/',
      ],
    },
  },
  resolve: {
    alias: {
      '#src': resolve(__dirname, 'build'),
      '#tests': resolve(__dirname, 'packages/bot/tests'),
    },
  },
  esbuild: {
    target: 'node18',
  },
});
